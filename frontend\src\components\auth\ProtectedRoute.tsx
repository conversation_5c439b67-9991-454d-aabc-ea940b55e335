/**
 * Protected Route Component
 * Wraps components that require authentication
 */

'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireAuth?: boolean;
  requiredRole?: 'admin' | 'manager' | 'employee';
  redirectTo?: string;
  fallback?: React.ReactNode;
}

/**
 * Protected route component that handles authentication and authorization
 * @param children - Components to render if user is authorized
 * @param requireAuth - Whether authentication is required (default: true)
 * @param requiredRole - Minimum role required to access the route
 * @param redirectTo - URL to redirect to if not authorized
 * @param fallback - Component to show while loading
 */
export default function ProtectedRoute({
  children,
  requireAuth = true,
  requiredRole,
  redirectTo = '/auth/login',
  fallback
}: ProtectedRouteProps) {
  const router = useRouter();
  const { user, loading, isAuthenticated } = useAuth();

  useEffect(() => {
    if (loading) return; // Wait for auth state to be determined

    // If authentication is required but user is not authenticated
    if (requireAuth && !isAuthenticated) {
      router.push(redirectTo);
      return;
    }

    // If specific role is required, check user role
    if (requiredRole && user && !hasRequiredRole(user.role, requiredRole)) {
      router.push('/unauthorized');
      return;
    }
  }, [loading, isAuthenticated, user, requireAuth, requiredRole, router, redirectTo]);

  /**
   * Check if user has required role
   * @param userRole - Current user's role
   * @param requiredRole - Required role
   * @returns True if user has sufficient role
   */
  const hasRequiredRole = (
    userRole: 'admin' | 'manager' | 'employee',
    requiredRole: 'admin' | 'manager' | 'employee'
  ): boolean => {
    const roleHierarchy = {
      admin: 3,
      manager: 2,
      employee: 1,
    };

    return roleHierarchy[userRole] >= roleHierarchy[requiredRole];
  };

  // Show loading state
  if (loading) {
    return fallback || <LoadingSpinner />;
  }

  // Show unauthorized if auth is required but user is not authenticated
  if (requireAuth && !isAuthenticated) {
    return null; // Router will handle redirect
  }

  // Show unauthorized if role is required but user doesn't have it
  if (requiredRole && user && !hasRequiredRole(user.role, requiredRole)) {
    return null; // Router will handle redirect
  }

  // Render children if all checks pass
  return <>{children}</>;
}

/**
 * Loading spinner component
 */
function LoadingSpinner() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="flex flex-col items-center">
        <svg
          className="animate-spin -ml-1 mr-3 h-8 w-8 text-indigo-600"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            className="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            strokeWidth="4"
          ></circle>
          <path
            className="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          ></path>
        </svg>
        <p className="mt-2 text-sm text-gray-600">Loading...</p>
      </div>
    </div>
  );
}

/**
 * Higher-order component for protecting pages
 * @param Component - Component to protect
 * @param options - Protection options
 * @returns Protected component
 */
export function withAuth<P extends object>(
  Component: React.ComponentType<P>,
  options: Omit<ProtectedRouteProps, 'children'> = {}
) {
  const ProtectedComponent = (props: P) => (
    <ProtectedRoute {...options}>
      <Component {...props} />
    </ProtectedRoute>
  );

  ProtectedComponent.displayName = `withAuth(${Component.displayName || Component.name})`;
  
  return ProtectedComponent;
}