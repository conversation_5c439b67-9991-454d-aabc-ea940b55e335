/**
 * Unit tests for TimeLogs component
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import TimeLogs from '@/components/time/TimeLogs';
import { AuthProvider } from '@/contexts/AuthContext';

// Mock the auth API
jest.mock('@/lib/auth', () => ({
  apiRequest: jest.fn(),
}));

// Mock the auth context
const mockUser = {
  id: 1,
  firstName: 'John',
  lastName: 'Doe',
  email: '<EMAIL>',
  role: 'employee',
  isActive: true,
};

const MockAuthProvider = ({ children }: { children: React.ReactNode }) => {
  return (
    <AuthProvider>
      {children}
    </AuthProvider>
  );
};

// Mock useAuth hook
jest.mock('@/contexts/AuthContext', () => ({
  ...jest.requireActual('@/contexts/AuthContext'),
  useAuth: () => ({
    user: mockUser,
    isAuthenticated: true,
    login: jest.fn(),
    logout: jest.fn(),
    refreshToken: jest.fn(),
  }),
}));

const { apiRequest } = require('@/lib/auth');

describe('TimeLogs Component', () => {
  const mockTimeLogs = [
    {
      id: 1,
      user_id: 1,
      clock_in_time: '2024-01-15T08:00:00Z',
      clock_out_time: '2024-01-15T17:00:00Z',
      total_hours: '9.00',
      break_minutes: 60,
      overtime_minutes: 60,
      clock_in_location: 'Office',
      clock_out_location: 'Office',
      notes: 'Regular workday',
      status: 'completed',
      created_at: '2024-01-15T08:00:00Z',
      updated_at: '2024-01-15T17:00:00Z',
    },
    {
      id: 2,
      user_id: 1,
      clock_in_time: '2024-01-16T08:30:00Z',
      clock_out_time: null,
      total_hours: null,
      break_minutes: 0,
      overtime_minutes: 0,
      clock_in_location: 'Remote',
      clock_out_location: null,
      notes: null,
      status: 'active',
      created_at: '2024-01-16T08:30:00Z',
      updated_at: '2024-01-16T08:30:00Z',
    },
  ];

  const mockTimeLogsResponse = {
    logs: mockTimeLogs,
    totalCount: 2,
    totalPages: 1,
    currentPage: 1,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    // Mock successful API responses by default
    apiRequest.mockResolvedValue({
      success: true,
      data: mockTimeLogsResponse,
    });
  });

  /**
   * Test: Component renders without crashing
   */
  it('renders without crashing', async () => {
    render(
      <MockAuthProvider>
        <TimeLogs />
      </MockAuthProvider>
    );

    expect(screen.getByText('Time Logs')).toBeInTheDocument();
    expect(screen.getByText('View and manage your time entries')).toBeInTheDocument();
  });

  /**
   * Test: Displays time logs correctly
   */
  it('displays time logs in table format', async () => {
    render(
      <MockAuthProvider>
        <TimeLogs />
      </MockAuthProvider>
    );

    // Wait for data to load
    await waitFor(() => {
      expect(screen.getByText('Jan 15, 2024')).toBeInTheDocument();
      expect(screen.getByText('9.00')).toBeInTheDocument();
      expect(screen.getByText('Office')).toBeInTheDocument();
      expect(screen.getByText('Completed')).toBeInTheDocument();
    });

    // Check for active log
    expect(screen.getByText('Jan 16, 2024')).toBeInTheDocument();
    expect(screen.getByText('Remote')).toBeInTheDocument();
    expect(screen.getByText('Active')).toBeInTheDocument();
  });

  /**
   * Test: Filter functionality works
   */
  it('applies filters when form is submitted', async () => {
    render(
      <MockAuthProvider>
        <TimeLogs />
      </MockAuthProvider>
    );

    // Wait for initial load
    await waitFor(() => {
      expect(screen.getByLabelText('Start Date')).toBeInTheDocument();
    });

    // Change filters
    const startDateInput = screen.getByLabelText('Start Date');
    const endDateInput = screen.getByLabelText('End Date');
    const statusSelect = screen.getByLabelText('Status');

    fireEvent.change(startDateInput, { target: { value: '2024-01-15' } });
    fireEvent.change(endDateInput, { target: { value: '2024-01-16' } });
    fireEvent.change(statusSelect, { target: { value: 'completed' } });

    // Click apply filters
    const applyButton = screen.getByText('Apply Filters');
    fireEvent.click(applyButton);

    // Verify API was called with correct parameters
    await waitFor(() => {
      expect(apiRequest).toHaveBeenCalledWith(
        expect.stringContaining('/api/time/logs?')
      );
      const lastCall = apiRequest.mock.calls[apiRequest.mock.calls.length - 1][0];
      expect(lastCall).toContain('start_date=2024-01-15');
      expect(lastCall).toContain('end_date=2024-01-16');
      expect(lastCall).toContain('status=completed');
    });
  });

  /**
   * Test: Pagination functionality
   */
  it('handles pagination correctly', async () => {
    const mockMultiPageResponse = {
      logs: mockTimeLogs,
      totalCount: 25,
      totalPages: 3,
      currentPage: 1,
    };

    apiRequest.mockResolvedValue({
      success: true,
      data: mockMultiPageResponse,
    });

    render(
      <MockAuthProvider>
        <TimeLogs />
      </MockAuthProvider>
    );

    // Wait for pagination to appear
    await waitFor(() => {
      expect(screen.getByText('Showing page 1 of 3 (25 total entries)')).toBeInTheDocument();
    });

    // Test next page button
    const nextButton = screen.getByText('Next');
    expect(nextButton).toBeInTheDocument();
    expect(nextButton).not.toBeDisabled();

    // Test previous button (should be disabled on first page)
    const prevButton = screen.getByText('Previous');
    expect(prevButton).toBeDisabled();
  });

  /**
   * Test: Empty state when no logs found
   */
  it('displays empty state when no logs are found', async () => {
    apiRequest.mockResolvedValue({
      success: true,
      data: {
        logs: [],
        totalCount: 0,
        totalPages: 0,
        currentPage: 1,
      },
    });

    render(
      <MockAuthProvider>
        <TimeLogs />
      </MockAuthProvider>
    );

    await waitFor(() => {
      expect(screen.getByText('No time logs found for the selected period.')).toBeInTheDocument();
    });
  });

  /**
   * Test: Loading state display
   */
  it('shows loading spinner during data fetch', async () => {
    // Mock delayed response
    apiRequest.mockImplementation(() => 
      new Promise(resolve => 
        setTimeout(() => resolve({ 
          success: true, 
          data: mockTimeLogsResponse 
        }), 100)
      )
    );

    render(
      <MockAuthProvider>
        <TimeLogs />
      </MockAuthProvider>
    );

    // Should show loading state
    expect(screen.getByText('Loading time logs...')).toBeInTheDocument();

    // Wait for loading to complete
    await waitFor(() => {
      expect(screen.queryByText('Loading time logs...')).not.toBeInTheDocument();
    });
  });

  /**
   * Test: Error handling
   */
  it('displays error message when API call fails', async () => {
    apiRequest.mockRejectedValue(new Error('Network error'));

    render(
      <MockAuthProvider>
        <TimeLogs />
      </MockAuthProvider>
    );

    await waitFor(() => {
      expect(screen.getByText('Error loading time logs. Please try again.')).toBeInTheDocument();
    });
  });

  /**
   * Test: Total hours calculation
   */
  it('calculates and displays total hours correctly', async () => {
    render(
      <MockAuthProvider>
        <TimeLogs />
      </MockAuthProvider>
    );

    await waitFor(() => {
      // Should show total entries and total hours in summary
      expect(screen.getByText('Total Entries')).toBeInTheDocument();
      expect(screen.getByText('Total Hours')).toBeInTheDocument();
      expect(screen.getByText('2')).toBeInTheDocument(); // Total entries
      expect(screen.getByText('9.00')).toBeInTheDocument(); // Total hours (only completed entries)
    });
  });

  /**
   * Test: Status badge styling
   */
  it('applies correct styling to status badges', async () => {
    render(
      <MockAuthProvider>
        <TimeLogs />
      </MockAuthProvider>
    );

    await waitFor(() => {
      const completedBadge = screen.getByText('Completed');
      const activeBadge = screen.getByText('Active');
      
      expect(completedBadge).toHaveClass('bg-green-100', 'text-green-800');
      expect(activeBadge).toHaveClass('bg-blue-100', 'text-blue-800');
    });
  });

  /**
   * Test: Date range display in summary
   */
  it('displays correct date range in summary section', async () => {
    render(
      <MockAuthProvider>
        <TimeLogs />
      </MockAuthProvider>
    );

    // Wait for component to initialize with current week dates
    await waitFor(() => {
      expect(screen.getByText('Date Range')).toBeInTheDocument();
    });

    // Should display some date range (exact dates will vary based on current date)
    const dateRangeElement = screen.getByText('Date Range').parentElement;
    expect(dateRangeElement).toBeInTheDocument();
  });

  /**
   * Test: Table accessibility
   */
  it('renders accessible table structure', async () => {
    render(
      <MockAuthProvider>
        <TimeLogs />
      </MockAuthProvider>
    );

    await waitFor(() => {
      // Check for table headers
      expect(screen.getByText('Date')).toBeInTheDocument();
      expect(screen.getByText('Clock In')).toBeInTheDocument();
      expect(screen.getByText('Clock Out')).toBeInTheDocument();
      expect(screen.getByText('Total Hours')).toBeInTheDocument();
      expect(screen.getByText('Location')).toBeInTheDocument();
      expect(screen.getByText('Status')).toBeInTheDocument();
    });

    // Verify table structure
    const table = screen.getByRole('table');
    expect(table).toBeInTheDocument();
  });
});