/**
 * Authentication middleware for JWT token verification
 */

const jwt = require('jsonwebtoken');
const { promisify } = require('util');
const { query } = require('../database');
const { logger } = require('../logger');

/**
 * Middleware to verify JWT token and authenticate requests
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      return res.status(401).json({ 
        error: 'Access token required',
        code: 'NO_TOKEN'
      });
    }

    // Check if token is blacklisted
    const blacklistedToken = await query(
      'SELECT id FROM token_blacklist WHERE token = ? AND expires_at > NOW()',
      [token]
    );

    if (blacklistedToken.length > 0) {
      return res.status(401).json({ 
        error: 'Token has been revoked',
        code: 'TOKEN_REVOKED'
      });
    }

    // Verify the token
    const decoded = await promisify(jwt.verify)(token, process.env.JWT_SECRET);
    
    // Get user information
    const users = await query(
      'SELECT id, email, role, is_active FROM users WHERE id = ?',
      [decoded.userId]
    );

    if (users.length === 0) {
      return res.status(401).json({ 
        error: 'User not found',
        code: 'USER_NOT_FOUND'
      });
    }

    const user = users[0];

    if (!user.is_active) {
      return res.status(401).json({ 
        error: 'Account is deactivated',
        code: 'ACCOUNT_DEACTIVATED'
      });
    }

    // Add user info to request
    req.user = {
      id: user.id,
      email: user.email,
      role: user.role
    };

    next();
  } catch (error) {
    logger.error('Authentication error:', error);
    
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({ 
        error: 'Invalid token',
        code: 'INVALID_TOKEN'
      });
    }
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({ 
        error: 'Token has expired',
        code: 'TOKEN_EXPIRED'
      });
    }

    return res.status(500).json({ 
      error: 'Authentication failed',
      code: 'AUTH_ERROR'
    });
  }
};

/**
 * Middleware to check if user has required role
 * @param {Array|string} roles - Required roles (admin, manager, employee)
 * @returns {Function} Middleware function
 */
const requireRole = (roles) => {
  return (req, res, next) => {
    const userRole = req.user?.role;
    
    if (!userRole) {
      return res.status(401).json({ 
        error: 'Authentication required',
        code: 'AUTH_REQUIRED'
      });
    }

    const allowedRoles = Array.isArray(roles) ? roles : [roles];
    
    if (!allowedRoles.includes(userRole)) {
      return res.status(403).json({ 
        error: 'Insufficient permissions',
        code: 'INSUFFICIENT_PERMISSIONS',
        required: allowedRoles,
        current: userRole
      });
    }

    next();
  };
};

/**
 * Middleware to check if user can access specific user data
 * @param {Object} req - Express request object  
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const requireSelfOrAdmin = (req, res, next) => {
  const userId = parseInt(req.params.id);
  const currentUserId = req.user.id;
  const userRole = req.user.role;

  // Allow admins and managers to access any user data
  if (userRole === 'admin' || userRole === 'manager') {
    return next();
  }

  // Allow users to access their own data
  if (userId === currentUserId) {
    return next();
  }

  return res.status(403).json({ 
    error: 'Access denied - can only access own data',
    code: 'ACCESS_DENIED'
  });
};

module.exports = {
  authenticateToken,
  requireRole,
  requireSelfOrAdmin
};