/**
 * Tests for ManagerApprovalInterface component
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import ManagerApprovalInterface from '@/components/approvals/ManagerApprovalInterface';
import * as approvalsLib from '@/lib/approvals';

// Mock the approvals library
jest.mock('@/lib/approvals', () => ({
  getPendingApprovals: jest.fn(),
  processApproval: jest.fn(),
  getStatusBadgeColor: jest.fn(() => 'bg-yellow-100 text-yellow-800'),
  getRequestTypeDisplayName: jest.fn((type) => type),
  formatRequestData: jest.fn(() => 'Mock request data'),
}));

const mockGetPendingApprovals = approvalsLib.getPendingApprovals as jest.MockedFunction<typeof approvalsLib.getPendingApprovals>;
const mockProcessApproval = approvalsLib.processApproval as jest.MockedFunction<typeof approvalsLib.processApproval>;

describe('ManagerApprovalInterface', () => {
  const mockApprovals = [
    {
      id: 1,
      timeLogId: 1,
      requestedBy: 1,
      assignedTo: 2,
      requestType: 'correction' as const,
      requestData: { clockIn: '09:00', clockOut: '17:00' },
      reason: 'Need to correct time',
      status: 'pending' as const,
      createdAt: '2024-01-15T10:00:00Z',
      requesterName: 'John Doe',
      requesterEmail: '<EMAIL>',
    },
    {
      id: 2,
      timeLogId: 2,
      requestedBy: 3,
      assignedTo: 2,
      requestType: 'overtime' as const,
      requestData: { hours: 2, date: '2024-01-15' },
      reason: 'Project deadline',
      status: 'pending' as const,
      createdAt: '2024-01-15T11:00:00Z',
      requesterName: 'Jane Smith',
      requesterEmail: '<EMAIL>',
    },
  ];

  const defaultProps = {
    onApprovalProcessed: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockGetPendingApprovals.mockResolvedValue({
      requests: mockApprovals,
      pagination: { page: 1, limit: 20, total: 2, totalPages: 1 },
    });
  });

  describe('Rendering', () => {
    it('renders the interface with pending approvals', async () => {
      render(<ManagerApprovalInterface {...defaultProps} />);

      await waitFor(() => {
        expect(screen.getByText('Pending Approvals (2)')).toBeInTheDocument();
      });

      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('Jane Smith')).toBeInTheDocument();
    });

    it('shows loading state initially', () => {
      render(<ManagerApprovalInterface {...defaultProps} />);
      expect(screen.getByRole('status')).toBeInTheDocument(); // Loading spinner
    });

    it('shows empty state when no approvals', async () => {
      mockGetPendingApprovals.mockResolvedValue({
        requests: [],
        pagination: { page: 1, limit: 20, total: 0, totalPages: 0 },
      });

      render(<ManagerApprovalInterface {...defaultProps} />);

      await waitFor(() => {
        expect(screen.getByText('No pending approvals found.')).toBeInTheDocument();
      });
    });

    it('shows error message when loading fails', async () => {
      mockGetPendingApprovals.mockRejectedValue(new Error('Network error'));

      render(<ManagerApprovalInterface {...defaultProps} />);

      await waitFor(() => {
        expect(screen.getByText('Network error')).toBeInTheDocument();
      });
    });
  });

  describe('Filtering', () => {
    it('applies request type filter', async () => {
      const user = userEvent.setup();
      render(<ManagerApprovalInterface {...defaultProps} />);

      await waitFor(() => {
        expect(screen.getByText('Pending Approvals (2)')).toBeInTheDocument();
      });

      const typeFilter = screen.getByDisplayValue('All Types');
      await user.selectOptions(typeFilter, 'correction');

      await waitFor(() => {
        expect(mockGetPendingApprovals).toHaveBeenCalledWith(
          expect.objectContaining({ requestType: 'correction' })
        );
      });
    });

    it('applies date range filters', async () => {
      const user = userEvent.setup();
      render(<ManagerApprovalInterface {...defaultProps} />);

      await waitFor(() => {
        expect(screen.getByText('Pending Approvals (2)')).toBeInTheDocument();
      });

      const startDateInput = screen.getByDisplayValue('');
      await user.type(startDateInput, '2024-01-01');

      await waitFor(() => {
        expect(mockGetPendingApprovals).toHaveBeenCalledWith(
          expect.objectContaining({ startDate: '2024-01-01' })
        );
      });
    });
  });

  describe('Selection', () => {
    it('selects individual approvals', async () => {
      const user = userEvent.setup();
      render(<ManagerApprovalInterface {...defaultProps} />);

      await waitFor(() => {
        expect(screen.getByText('Pending Approvals (2)')).toBeInTheDocument();
      });

      const checkboxes = screen.getAllByRole('checkbox');
      const firstApprovalCheckbox = checkboxes[1]; // Skip the "select all" checkbox

      await user.click(firstApprovalCheckbox);

      expect(screen.getByText('Approve (1)')).toBeInTheDocument();
      expect(screen.getByText('Reject (1)')).toBeInTheDocument();
    });

    it('selects all approvals', async () => {
      const user = userEvent.setup();
      render(<ManagerApprovalInterface {...defaultProps} />);

      await waitFor(() => {
        expect(screen.getByText('Pending Approvals (2)')).toBeInTheDocument();
      });

      const selectAllCheckbox = screen.getAllByRole('checkbox')[0];
      await user.click(selectAllCheckbox);

      expect(screen.getByText('Approve (2)')).toBeInTheDocument();
      expect(screen.getByText('Reject (2)')).toBeInTheDocument();
    });
  });

  describe('Individual Approval Processing', () => {
    it('opens approve modal when approve button is clicked', async () => {
      const user = userEvent.setup();
      render(<ManagerApprovalInterface {...defaultProps} />);

      await waitFor(() => {
        expect(screen.getByText('Pending Approvals (2)')).toBeInTheDocument();
      });

      const approveButton = screen.getAllByText('Approve')[0];
      await user.click(approveButton);

      expect(screen.getByText('Approve Request')).toBeInTheDocument();
      expect(screen.getByText('Employee: John Doe')).toBeInTheDocument();
    });

    it('opens reject modal when reject button is clicked', async () => {
      const user = userEvent.setup();
      render(<ManagerApprovalInterface {...defaultProps} />);

      await waitFor(() => {
        expect(screen.getByText('Pending Approvals (2)')).toBeInTheDocument();
      });

      const rejectButton = screen.getAllByText('Reject')[0];
      await user.click(rejectButton);

      expect(screen.getByText('Reject Request')).toBeInTheDocument();
      expect(screen.getByText('Employee: John Doe')).toBeInTheDocument();
    });

    it('processes approval successfully', async () => {
      const user = userEvent.setup();
      const mockProcessedApproval = { ...mockApprovals[0], status: 'approved' as const };
      mockProcessApproval.mockResolvedValue(mockProcessedApproval);

      render(<ManagerApprovalInterface {...defaultProps} />);

      await waitFor(() => {
        expect(screen.getByText('Pending Approvals (2)')).toBeInTheDocument();
      });

      const approveButton = screen.getAllByText('Approve')[0];
      await user.click(approveButton);

      const commentsInput = screen.getByPlaceholderText('Add any comments...');
      await user.type(commentsInput, 'Approved - looks good');

      const confirmButton = screen.getByRole('button', { name: 'Approve' });
      await user.click(confirmButton);

      await waitFor(() => {
        expect(mockProcessApproval).toHaveBeenCalledWith(1, {
          status: 'approved',
          comments: 'Approved - looks good',
        });
      });

      expect(defaultProps.onApprovalProcessed).toHaveBeenCalled();
    });

    it('handles processing errors', async () => {
      const user = userEvent.setup();
      mockProcessApproval.mockRejectedValue(new Error('Processing failed'));

      render(<ManagerApprovalInterface {...defaultProps} />);

      await waitFor(() => {
        expect(screen.getByText('Pending Approvals (2)')).toBeInTheDocument();
      });

      const approveButton = screen.getAllByText('Approve')[0];
      await user.click(approveButton);

      const confirmButton = screen.getByRole('button', { name: 'Approve' });
      await user.click(confirmButton);

      await waitFor(() => {
        expect(screen.getByText('Processing failed')).toBeInTheDocument();
      });
    });
  });

  describe('Bulk Processing', () => {
    it('processes bulk approvals', async () => {
      const user = userEvent.setup();
      const mockProcessedApproval = { ...mockApprovals[0], status: 'approved' as const };
      mockProcessApproval.mockResolvedValue(mockProcessedApproval);

      render(<ManagerApprovalInterface {...defaultProps} />);

      await waitFor(() => {
        expect(screen.getByText('Pending Approvals (2)')).toBeInTheDocument();
      });

      // Select all approvals
      const selectAllCheckbox = screen.getAllByRole('checkbox')[0];
      await user.click(selectAllCheckbox);

      // Click bulk approve
      const bulkApproveButton = screen.getByText('Approve (2)');
      await user.click(bulkApproveButton);

      await waitFor(() => {
        expect(mockProcessApproval).toHaveBeenCalledTimes(2);
        expect(mockProcessApproval).toHaveBeenCalledWith(1, {
          status: 'approved',
          comments: 'Bulk approved by manager',
        });
        expect(mockProcessApproval).toHaveBeenCalledWith(2, {
          status: 'approved',
          comments: 'Bulk approved by manager',
        });
      });

      expect(defaultProps.onApprovalProcessed).toHaveBeenCalled();
    });

    it('processes bulk rejections', async () => {
      const user = userEvent.setup();
      const mockProcessedApproval = { ...mockApprovals[0], status: 'rejected' as const };
      mockProcessApproval.mockResolvedValue(mockProcessedApproval);

      render(<ManagerApprovalInterface {...defaultProps} />);

      await waitFor(() => {
        expect(screen.getByText('Pending Approvals (2)')).toBeInTheDocument();
      });

      // Select first approval
      const checkboxes = screen.getAllByRole('checkbox');
      await user.click(checkboxes[1]);

      // Click bulk reject
      const bulkRejectButton = screen.getByText('Reject (1)');
      await user.click(bulkRejectButton);

      await waitFor(() => {
        expect(mockProcessApproval).toHaveBeenCalledWith(1, {
          status: 'rejected',
          comments: 'Bulk rejected by manager',
        });
      });

      expect(defaultProps.onApprovalProcessed).toHaveBeenCalled();
    });
  });

  describe('Modal Interactions', () => {
    it('closes modal when cancel is clicked', async () => {
      const user = userEvent.setup();
      render(<ManagerApprovalInterface {...defaultProps} />);

      await waitFor(() => {
        expect(screen.getByText('Pending Approvals (2)')).toBeInTheDocument();
      });

      const approveButton = screen.getAllByText('Approve')[0];
      await user.click(approveButton);

      expect(screen.getByText('Approve Request')).toBeInTheDocument();

      const cancelButton = screen.getByRole('button', { name: 'Cancel' });
      await user.click(cancelButton);

      expect(screen.queryByText('Approve Request')).not.toBeInTheDocument();
    });

    it('allows adding comments in modal', async () => {
      const user = userEvent.setup();
      render(<ManagerApprovalInterface {...defaultProps} />);

      await waitFor(() => {
        expect(screen.getByText('Pending Approvals (2)')).toBeInTheDocument();
      });

      const approveButton = screen.getAllByText('Approve')[0];
      await user.click(approveButton);

      const commentsInput = screen.getByPlaceholderText('Add any comments...');
      await user.type(commentsInput, 'Test comment');

      expect(commentsInput).toHaveValue('Test comment');
    });
  });
});
