"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/approvals/page",{

/***/ "(app-pages-browser)/./src/app/approvals/page.tsx":
/*!************************************!*\
  !*** ./src/app/approvals/page.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ApprovalsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/auth/ProtectedRoute */ \"(app-pages-browser)/./src/components/auth/ProtectedRoute.tsx\");\n/* harmony import */ var _components_approvals_ManagerApprovalInterface__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/approvals/ManagerApprovalInterface */ \"(app-pages-browser)/./src/components/approvals/ManagerApprovalInterface.tsx\");\n/* harmony import */ var _components_approvals_ApprovalHistory__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/approvals/ApprovalHistory */ \"(app-pages-browser)/./src/components/approvals/ApprovalHistory.tsx\");\n/* harmony import */ var _lib_approvals__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/approvals */ \"(app-pages-browser)/./src/lib/approvals.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n/**\n * Approval Dashboard Page\n * Main page for approval workflow with tabs for different views\n */ function ApprovalsPage() {\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"my-requests\");\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [statsLoading, setStatsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Check if user can manage approvals (admin or manager)\n    const canManageApprovals = (user === null || user === void 0 ? void 0 : user.role) === \"admin\" || (user === null || user === void 0 ? void 0 : user.role) === \"manager\";\n    /**\n   * Load approval statistics for managers/admins\n   */ const loadStats = async ()=>{\n        if (!canManageApprovals) return;\n        try {\n            setStatsLoading(true);\n            const approvalStats = await (0,_lib_approvals__WEBPACK_IMPORTED_MODULE_6__.getApprovalStats)();\n            setStats(approvalStats);\n        } catch (error) {\n            console.error(\"Failed to load approval stats:\", error);\n        } finally{\n            setStatsLoading(false);\n        }\n    };\n    /**\n   * Load stats on component mount\n   */ (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadStats();\n    }, [\n        canManageApprovals\n    ]);\n    /**\n   * Handle approval processed (refresh stats)\n   */ const handleApprovalProcessed = ()=>{\n        loadStats();\n    };\n    /**\n   * Get tab configuration based on user role\n   */ const getTabs = ()=>{\n        const baseTabs = [\n            {\n                id: \"my-requests\",\n                name: \"My Requests\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-4 h-4\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 11\n                }, this)\n            }\n        ];\n        if (canManageApprovals) {\n            baseTabs.push({\n                id: \"pending-approvals\",\n                name: \"Pending Approvals\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-5 h-5\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 13\n                }, this),\n                badge: (stats === null || stats === void 0 ? void 0 : stats.pendingRequests) || 0\n            }, {\n                id: \"all-history\",\n                name: \"All History\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-5 h-5\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 13\n                }, this)\n            });\n        }\n        return baseTabs;\n    };\n    /**\n   * Set default tab based on user role\n   */ (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (canManageApprovals && (stats === null || stats === void 0 ? void 0 : stats.pendingRequests) && stats.pendingRequests > 0) {\n            setActiveTab(\"pending-approvals\");\n        }\n    }, [\n        canManageApprovals,\n        stats\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 py-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"Approval Management\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1 text-gray-600\",\n                                children: canManageApprovals ? \"Manage approval requests and review team submissions\" : \"View and track your approval requests\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 11\n                    }, this),\n                    canManageApprovals && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white overflow-hidden shadow rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-5 w-5 text-gray-400\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                                        lineNumber: 134,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                                    lineNumber: 133,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-5 w-0 flex-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                            className: \"text-sm font-medium text-gray-500 truncate\",\n                                                            children: \"Total Requests\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                                            lineNumber: 139,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                            className: \"text-lg font-medium text-gray-900\",\n                                                            children: statsLoading ? \"...\" : (stats === null || stats === void 0 ? void 0 : stats.totalRequests) || 0\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                                            lineNumber: 140,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                                    lineNumber: 138,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white overflow-hidden shadow rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-5 w-5 text-yellow-400\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                                        lineNumber: 154,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-5 w-0 flex-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                            className: \"text-sm font-medium text-gray-500 truncate\",\n                                                            children: \"Pending\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                                            lineNumber: 159,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                            className: \"text-lg font-medium text-yellow-600\",\n                                                            children: statsLoading ? \"...\" : (stats === null || stats === void 0 ? void 0 : stats.pendingRequests) || 0\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                                            lineNumber: 160,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                                    lineNumber: 158,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white overflow-hidden shadow rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-5 w-5 text-green-400\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                                        lineNumber: 174,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                                    lineNumber: 173,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-5 w-0 flex-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                            className: \"text-sm font-medium text-gray-500 truncate\",\n                                                            children: \"Approved\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                                            lineNumber: 179,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                            className: \"text-lg font-medium text-green-600\",\n                                                            children: statsLoading ? \"...\" : (stats === null || stats === void 0 ? void 0 : stats.approvedRequests) || 0\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                                            lineNumber: 180,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white overflow-hidden shadow rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-5 w-5 text-red-400\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                                        lineNumber: 194,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                                lineNumber: 192,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-5 w-0 flex-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                            className: \"text-sm font-medium text-gray-500 truncate\",\n                                                            children: \"Rejected\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                                            lineNumber: 199,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                            className: \"text-lg font-medium text-red-600\",\n                                                            children: statsLoading ? \"...\" : (stats === null || stats === void 0 ? void 0 : stats.rejectedRequests) || 0\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                                            lineNumber: 200,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white shadow rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"-mb-px flex space-x-8 px-6\",\n                                    \"aria-label\": \"Tabs\",\n                                    children: getTabs().map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab(tab.id),\n                                            className: \"\".concat(activeTab === tab.id ? \"border-blue-500 text-blue-600\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\", \" whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2\"),\n                                            children: [\n                                                tab.icon,\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: tab.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                                    lineNumber: 226,\n                                                    columnNumber: 21\n                                                }, this),\n                                                tab.badge !== undefined && tab.badge > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded-full\",\n                                                    children: tab.badge\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, tab.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: [\n                                    activeTab === \"my-requests\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_approvals_ApprovalHistory__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        viewMode: \"my-requests\",\n                                        showFilters: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 17\n                                    }, this),\n                                    activeTab === \"pending-approvals\" && canManageApprovals && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_approvals_ManagerApprovalInterface__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        onApprovalProcessed: handleApprovalProcessed\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 17\n                                    }, this),\n                                    activeTab === \"all-history\" && canManageApprovals && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_approvals_ApprovalHistory__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        viewMode: \"all-approvals\",\n                                        showFilters: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                lineNumber: 114,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n            lineNumber: 113,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n        lineNumber: 112,\n        columnNumber: 5\n    }, this);\n}\n_s(ApprovalsPage, \"baRjN+aGvVwcjMTWFGV47W/dD68=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = ApprovalsPage;\nvar _c;\n$RefreshReg$(_c, \"ApprovalsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/approvals/page.tsx\n"));

/***/ })

});