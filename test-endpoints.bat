@echo off
echo 🚀 Testing Flexair Approval System API Endpoints
echo.

echo 1. Testing Backend Server Health...
curl -s -o nul -w "Backend Server: %%{http_code}\n" http://localhost:5000/api/health
echo.

echo 2. Testing Employee Login...
curl -X POST http://localhost:5000/api/auth/login ^
  -H "Content-Type: application/json" ^
  -d "{\"email\":\"<EMAIL>\",\"password\":\"password123\"}" ^
  -w "Employee Login: %%{http_code}\n" -s -o employee_token.tmp
echo.

echo 3. Testing Manager Login...
curl -X POST http://localhost:5000/api/auth/login ^
  -H "Content-Type: application/json" ^
  -d "{\"email\":\"<EMAIL>\",\"password\":\"password123\"}" ^
  -w "Manager Login: %%{http_code}\n" -s -o manager_token.tmp
echo.

echo 4. Testing Approval Endpoints (requires valid tokens)...
echo Note: If login tests failed (status 401/404), create test users first
echo.

echo ✅ Basic connectivity tests complete!
echo.
echo 📋 Next Steps for Manual Testing:
echo 1. Ensure both backend (port 5000) and frontend (port 3000) are running
echo 2. Create test users using the setup-test-environment.sql script
echo 3. Follow the MANUAL_TESTING_GUIDE.md for complete testing
echo.

pause
