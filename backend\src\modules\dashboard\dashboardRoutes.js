/**
 * Dashboard routes
 */

const express = require('express');
const router = express.Router();
const dashboardController = require('./dashboardController');
const { authenticateToken } = require('../../shared/middleware/authMiddleware');

// All routes require authentication
router.use(authenticateToken);

// Get comprehensive dashboard data
router.get('/', dashboardController.getDashboardData);

// Get time tracking trends
router.get('/trends', dashboardController.getTimeTrackingTrends);

// Get quick stats summary
router.get('/quick-stats', dashboardController.getQuickStats);

module.exports = router;