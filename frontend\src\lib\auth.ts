/**
 * Authentication utilities and API calls
 * Handles login, registration, token management, and user session
 */

const API_BASE = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5002/api';

export interface User {
  id: number;
  email: string;
  firstName: string;
  lastName: string;
  role: 'admin' | 'manager' | 'employee';
  isActive: boolean;
  lastLogin?: string;
  createdAt: string;
}

export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
  expiresIn: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface RegisterData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  department?: string;
}

export interface AuthResponse {
  message: string;
  user: User;
  tokens: AuthTokens;
}

/**
 * API response wrapper for error handling
 */
class ApiError extends Error {
  constructor(
    message: string,
    public status: number,
    public code?: string
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

/**
 * Make authenticated API request with automatic token refresh
 * @param url - API endpoint URL
 * @param options - Fetch options
 * @returns Response data
 */
export async function apiRequest(url: string, options: RequestInit = {}): Promise<any> {
  const token = getAccessToken();
  
  const config: RequestInit = {
    headers: {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` }),
      ...options.headers,
    },
    ...options,
  };

  const response = await fetch(`${API_BASE}${url}`, config);
  
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new ApiError(
      errorData.error?.message || 'Request failed',
      response.status,
      errorData.error?.code
    );
  }

  return response.json();
}

/**
 * Login user with email and password
 * @param credentials - Login credentials
 * @returns Authentication response with user data and tokens
 */
export async function login(credentials: LoginCredentials): Promise<AuthResponse> {
  const response = await apiRequest('/auth/login', {
    method: 'POST',
    body: JSON.stringify(credentials),
  });

  // Store tokens
  setTokens(response.tokens);
  
  return response;
}

/**
 * Register new user account
 * @param userData - Registration data
 * @returns Authentication response with user data and tokens
 */
export async function register(userData: RegisterData): Promise<AuthResponse> {
  const response = await apiRequest('/auth/register', {
    method: 'POST',
    body: JSON.stringify(userData),
  });

  // Store tokens
  setTokens(response.tokens);
  
  return response;
}

/**
 * Logout user and clear tokens
 */
export async function logout(): Promise<void> {
  const refreshToken = getRefreshToken();
  
  try {
    await apiRequest('/auth/logout', {
      method: 'POST',
      body: JSON.stringify({ refreshToken }),
    });
  } catch (error) {
    // Continue with logout even if API call fails
    console.warn('Logout API call failed:', error);
  }
  
  clearTokens();
}

/**
 * Get current user profile
 * @returns Current user data
 */
export async function getCurrentUser(): Promise<User> {
  const response = await apiRequest('/auth/profile');
  return response.user;
}

/**
 * Verify if current token is valid
 * @returns Token validity and user data
 */
export async function verifyToken(): Promise<{ valid: boolean; user?: User }> {
  try {
    const response = await apiRequest('/auth/verify');
    return { valid: response.valid, user: response.user };
  } catch (error) {
    return { valid: false };
  }
}

/**
 * Refresh access token using refresh token
 * @returns New authentication tokens
 */
export async function refreshTokens(): Promise<AuthTokens> {
  const refreshToken = getRefreshToken();
  
  if (!refreshToken) {
    throw new Error('No refresh token available');
  }

  const response = await fetch(`${API_BASE}/auth/refresh`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ refreshToken }),
  });

  if (!response.ok) {
    clearTokens();
    throw new Error('Token refresh failed');
  }

  const data = await response.json();
  setTokens(data.tokens);
  
  return data.tokens;
}

/**
 * Store authentication tokens in localStorage
 * @param tokens - Authentication tokens to store
 */
export function setTokens(tokens: AuthTokens): void {
  if (typeof window !== 'undefined') {
    localStorage.setItem('accessToken', tokens.accessToken);
    localStorage.setItem('refreshToken', tokens.refreshToken);
  }
}

/**
 * Get stored access token
 * @returns Access token or null
 */
export function getAccessToken(): string | null {
  if (typeof window !== 'undefined') {
    return localStorage.getItem('accessToken');
  }
  return null;
}

/**
 * Get stored refresh token
 * @returns Refresh token or null
 */
export function getRefreshToken(): string | null {
  if (typeof window !== 'undefined') {
    return localStorage.getItem('refreshToken');
  }
  return null;
}

/**
 * Clear all stored authentication tokens
 */
export function clearTokens(): void {
  if (typeof window !== 'undefined') {
    localStorage.removeItem('accessToken');
    localStorage.removeItem('refreshToken');
  }
}

/**
 * Check if user is currently authenticated
 * @returns True if user has valid tokens
 */
export function isAuthenticated(): boolean {
  return !!(getAccessToken() && getRefreshToken());
}

/**
 * Decode JWT token payload without verification
 * @param token - JWT token to decode
 * @returns Decoded payload or null
 */
export function decodeToken(token: string): any {
  try {
    const payload = token.split('.')[1];
    return JSON.parse(atob(payload));
  } catch (error) {
    return null;
  }
}

/**
 * Check if token is expired
 * @param token - JWT token to check
 * @returns True if token is expired
 */
export function isTokenExpired(token: string): boolean {
  const decoded = decodeToken(token);
  if (!decoded || !decoded.exp) return true;
  
  return Date.now() >= decoded.exp * 1000;
}