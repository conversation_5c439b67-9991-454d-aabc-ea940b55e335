/**
 * Global error handling middleware
 * Catches and processes all unhandled errors in the application
 */

const { logger } = require('../logger');

/**
 * Custom application error class
 */
class AppError extends Error {
  constructor(message, statusCode = 500, isOperational = true) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    this.status = `${statusCode}`.startsWith('4') ? 'fail' : 'error';
    
    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * Validation error class
 */
class ValidationError extends AppError {
  constructor(message, details = null) {
    super(message, 400);
    this.details = details;
  }
}

/**
 * Authentication error class
 */
class AuthenticationError extends AppError {
  constructor(message = 'Authentication failed') {
    super(message, 401);
  }
}

/**
 * Authorization error class
 */
class AuthorizationError extends AppError {
  constructor(message = 'Access denied') {
    super(message, 403);
  }
}

/**
 * Not found error class
 */
class NotFoundError extends AppError {
  constructor(message = 'Resource not found') {
    super(message, 404);
  }
}

/**
 * Database error class
 */
class DatabaseError extends AppError {
  constructor(message = 'Database operation failed', originalError = null) {
    super(message, 500);
    this.originalError = originalError;
  }
}

/**
 * Handle MySQL specific errors
 * @param {Error} error - MySQL error object
 * @returns {AppError} Formatted application error
 */
function handleMySQLError(error) {
  switch (error.code) {
    case 'ER_DUP_ENTRY':
      return new ValidationError('Duplicate entry. Record already exists.');
    case 'ER_NO_REFERENCED_ROW_2':
      return new ValidationError('Referenced record does not exist.');
    case 'ER_ROW_IS_REFERENCED_2':
      return new ValidationError('Cannot delete record. It is referenced by other records.');
    case 'ER_ACCESS_DENIED_ERROR':
      return new DatabaseError('Database access denied');
    case 'ER_BAD_DB_ERROR':
      return new DatabaseError('Database does not exist');
    case 'ECONNREFUSED':
      return new DatabaseError('Database connection refused');
    case 'ENOTFOUND':
      return new DatabaseError('Database host not found');
    default:
      return new DatabaseError('Database operation failed', error);
  }
}

/**
 * Handle JWT errors
 * @param {Error} error - JWT error object
 * @returns {AppError} Formatted application error
 */
function handleJWTError(error) {
  if (error.name === 'JsonWebTokenError') {
    return new AuthenticationError('Invalid token');
  }
  if (error.name === 'TokenExpiredError') {
    return new AuthenticationError('Token expired');
  }
  return new AuthenticationError('Token verification failed');
}

/**
 * Handle validation errors from Joi
 * @param {Error} error - Joi validation error
 * @returns {ValidationError} Formatted validation error
 */
function handleValidationError(error) {
  const details = error.details.map(detail => ({
    field: detail.path.join('.'),
    message: detail.message,
    value: detail.context?.value
  }));
  
  return new ValidationError('Validation failed', details);
}

/**
 * Send error response in development mode
 * @param {Error} error - Error object
 * @param {Object} res - Express response object
 */
function sendErrorDev(error, res) {
  res.status(error.statusCode || 500).json({
    status: error.status || 'error',
    error: {
      message: error.message,
      stack: error.stack,
      details: error.details || null
    },
    timestamp: new Date().toISOString()
  });
}

/**
 * Send error response in production mode
 * @param {Error} error - Error object
 * @param {Object} res - Express response object
 */
function sendErrorProd(error, res) {
  // Operational, trusted error: send message to client
  if (error.isOperational) {
    res.status(error.statusCode).json({
      status: error.status,
      message: error.message,
      details: error.details || null,
      timestamp: new Date().toISOString()
    });
  } else {
    // Programming or unknown error: don't leak error details
    logger.error('Unexpected error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Something went wrong!',
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * Global error handling middleware
 * @param {Error} err - Error object
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Next middleware function
 */
function errorHandler(err, req, res, next) {
  let error = { ...err };
  error.message = err.message;
  error.statusCode = err.statusCode || 500;

  // Log error details
  logger.error('Error occurred:', {
    message: error.message,
    stack: error.stack,
    url: req.originalUrl,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.user?.id || null
  });

  // Handle specific error types
  if (err.code && err.code.startsWith('ER_')) {
    error = handleMySQLError(err);
  } else if (err.name === 'JsonWebTokenError' || err.name === 'TokenExpiredError') {
    error = handleJWTError(err);
  } else if (err.isJoi) {
    error = handleValidationError(err);
  } else if (err.name === 'MulterError') {
    error = new ValidationError(`File upload error: ${err.message}`);
  }

  // Send error response
  if (process.env.NODE_ENV === 'development') {
    sendErrorDev(error, res);
  } else {
    sendErrorProd(error, res);
  }
}

/**
 * Async error wrapper
 * Catches async errors and passes them to error handler
 * @param {Function} fn - Async function to wrap
 * @returns {Function} Wrapped function
 */
function asyncErrorHandler(fn) {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
}

module.exports = {
  errorHandler,
  asyncErrorHandler,
  AppError,
  ValidationError,
  AuthenticationError,
  AuthorizationError,
  NotFoundError,
  DatabaseError
};