/**
 * Dashboard Page
 * Main application dashboard with time tracking and statistics
 */

'use client';

import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import { useApprovalNotifications } from '@/hooks/useApprovalNotifications';
import NotificationBadge from '@/components/approvals/NotificationBadge';
import ApprovalNotificationSummary from '@/components/approvals/ApprovalNotificationSummary';

/**
 * Dashboard component with user information and quick actions
 */
function DashboardContent() {
  const { user, logout } = useAuth();
  const notifications = useApprovalNotifications();

  /**
   * Handle user logout
   */
  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
              <p className="text-sm text-gray-600">Welcome back, {user?.firstName}!</p>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-500">
                Role: <span className="font-medium">{user?.role}</span>
              </span>
              <button
                onClick={handleLogout}
                className="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                Logout
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            
            {/* User Info Card */}
            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="px-4 py-5 sm:p-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900">
                  User Information
                </h3>
                <div className="mt-4 space-y-2">
                  <p className="text-sm text-gray-600">
                    <span className="font-medium">Name:</span> {user?.firstName} {user?.lastName}
                  </p>
                  <p className="text-sm text-gray-600">
                    <span className="font-medium">Email:</span> {user?.email}
                  </p>
                  <p className="text-sm text-gray-600">
                    <span className="font-medium">Role:</span> {user?.role}
                  </p>
                  <p className="text-sm text-gray-600">
                    <span className="font-medium">Status:</span> 
                    <span className={`ml-1 ${user?.isActive ? 'text-green-600' : 'text-red-600'}`}>
                      {user?.isActive ? 'Active' : 'Inactive'}
                    </span>
                  </p>
                </div>
              </div>
            </div>

            {/* Quick Actions Card */}
            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="px-4 py-5 sm:p-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900">
                  Quick Actions
                </h3>
                <div className="mt-4 space-y-3">
                  <a
                    href="/time"
                    className="w-full block text-center bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium transition duration-200"
                  >
                    Time Tracking
                  </a>
                  <a
                    href="/approvals"
                    className="w-full block text-center bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-md text-sm font-medium transition duration-200 relative"
                  >
                    <span>Approval Management</span>
                    {(notifications.pendingCount > 0 || notifications.myPendingRequests > 0) && (
                      <NotificationBadge
                        count={notifications.pendingCount + notifications.myPendingRequests}
                        className="absolute -top-2 -right-2"
                      />
                    )}
                  </a>
                  {(user?.role === 'admin' || user?.role === 'manager') && (
                    <a
                      href="/users"
                      className="w-full block text-center bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition duration-200"
                    >
                      User Management
                    </a>
                  )}
                  <button className="w-full bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                    Reports & Analytics
                  </button>
                </div>
              </div>
            </div>

            {/* Notifications Card */}
            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="px-4 py-5 sm:p-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900">
                  Notifications
                </h3>
                <div className="mt-4">
                  <ApprovalNotificationSummary
                    notifications={notifications}
                    showDetails={true}
                  />
                </div>
              </div>
            </div>

          </div>

          {/* Welcome Message */}
          <div className="mt-8 bg-indigo-50 rounded-lg p-6">
            <h2 className="text-xl font-semibold text-indigo-900 mb-2">
              🎉 Welcome to Flexair Timekeeping!
            </h2>
            <p className="text-indigo-700">
              Your authentication system is working perfectly. You are successfully logged in as{' '}
              <span className="font-medium">{user?.firstName} {user?.lastName}</span> with{' '}
              <span className="font-medium">{user?.role}</span> privileges.
            </p>
            <div className="mt-4 text-sm text-indigo-600">
              <p>✅ Authentication: Working</p>
              <p>✅ Protected Routes: Working</p>
              <p>✅ User Session: Active</p>
              <p>✅ JWT Tokens: Valid</p>
            </div>
          </div>

          {/* Feature Status */}
          <div className="mt-6 bg-gray-100 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-3">
              Feature Status
            </h3>
            <ul className="space-y-1 text-sm">
              <li className="flex items-center text-green-700">
                <span className="mr-2">✅</span>
                Authentication System - Complete
              </li>
              <li className="flex items-center text-green-700">
                <span className="mr-2">✅</span>
                Time Tracking Interface - Complete
              </li>
              <li className="flex items-center text-green-700">
                <span className="mr-2">✅</span>
                User Management Interface - Complete
              </li>
              <li className="flex items-center text-green-700">
                <span className="mr-2">✅</span>
                Approval Workflow Interface - Complete
              </li>
              <li className="flex items-center text-gray-700">
                <span className="mr-2">⏳</span>
                Biometric Integration - Planned
              </li>
              <li className="flex items-center text-gray-700">
                <span className="mr-2">⏳</span>
                Reports & Analytics Dashboard - Planned
              </li>
            </ul>
          </div>
        </div>
      </main>
    </div>
  );
}

/**
 * Dashboard page with authentication protection
 */
export default function DashboardPage() {
  return (
    <ProtectedRoute requireAuth={true}>
      <DashboardContent />
    </ProtectedRoute>
  );
}