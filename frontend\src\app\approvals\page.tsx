'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import ManagerApprovalInterface from '@/components/approvals/ManagerApprovalInterface';
import ApprovalHistory from '@/components/approvals/ApprovalHistory';
import { getApprovalStats, ApprovalStats } from '@/lib/approvals';

/**
 * Tab type definition
 */
type TabType = 'my-requests' | 'pending-approvals' | 'all-history';

/**
 * Approval Dashboard Page
 * Main page for approval workflow with tabs for different views
 */
export default function ApprovalsPage() {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState<TabType>('my-requests');
  const [stats, setStats] = useState<ApprovalStats | null>(null);
  const [statsLoading, setStatsLoading] = useState(false);

  // Check if user can manage approvals (admin or manager)
  const canManageApprovals = user?.role === 'admin' || user?.role === 'manager';

  /**
   * Load approval statistics for managers/admins
   */
  const loadStats = async () => {
    if (!canManageApprovals) return;

    try {
      setStatsLoading(true);
      const approvalStats = await getApprovalStats();
      setStats(approvalStats);
    } catch (error) {
      console.error('Failed to load approval stats:', error);
    } finally {
      setStatsLoading(false);
    }
  };

  /**
   * Load stats on component mount
   */
  useEffect(() => {
    loadStats();
  }, [canManageApprovals]);

  /**
   * Handle approval processed (refresh stats)
   */
  const handleApprovalProcessed = () => {
    loadStats();
  };

  /**
   * Get tab configuration based on user role
   */
  const getTabs = () => {
    const baseTabs = [
      {
        id: 'my-requests' as TabType,
        name: 'My Requests',
        icon: (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        )
      }
    ];

    if (canManageApprovals) {
      baseTabs.push(
        {
          id: 'pending-approvals' as TabType,
          name: 'Pending Approvals',
          icon: (
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          ),
          badge: stats?.pendingRequests || 0
        },
        {
          id: 'all-history' as TabType,
          name: 'All History',
          icon: (
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
          )
        }
      );
    }

    return baseTabs;
  };

  /**
   * Set default tab based on user role
   */
  useEffect(() => {
    if (canManageApprovals && stats?.pendingRequests && stats.pendingRequests > 0) {
      setActiveTab('pending-approvals');
    }
  }, [canManageApprovals, stats]);

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-50 py-6">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-gray-900">Approval Management</h1>
            <p className="mt-1 text-gray-600">
              {canManageApprovals
                ? 'Manage approval requests and review team submissions'
                : 'View and track your approval requests'
              }
            </p>
          </div>

          {/* Statistics Cards (for managers/admins) */}
          {canManageApprovals && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-4">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">Total Requests</dt>
                        <dd className="text-lg font-medium text-gray-900">
                          {statsLoading ? '...' : stats?.totalRequests || 0}
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-4">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">Pending</dt>
                        <dd className="text-lg font-medium text-yellow-600">
                          {statsLoading ? '...' : stats?.pendingRequests || 0}
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-4">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">Approved</dt>
                        <dd className="text-lg font-medium text-green-600">
                          {statsLoading ? '...' : stats?.approvedRequests || 0}
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-4">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">Rejected</dt>
                        <dd className="text-lg font-medium text-red-600">
                          {statsLoading ? '...' : stats?.rejectedRequests || 0}
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Tab Navigation */}
          <div className="bg-white shadow rounded-lg">
            <div className="border-b border-gray-200">
              <nav className="-mb-px flex space-x-8 px-6" aria-label="Tabs">
                {getTabs().map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`${
                      activeTab === tab.id
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2`}
                  >
                    {tab.icon}
                    <span>{tab.name}</span>
                    {tab.badge !== undefined && tab.badge > 0 && (
                      <span className="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                        {tab.badge}
                      </span>
                    )}
                  </button>
                ))}
              </nav>
            </div>

            {/* Tab Content */}
            <div className="p-6">
              {activeTab === 'my-requests' && (
                <ApprovalHistory 
                  viewMode="my-requests" 
                  showFilters={true}
                />
              )}

              {activeTab === 'pending-approvals' && canManageApprovals && (
                <ManagerApprovalInterface 
                  onApprovalProcessed={handleApprovalProcessed}
                />
              )}

              {activeTab === 'all-history' && canManageApprovals && (
                <ApprovalHistory 
                  viewMode="all-approvals" 
                  showFilters={true}
                />
              )}
            </div>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
