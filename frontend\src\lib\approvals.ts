/**
 * Approval API service functions
 * Handles all approval-related API calls
 */

import { getAccessToken } from './auth';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';

/**
 * Approval request interface
 */
export interface ApprovalRequest {
  id: number;
  timeLogId: number;
  requestedBy: number;
  assignedTo: number;
  requestType: 'correction' | 'overtime' | 'leave';
  requestData: any;
  reason: string;
  status: 'pending' | 'approved' | 'rejected';
  comments?: string;
  approvedBy?: number;
  approvedAt?: string;
  createdAt: string;
  updatedAt?: string;
  // Populated fields
  requesterName?: string;
  requesterEmail?: string;
  approverName?: string;
  approverEmail?: string;
  timeLog?: any;
}

/**
 * Create approval request data interface
 */
export interface CreateApprovalData {
  timeLogId: number;
  requestType: 'correction' | 'overtime' | 'leave';
  requestData: any;
  reason: string;
}

/**
 * Process approval data interface
 */
export interface ProcessApprovalData {
  status: 'approved' | 'rejected';
  comments?: string;
}

/**
 * Approval filters interface
 */
export interface ApprovalFilters {
  page?: number;
  limit?: number;
  status?: 'pending' | 'approved' | 'rejected';
  requestType?: 'correction' | 'overtime' | 'leave';
  startDate?: string;
  endDate?: string;
  requestedBy?: number;
  assignedTo?: number;
}

/**
 * Approval statistics interface
 */
export interface ApprovalStats {
  totalRequests: number;
  pendingRequests: number;
  approvedRequests: number;
  rejectedRequests: number;
  averageProcessingTime: number;
  requestsByType: {
    correction: number;
    overtime: number;
    leave: number;
  };
}

/**
 * API response wrapper interface
 */
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  code?: string;
}

/**
 * Paginated response interface
 */
interface PaginatedResponse<T> {
  success: boolean;
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

/**
 * Make authenticated API request
 */
async function makeRequest<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  const token = getAccessToken();
  
  const response = await fetch(`${API_BASE_URL}${endpoint}`, {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
      ...options.headers,
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
  }

  return response.json();
}

/**
 * Create a new approval request
 */
export async function createApprovalRequest(data: CreateApprovalData): Promise<ApprovalRequest> {
  const response = await makeRequest<ApiResponse<ApprovalRequest>>('/approvals', {
    method: 'POST',
    body: JSON.stringify(data),
  });

  if (!response.success || !response.data) {
    throw new Error(response.error || 'Failed to create approval request');
  }

  return response.data;
}

/**
 * Get my approval requests
 */
export async function getMyApprovalRequests(filters: ApprovalFilters = {}): Promise<{
  requests: ApprovalRequest[];
  pagination: any;
}> {
  const queryParams = new URLSearchParams();
  
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      queryParams.append(key, value.toString());
    }
  });

  const response = await makeRequest<PaginatedResponse<ApprovalRequest>>(
    `/approvals/my-requests?${queryParams.toString()}`
  );

  if (!response.success) {
    throw new Error('Failed to fetch approval requests');
  }

  return {
    requests: response.data,
    pagination: response.pagination,
  };
}

/**
 * Get pending approvals (manager/admin only)
 */
export async function getPendingApprovals(filters: ApprovalFilters = {}): Promise<{
  requests: ApprovalRequest[];
  pagination: any;
}> {
  const queryParams = new URLSearchParams();
  
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      queryParams.append(key, value.toString());
    }
  });

  const response = await makeRequest<PaginatedResponse<ApprovalRequest>>(
    `/approvals/pending?${queryParams.toString()}`
  );

  if (!response.success) {
    throw new Error('Failed to fetch pending approvals');
  }

  return {
    requests: response.data,
    pagination: response.pagination,
  };
}

/**
 * Get all approvals with filtering
 */
export async function getApprovals(filters: ApprovalFilters = {}): Promise<{
  requests: ApprovalRequest[];
  pagination: any;
}> {
  const queryParams = new URLSearchParams();
  
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      queryParams.append(key, value.toString());
    }
  });

  const response = await makeRequest<PaginatedResponse<ApprovalRequest>>(
    `/approvals?${queryParams.toString()}`
  );

  if (!response.success) {
    throw new Error('Failed to fetch approvals');
  }

  return {
    requests: response.data,
    pagination: response.pagination,
  };
}

/**
 * Get approval by ID
 */
export async function getApprovalById(id: number): Promise<ApprovalRequest> {
  const response = await makeRequest<ApiResponse<ApprovalRequest>>(`/approvals/${id}`);

  if (!response.success || !response.data) {
    throw new Error(response.error || 'Failed to fetch approval');
  }

  return response.data;
}

/**
 * Process approval (approve/reject) - manager/admin only
 */
export async function processApproval(
  id: number,
  data: ProcessApprovalData
): Promise<ApprovalRequest> {
  const response = await makeRequest<ApiResponse<ApprovalRequest>>(`/approvals/${id}`, {
    method: 'PUT',
    body: JSON.stringify(data),
  });

  if (!response.success || !response.data) {
    throw new Error(response.error || 'Failed to process approval');
  }

  return response.data;
}

/**
 * Get approval statistics (admin/manager only)
 */
export async function getApprovalStats(filters: {
  startDate?: string;
  endDate?: string;
} = {}): Promise<ApprovalStats> {
  const queryParams = new URLSearchParams();
  
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      queryParams.append(key, value.toString());
    }
  });

  const response = await makeRequest<ApiResponse<ApprovalStats>>(
    `/approvals/stats?${queryParams.toString()}`
  );

  if (!response.success || !response.data) {
    throw new Error(response.error || 'Failed to fetch approval statistics');
  }

  return response.data;
}

/**
 * Utility functions
 */

/**
 * Get status badge color for approval status
 */
export function getStatusBadgeColor(status: string): string {
  switch (status) {
    case 'pending':
      return 'bg-yellow-100 text-yellow-800';
    case 'approved':
      return 'bg-green-100 text-green-800';
    case 'rejected':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
}

/**
 * Get request type display name
 */
export function getRequestTypeDisplayName(type: string): string {
  switch (type) {
    case 'correction':
      return 'Time Correction';
    case 'overtime':
      return 'Overtime Request';
    case 'leave':
      return 'Leave Request';
    default:
      return type;
  }
}

/**
 * Format approval request data for display
 */
export function formatRequestData(requestType: string, requestData: any): string {
  try {
    switch (requestType) {
      case 'correction':
        return `Clock In: ${requestData.clockIn || 'N/A'}, Clock Out: ${requestData.clockOut || 'N/A'}`;
      case 'overtime':
        return `Hours: ${requestData.hours || 'N/A'}, Date: ${requestData.date || 'N/A'}`;
      case 'leave':
        return `From: ${requestData.startDate || 'N/A'}, To: ${requestData.endDate || 'N/A'}`;
      default:
        return JSON.stringify(requestData);
    }
  } catch {
    return 'Invalid request data';
  }
}
