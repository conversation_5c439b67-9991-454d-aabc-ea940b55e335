/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/auth/login/page";
exports.ids = ["app/auth/login/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Flogin%2Fpage&page=%2Fauth%2Flogin%2Fpage&appPaths=%2Fauth%2Flogin%2Fpage&pagePath=private-next-app-dir%2Fauth%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5Cijureidini%5CWorkspace%5Cflexair_timekeeping_app%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cijureidini%5CWorkspace%5Cflexair_timekeeping_app%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Flogin%2Fpage&page=%2Fauth%2Flogin%2Fpage&appPaths=%2Fauth%2Flogin%2Fpage&pagePath=private-next-app-dir%2Fauth%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5Cijureidini%5CWorkspace%5Cflexair_timekeeping_app%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cijureidini%5CWorkspace%5Cflexair_timekeeping_app%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?41d8\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/../node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/../node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'auth',\n        {\n        children: [\n        'login',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/auth/login/page.tsx */ \"(rsc)/./src/app/auth/login/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/../node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/auth/login/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/auth/login/page\",\n        pathname: \"/auth/login\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1hcHAtbG9hZGVyLmpzP25hbWU9YXBwJTJGYXV0aCUyRmxvZ2luJTJGcGFnZSZwYWdlPSUyRmF1dGglMkZsb2dpbiUyRnBhZ2UmYXBwUGF0aHM9JTJGYXV0aCUyRmxvZ2luJTJGcGFnZSZwYWdlUGF0aD1wcml2YXRlLW5leHQtYXBwLWRpciUyRmF1dGglMkZsb2dpbiUyRnBhZ2UudHN4JmFwcERpcj1DJTNBJTVDVXNlcnMlNUNpanVyZWlkaW5pJTVDV29ya3NwYWNlJTVDZmxleGFpcl90aW1la2VlcGluZ19hcHAlNUNmcm9udGVuZCU1Q3NyYyU1Q2FwcCZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzJnJvb3REaXI9QyUzQSU1Q1VzZXJzJTVDaWp1cmVpZGluaSU1Q1dvcmtzcGFjZSU1Q2ZsZXhhaXJfdGltZWtlZXBpbmdfYXBwJTVDZnJvbnRlbmQmaXNEZXY9dHJ1ZSZ0c2NvbmZpZ1BhdGg9dHNjb25maWcuanNvbiZiYXNlUGF0aD0mYXNzZXRQcmVmaXg9Jm5leHRDb25maWdPdXRwdXQ9JnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSxhQUFhLHNCQUFzQjtBQUNpRTtBQUNyQztBQUMvRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQ0FBaUM7QUFDakMsdUJBQXVCLHNLQUF5STtBQUNoSztBQUNBLFNBQVM7QUFDVCxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0EseUJBQXlCLG9KQUE4SDtBQUN2SixvQkFBb0IsMk5BQWdGO0FBQ3BHO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUN1QjtBQUM2RDtBQUNwRiw2QkFBNkIsbUJBQW1CO0FBQ2hEO0FBQ087QUFDQTtBQUNQO0FBQ0E7QUFDQTtBQUN1RDtBQUN2RDtBQUNPLHdCQUF3Qiw4R0FBa0I7QUFDakQ7QUFDQSxjQUFjLHlFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxDQUFDOztBQUVEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZmxleGFpci1mcm9udGVuZC8/M2ZmZCJdLCJzb3VyY2VzQ29udGVudCI6WyJcIlRVUkJPUEFDSyB7IHRyYW5zaXRpb246IG5leHQtc3NyIH1cIjtcbmltcG9ydCB7IEFwcFBhZ2VSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1tb2R1bGVzL2FwcC1wYWdlL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLWtpbmRcIjtcbi8vIFdlIGluamVjdCB0aGUgdHJlZSBhbmQgcGFnZXMgaGVyZSBzbyB0aGF0IHdlIGNhbiB1c2UgdGhlbSBpbiB0aGUgcm91dGVcbi8vIG1vZHVsZS5cbmNvbnN0IHRyZWUgPSB7XG4gICAgICAgIGNoaWxkcmVuOiBbXG4gICAgICAgICcnLFxuICAgICAgICB7XG4gICAgICAgIGNoaWxkcmVuOiBbXG4gICAgICAgICdhdXRoJyxcbiAgICAgICAge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAnbG9naW4nLFxuICAgICAgICB7XG4gICAgICAgIGNoaWxkcmVuOiBbJ19fUEFHRV9fJywge30sIHtcbiAgICAgICAgICBwYWdlOiBbKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxpanVyZWlkaW5pXFxcXFdvcmtzcGFjZVxcXFxmbGV4YWlyX3RpbWVrZWVwaW5nX2FwcFxcXFxmcm9udGVuZFxcXFxzcmNcXFxcYXBwXFxcXGF1dGhcXFxcbG9naW5cXFxccGFnZS50c3hcIiksIFwiQzpcXFxcVXNlcnNcXFxcaWp1cmVpZGluaVxcXFxXb3Jrc3BhY2VcXFxcZmxleGFpcl90aW1la2VlcGluZ19hcHBcXFxcZnJvbnRlbmRcXFxcc3JjXFxcXGFwcFxcXFxhdXRoXFxcXGxvZ2luXFxcXHBhZ2UudHN4XCJdLFxuICAgICAgICAgIFxuICAgICAgICB9XVxuICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICBcbiAgICAgICAgXG4gICAgICB9XG4gICAgICBdXG4gICAgICB9LFxuICAgICAgICB7XG4gICAgICAgIFxuICAgICAgICBcbiAgICAgIH1cbiAgICAgIF1cbiAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgJ2xheW91dCc6IFsoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXGlqdXJlaWRpbmlcXFxcV29ya3NwYWNlXFxcXGZsZXhhaXJfdGltZWtlZXBpbmdfYXBwXFxcXGZyb250ZW5kXFxcXHNyY1xcXFxhcHBcXFxcbGF5b3V0LnRzeFwiKSwgXCJDOlxcXFxVc2Vyc1xcXFxpanVyZWlkaW5pXFxcXFdvcmtzcGFjZVxcXFxmbGV4YWlyX3RpbWVrZWVwaW5nX2FwcFxcXFxmcm9udGVuZFxcXFxzcmNcXFxcYXBwXFxcXGxheW91dC50c3hcIl0sXG4nbm90LWZvdW5kJzogWygpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL25vdC1mb3VuZC1lcnJvclwiKSwgXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbm90LWZvdW5kLWVycm9yXCJdLFxuICAgICAgICBcbiAgICAgIH1cbiAgICAgIF1cbiAgICAgIH0uY2hpbGRyZW47XG5jb25zdCBwYWdlcyA9IFtcIkM6XFxcXFVzZXJzXFxcXGlqdXJlaWRpbmlcXFxcV29ya3NwYWNlXFxcXGZsZXhhaXJfdGltZWtlZXBpbmdfYXBwXFxcXGZyb250ZW5kXFxcXHNyY1xcXFxhcHBcXFxcYXV0aFxcXFxsb2dpblxcXFxwYWdlLnRzeFwiXTtcbmV4cG9ydCB7IHRyZWUsIHBhZ2VzIH07XG5leHBvcnQgeyBkZWZhdWx0IGFzIEdsb2JhbEVycm9yIH0gZnJvbSBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9lcnJvci1ib3VuZGFyeVwiO1xuY29uc3QgX19uZXh0X2FwcF9yZXF1aXJlX18gPSBfX3dlYnBhY2tfcmVxdWlyZV9fXG5jb25zdCBfX25leHRfYXBwX2xvYWRfY2h1bmtfXyA9ICgpID0+IFByb21pc2UucmVzb2x2ZSgpXG5leHBvcnQgY29uc3Qgb3JpZ2luYWxQYXRobmFtZSA9IFwiL2F1dGgvbG9naW4vcGFnZVwiO1xuZXhwb3J0IGNvbnN0IF9fbmV4dF9hcHBfXyA9IHtcbiAgICByZXF1aXJlOiBfX25leHRfYXBwX3JlcXVpcmVfXyxcbiAgICBsb2FkQ2h1bms6IF9fbmV4dF9hcHBfbG9hZF9jaHVua19fXG59O1xuZXhwb3J0ICogZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvYXBwLXJlbmRlci9lbnRyeS1iYXNlXCI7XG4vLyBDcmVhdGUgYW5kIGV4cG9ydCB0aGUgcm91dGUgbW9kdWxlIHRoYXQgd2lsbCBiZSBjb25zdW1lZC5cbmV4cG9ydCBjb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBBcHBQYWdlUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLkFQUF9QQUdFLFxuICAgICAgICBwYWdlOiBcIi9hdXRoL2xvZ2luL3BhZ2VcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL2F1dGgvbG9naW5cIixcbiAgICAgICAgLy8gVGhlIGZvbGxvd2luZyBhcmVuJ3QgdXNlZCBpbiBwcm9kdWN0aW9uLlxuICAgICAgICBidW5kbGVQYXRoOiBcIlwiLFxuICAgICAgICBmaWxlbmFtZTogXCJcIixcbiAgICAgICAgYXBwUGF0aHM6IFtdXG4gICAgfSxcbiAgICB1c2VybGFuZDoge1xuICAgICAgICBsb2FkZXJUcmVlOiB0cmVlXG4gICAgfVxufSk7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwcC1wYWdlLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Flogin%2Fpage&page=%2Fauth%2Flogin%2Fpage&appPaths=%2Fauth%2Flogin%2Fpage&pagePath=private-next-app-dir%2Fauth%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5Cijureidini%5CWorkspace%5Cflexair_timekeeping_app%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cijureidini%5CWorkspace%5Cflexair_timekeeping_app%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cauth%5C%5CLoginForm.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cauth%5C%5CLoginForm.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/auth/LoginForm.tsx */ \"(ssr)/./src/components/auth/LoginForm.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNpanVyZWlkaW5pJTVDJTVDV29ya3NwYWNlJTVDJTVDZmxleGFpcl90aW1la2VlcGluZ19hcHAlNUMlNUNmcm9udGVuZCU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUNhdXRoJTVDJTVDTG9naW5Gb3JtLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtMQUEySyIsInNvdXJjZXMiOlsid2VicGFjazovL2ZsZXhhaXItZnJvbnRlbmQvPzA2OWIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiQzpcXFxcVXNlcnNcXFxcaWp1cmVpZGluaVxcXFxXb3Jrc3BhY2VcXFxcZmxleGFpcl90aW1la2VlcGluZ19hcHBcXFxcZnJvbnRlbmRcXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcYXV0aFxcXFxMb2dpbkZvcm0udHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cauth%5C%5CLoginForm.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(ssr)/./src/contexts/AuthContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNpanVyZWlkaW5pJTVDJTVDV29ya3NwYWNlJTVDJTVDZmxleGFpcl90aW1la2VlcGluZ19hcHAlNUMlNUNmcm9udGVuZCU1QyU1Q3NyYyU1QyU1Q2NvbnRleHRzJTVDJTVDQXV0aENvbnRleHQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQXV0aFByb3ZpZGVyJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2lqdXJlaWRpbmklNUMlNUNXb3Jrc3BhY2UlNUMlNUNmbGV4YWlyX3RpbWVrZWVwaW5nX2FwcCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNmb250JTVDJTVDZ29vZ2xlJTVDJTVDdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJzcmMlNUMlNUMlNUMlNUNhcHAlNUMlNUMlNUMlNUNsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIySW50ZXIlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJpbnRlciU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNpanVyZWlkaW5pJTVDJTVDV29ya3NwYWNlJTVDJTVDZmxleGFpcl90aW1la2VlcGluZ19hcHAlNUMlNUNmcm9udGVuZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3S0FBMEsiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mbGV4YWlyLWZyb250ZW5kLz82NjM5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiQXV0aFByb3ZpZGVyXCJdICovIFwiQzpcXFxcVXNlcnNcXFxcaWp1cmVpZGluaVxcXFxXb3Jrc3BhY2VcXFxcZmxleGFpcl90aW1la2VlcGluZ19hcHBcXFxcZnJvbnRlbmRcXFxcc3JjXFxcXGNvbnRleHRzXFxcXEF1dGhDb250ZXh0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/app-router.js */ \"(ssr)/../node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/client-page.js */ \"(ssr)/../node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/../node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/../node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/../node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/../node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/auth/LoginForm.tsx":
/*!*******************************************!*\
  !*** ./src/components/auth/LoginForm.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/../node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/../node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/**\r\n * Login Form Component\r\n * Provides user authentication interface with form validation\r\n */ /* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n/**\r\n * Login form component with validation and error handling\r\n * @param redirectTo - URL to redirect to after successful login\r\n * @param onSuccess - Callback function called on successful login\r\n */ function LoginForm({ redirectTo = \"/dashboard\", onSuccess }) {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { login, loading, error, clearError } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        email: \"\",\n        password: \"\",\n        rememberMe: false\n    });\n    const [validationErrors, setValidationErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    /**\r\n   * Handle input field changes\r\n   * @param e - Change event from input field\r\n   */ const handleChange = (e)=>{\n        const { name, value, type, checked } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: type === \"checkbox\" ? checked : value\n            }));\n        // Clear validation error when user starts typing\n        if (validationErrors[name]) {\n            setValidationErrors((prev)=>({\n                    ...prev,\n                    [name]: undefined\n                }));\n        }\n        // Clear auth error when user makes changes\n        if (error) {\n            clearError();\n        }\n    };\n    /**\r\n   * Validate form data\r\n   * @returns True if form is valid\r\n   */ const validateForm = ()=>{\n        const errors = {};\n        if (!formData.email) {\n            errors.email = \"Email is required\";\n        } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n            errors.email = \"Please enter a valid email address\";\n        }\n        if (!formData.password) {\n            errors.password = \"Password is required\";\n        } else if (formData.password.length < 6) {\n            errors.password = \"Password must be at least 6 characters\";\n        }\n        setValidationErrors(errors);\n        return Object.keys(errors).length === 0;\n    };\n    /**\r\n   * Handle form submission\r\n   * @param e - Form submit event\r\n   */ const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            return;\n        }\n        try {\n            await login(formData);\n            if (onSuccess) {\n                onSuccess();\n            } else {\n                router.push(redirectTo);\n            }\n        } catch (error) {\n            // Error is handled by the auth context\n            console.error(\"Login failed:\", error);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50 py-8 px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"mt-4 text-center text-2xl font-extrabold text-gray-900\",\n                            children: \"Sign in to your account\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-center text-sm text-gray-600\",\n                            children: \"Welcome back to Flexair Timekeeping\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                    lineNumber: 116,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    className: \"mt-8 space-y-6\",\n                    onSubmit: handleSubmit,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"rounded-md shadow-sm -space-y-px\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"email\",\n                                            className: \"sr-only\",\n                                            children: \"Email address\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"email\",\n                                            name: \"email\",\n                                            type: \"email\",\n                                            autoComplete: \"email\",\n                                            required: true,\n                                            value: formData.email,\n                                            onChange: handleChange,\n                                            className: `appearance-none rounded-none relative block w-full px-3 py-2 border ${validationErrors.email ? \"border-red-300 text-red-900 placeholder-red-300 focus:ring-red-500 focus:border-red-500\" : \"border-gray-300 placeholder-gray-500 text-gray-900 focus:ring-indigo-500 focus:border-indigo-500\"} rounded-t-md focus:outline-none focus:z-10 sm:text-sm`,\n                                            placeholder: \"Email address\",\n                                            disabled: loading\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 15\n                                        }, this),\n                                        validationErrors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-red-600\",\n                                            children: validationErrors.email\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"password\",\n                                            className: \"sr-only\",\n                                            children: \"Password\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"password\",\n                                            name: \"password\",\n                                            type: \"password\",\n                                            autoComplete: \"current-password\",\n                                            required: true,\n                                            value: formData.password,\n                                            onChange: handleChange,\n                                            className: `appearance-none rounded-none relative block w-full px-3 py-2 border ${validationErrors.password ? \"border-red-300 text-red-900 placeholder-red-300 focus:ring-red-500 focus:border-red-500\" : \"border-gray-300 placeholder-gray-500 text-gray-900 focus:ring-indigo-500 focus:border-indigo-500\"} rounded-b-md focus:outline-none focus:z-10 sm:text-sm`,\n                                            placeholder: \"Password\",\n                                            disabled: loading\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 15\n                                        }, this),\n                                        validationErrors.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-red-600\",\n                                            children: validationErrors.password\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"rememberMe\",\n                                            name: \"rememberMe\",\n                                            type: \"checkbox\",\n                                            checked: formData.rememberMe,\n                                            onChange: handleChange,\n                                            className: \"h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded\",\n                                            disabled: loading\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"rememberMe\",\n                                            className: \"ml-2 block text-sm text-gray-900\",\n                                            children: \"Remember me\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/auth/forgot-password\",\n                                        className: \"font-medium text-indigo-600 hover:text-indigo-500\",\n                                        children: \"Forgot your password?\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 11\n                        }, this),\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"rounded-md bg-red-50 p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-red-800\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                disabled: loading,\n                                className: \"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:bg-gray-400 disabled:cursor-not-allowed\",\n                                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"animate-spin -ml-1 mr-3 h-5 w-5 text-white\",\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            fill: \"none\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                    className: \"opacity-25\",\n                                                    cx: \"12\",\n                                                    cy: \"12\",\n                                                    r: \"10\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    className: \"opacity-75\",\n                                                    fill: \"currentColor\",\n                                                    d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                                    lineNumber: 220,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"Signing in...\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 17\n                                }, this) : \"Sign in\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                            lineNumber: 210,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600\",\n                                children: [\n                                    \"Don't have an account?\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/auth/register\",\n                                        className: \"font-medium text-indigo-600 hover:text-indigo-500\",\n                                        children: \"Sign up here\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n            lineNumber: 115,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n        lineNumber: 114,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/auth/LoginForm.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(ssr)/./src/lib/auth.ts\");\n/**\r\n * Authentication Context Provider\r\n * Manages global authentication state and provides auth methods\r\n */ /* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n/**\r\n * Authentication Provider Component\r\n * Wraps the app and provides authentication state and methods\r\n * @param children - Child components to render\r\n */ function AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    /**\r\n   * Clear any authentication errors\r\n   */ const clearError = ()=>setError(null);\n    /**\r\n   * Initialize authentication state on mount\r\n   */ (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        initializeAuth();\n    }, []);\n    /**\r\n   * Initialize authentication state\r\n   * Checks for existing tokens and validates them\r\n   */ const initializeAuth = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            if (!(0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.isAuthenticated)()) {\n                setLoading(false);\n                return;\n            }\n            const token = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.getAccessToken)();\n            if (!token || (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.isTokenExpired)(token)) {\n                // Try to refresh token\n                try {\n                    await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.refreshTokens)();\n                } catch (error) {\n                    (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.clearTokens)();\n                    setLoading(false);\n                    return;\n                }\n            }\n            // Verify token and get user data\n            const { valid, user: userData } = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.verifyToken)();\n            if (valid && userData) {\n                setUser(userData);\n            } else {\n                (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.clearTokens)();\n            }\n        } catch (error) {\n            console.error(\"Auth initialization error:\", error);\n            (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.clearTokens)();\n        } finally{\n            setLoading(false);\n        }\n    };\n    /**\r\n   * Login user with credentials\r\n   * @param credentials - Login credentials\r\n   */ const login = async (credentials)=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const response = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.login)(credentials);\n            setUser(response.user);\n        } catch (error) {\n            setError(error.message || \"Login failed\");\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    /**\r\n   * Register new user\r\n   * @param userData - Registration data\r\n   */ const register = async (userData)=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const response = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.register)(userData);\n            setUser(response.user);\n        } catch (error) {\n            setError(error.message || \"Registration failed\");\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    /**\r\n   * Logout current user\r\n   */ const logout = async ()=>{\n        try {\n            setLoading(true);\n            await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.logout)();\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n        } finally{\n            setUser(null);\n            setLoading(false);\n        }\n    };\n    /**\r\n   * Refresh current user data\r\n   */ const refreshUser = async ()=>{\n        try {\n            const userData = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.getCurrentUser)();\n            setUser(userData);\n        } catch (error) {\n            console.error(\"Failed to refresh user data:\", error);\n        }\n    };\n    const value = {\n        user,\n        loading,\n        error,\n        isAuthenticated: !!user,\n        login,\n        register,\n        logout,\n        clearError,\n        refreshUser\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 186,\n        columnNumber: 5\n    }, this);\n}\n/**\r\n * Hook to use authentication context\r\n * @returns Authentication context\r\n * @throws Error if used outside AuthProvider\r\n */ function useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiRequest: () => (/* binding */ apiRequest),\n/* harmony export */   clearTokens: () => (/* binding */ clearTokens),\n/* harmony export */   decodeToken: () => (/* binding */ decodeToken),\n/* harmony export */   getAccessToken: () => (/* binding */ getAccessToken),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   getRefreshToken: () => (/* binding */ getRefreshToken),\n/* harmony export */   isAuthenticated: () => (/* binding */ isAuthenticated),\n/* harmony export */   isTokenExpired: () => (/* binding */ isTokenExpired),\n/* harmony export */   login: () => (/* binding */ login),\n/* harmony export */   logout: () => (/* binding */ logout),\n/* harmony export */   refreshTokens: () => (/* binding */ refreshTokens),\n/* harmony export */   register: () => (/* binding */ register),\n/* harmony export */   setTokens: () => (/* binding */ setTokens),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/**\r\n * Authentication utilities and API calls\r\n * Handles login, registration, token management, and user session\r\n */ const API_BASE = \"http://localhost:5002/api\" || 0;\n/**\r\n * API response wrapper for error handling\r\n */ class ApiError extends Error {\n    constructor(message, status, code){\n        super(message);\n        this.status = status;\n        this.code = code;\n        this.name = \"ApiError\";\n    }\n}\n/**\r\n * Make authenticated API request with automatic token refresh\r\n * @param url - API endpoint URL\r\n * @param options - Fetch options\r\n * @returns Response data\r\n */ async function apiRequest(url, options = {}) {\n    const token = getAccessToken();\n    const config = {\n        headers: {\n            \"Content-Type\": \"application/json\",\n            ...token && {\n                Authorization: `Bearer ${token}`\n            },\n            ...options.headers\n        },\n        ...options\n    };\n    const response = await fetch(`${API_BASE}${url}`, config);\n    if (!response.ok) {\n        const errorData = await response.json().catch(()=>({}));\n        throw new ApiError(errorData.error?.message || \"Request failed\", response.status, errorData.error?.code);\n    }\n    return response.json();\n}\n/**\r\n * Login user with email and password\r\n * @param credentials - Login credentials\r\n * @returns Authentication response with user data and tokens\r\n */ async function login(credentials) {\n    const response = await apiRequest(\"/auth/login\", {\n        method: \"POST\",\n        body: JSON.stringify(credentials)\n    });\n    // Store tokens\n    setTokens(response.tokens);\n    return response;\n}\n/**\r\n * Register new user account\r\n * @param userData - Registration data\r\n * @returns Authentication response with user data and tokens\r\n */ async function register(userData) {\n    const response = await apiRequest(\"/auth/register\", {\n        method: \"POST\",\n        body: JSON.stringify(userData)\n    });\n    // Store tokens\n    setTokens(response.tokens);\n    return response;\n}\n/**\r\n * Logout user and clear tokens\r\n */ async function logout() {\n    const refreshToken = getRefreshToken();\n    try {\n        await apiRequest(\"/auth/logout\", {\n            method: \"POST\",\n            body: JSON.stringify({\n                refreshToken\n            })\n        });\n    } catch (error) {\n        // Continue with logout even if API call fails\n        console.warn(\"Logout API call failed:\", error);\n    }\n    clearTokens();\n}\n/**\r\n * Get current user profile\r\n * @returns Current user data\r\n */ async function getCurrentUser() {\n    const response = await apiRequest(\"/auth/profile\");\n    return response.user;\n}\n/**\r\n * Verify if current token is valid\r\n * @returns Token validity and user data\r\n */ async function verifyToken() {\n    try {\n        const response = await apiRequest(\"/auth/verify\");\n        return {\n            valid: response.valid,\n            user: response.user\n        };\n    } catch (error) {\n        return {\n            valid: false\n        };\n    }\n}\n/**\r\n * Refresh access token using refresh token\r\n * @returns New authentication tokens\r\n */ async function refreshTokens() {\n    const refreshToken = getRefreshToken();\n    if (!refreshToken) {\n        throw new Error(\"No refresh token available\");\n    }\n    const response = await fetch(`${API_BASE}/auth/refresh`, {\n        method: \"POST\",\n        headers: {\n            \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n            refreshToken\n        })\n    });\n    if (!response.ok) {\n        clearTokens();\n        throw new Error(\"Token refresh failed\");\n    }\n    const data = await response.json();\n    setTokens(data.tokens);\n    return data.tokens;\n}\n/**\r\n * Store authentication tokens in localStorage\r\n * @param tokens - Authentication tokens to store\r\n */ function setTokens(tokens) {\n    if (false) {}\n}\n/**\r\n * Get stored access token\r\n * @returns Access token or null\r\n */ function getAccessToken() {\n    if (false) {}\n    return null;\n}\n/**\r\n * Get stored refresh token\r\n * @returns Refresh token or null\r\n */ function getRefreshToken() {\n    if (false) {}\n    return null;\n}\n/**\r\n * Clear all stored authentication tokens\r\n */ function clearTokens() {\n    if (false) {}\n}\n/**\r\n * Check if user is currently authenticated\r\n * @returns True if user has valid tokens\r\n */ function isAuthenticated() {\n    return !!(getAccessToken() && getRefreshToken());\n}\n/**\r\n * Decode JWT token payload without verification\r\n * @param token - JWT token to decode\r\n * @returns Decoded payload or null\r\n */ function decodeToken(token) {\n    try {\n        const payload = token.split(\".\")[1];\n        return JSON.parse(atob(payload));\n    } catch (error) {\n        return null;\n    }\n}\n/**\r\n * Check if token is expired\r\n * @param token - JWT token to check\r\n * @returns True if token is expired\r\n */ function isTokenExpired(token) {\n    const decoded = decodeToken(token);\n    if (!decoded || !decoded.exp) return true;\n    return Date.now() >= decoded.exp * 1000;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"347b41cc8b5b\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZmxleGFpci1mcm9udGVuZC8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/MTc0ZCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjM0N2I0MWNjOGI1YlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/auth/login/page.tsx":
/*!*************************************!*\
  !*** ./src/app/auth/login/page.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginPage),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_auth_LoginForm__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/auth/LoginForm */ \"(rsc)/./src/components/auth/LoginForm.tsx\");\n/**\r\n * Login Page\r\n * Handles user authentication\r\n */ \n\n\nconst metadata = {\n    title: \"Login - Flexair Timekeeping\",\n    description: \"Sign in to your Flexair Timekeeping account\"\n};\n/**\r\n * Login page component\r\n */ function LoginPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_LoginForm__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n        lineNumber: 19,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2F1dGgvbG9naW4vcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUFBOzs7Q0FHQztBQUV5QjtBQUUwQjtBQUU3QyxNQUFNRSxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBRTtBQUVGOztDQUVDLEdBQ2MsU0FBU0M7SUFDdEIscUJBQU8sOERBQUNKLGtFQUFTQTs7Ozs7QUFDbkIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mbGV4YWlyLWZyb250ZW5kLy4vc3JjL2FwcC9hdXRoL2xvZ2luL3BhZ2UudHN4P2Y2ZjMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXHJcbiAqIExvZ2luIFBhZ2VcclxuICogSGFuZGxlcyB1c2VyIGF1dGhlbnRpY2F0aW9uXHJcbiAqL1xyXG5cclxuaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcclxuaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gJ25leHQnO1xyXG5pbXBvcnQgTG9naW5Gb3JtIGZyb20gJ0AvY29tcG9uZW50cy9hdXRoL0xvZ2luRm9ybSc7XHJcblxyXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xyXG4gIHRpdGxlOiAnTG9naW4gLSBGbGV4YWlyIFRpbWVrZWVwaW5nJyxcclxuICBkZXNjcmlwdGlvbjogJ1NpZ24gaW4gdG8geW91ciBGbGV4YWlyIFRpbWVrZWVwaW5nIGFjY291bnQnLFxyXG59O1xyXG5cclxuLyoqXHJcbiAqIExvZ2luIHBhZ2UgY29tcG9uZW50XHJcbiAqL1xyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBMb2dpblBhZ2UoKSB7XHJcbiAgcmV0dXJuIDxMb2dpbkZvcm0gLz47XHJcbn0iXSwibmFtZXMiOlsiUmVhY3QiLCJMb2dpbkZvcm0iLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJMb2dpblBhZ2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/auth/login/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/../node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(rsc)/./src/contexts/AuthContext.tsx\");\n/**\r\n * Root layout component for the Flexair Timekeeping App\r\n * Provides global styling, context providers, and common layout structure\r\n */ \n\n\n\nconst metadata = {\n    title: \"Flexair Timekeeping\",\n    description: \"Modern timekeeping application with biometric integration\",\n    keywords: [\n        \"timekeeping\",\n        \"attendance\",\n        \"biometric\",\n        \"HR\",\n        \"workforce management\"\n    ],\n    authors: [\n        {\n            name: \"Flexair Team\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\",\n    robots: \"noindex, nofollow\"\n};\n/**\r\n * Root layout component with authentication context\r\n * @param children - Child components to render\r\n */ function RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className)} h-full bg-gray-50 antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-full\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 34,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 33,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/auth/LoginForm.tsx":
/*!*******************************************!*\
  !*** ./src/components/auth/LoginForm.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ \"(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js\");\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\\Users\\<USER>\\Workspace\\flexair_timekeeping_app\\frontend\\src\\components\\auth\\LoginForm.tsx#default`));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/auth/LoginForm.tsx\n");

/***/ }),

/***/ "(rsc)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ e0),\n/* harmony export */   useAuth: () => (/* binding */ e1)\n/* harmony export */ });\n/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ \"(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js\");\n\n\nconst e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\\Users\\<USER>\\Workspace\\flexair_timekeeping_app\\frontend\\src\\contexts\\AuthContext.tsx#AuthProvider`);\n\nconst e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\\Users\\<USER>\\Workspace\\flexair_timekeeping_app\\frontend\\src\\contexts\\AuthContext.tsx#useAuth`);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/contexts/AuthContext.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Flogin%2Fpage&page=%2Fauth%2Flogin%2Fpage&appPaths=%2Fauth%2Flogin%2Fpage&pagePath=private-next-app-dir%2Fauth%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5Cijureidini%5CWorkspace%5Cflexair_timekeeping_app%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cijureidini%5CWorkspace%5Cflexair_timekeeping_app%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();