/**
 * Biometric integration service layer
 * Handles biometric template management and verification
 */

const { query } = require('../../shared/database');
const { logger } = require('../../shared/logger');

/**
 * Enroll biometric template for user
 * @param {number} userId - User ID
 * @param {Object} templateData - Template data
 * @returns {Object} Enrollment result
 */
const enrollBiometricTemplate = async (userId, templateData) => {
  try {
    const { templateData: template, templateType, quality = null } = templateData;

    // Validate template type
    if (!['fingerprint', 'face'].includes(templateType)) {
      throw new Error('Invalid template type. Must be fingerprint or face.');
    }

    // Check if user already has this type of template
    const existingTemplates = await query(
      'SELECT id FROM biometric_data WHERE user_id = ? AND template_type = ? AND used_for_verification = false',
      [userId, templateType]
    );

    // Remove old template if exists
    if (existingTemplates.length > 0) {
      await query(
        'DELETE FROM biometric_data WHERE user_id = ? AND template_type = ? AND used_for_verification = false',
        [userId, templateType]
      );
      logger.info(`Removed existing ${templateType} template for user ${userId}`);
    }

    // Store new template
    const result = await query(
      `INSERT INTO biometric_data (user_id, template_data, template_type, quality, used_for_verification, created_at)
       VALUES (?, ?, ?, ?, false, NOW())`,
      [userId, template, templateType, quality]
    );

    const templateId = result.insertId;

    // Log the enrollment
    await query(
      `INSERT INTO audit_logs (user_id, action, resource_type, resource_id, details)
       VALUES (?, 'ENROLL', 'biometric', ?, ?)`,
      [userId, templateId, JSON.stringify({ templateType, quality })]
    );

    logger.info(`Biometric template enrolled for user ${userId}, type: ${templateType}`);

    return {
      id: templateId,
      userId,
      templateType,
      quality,
      enrolledAt: new Date(),
      success: true
    };
  } catch (error) {
    logger.error('Error enrolling biometric template:', error);
    throw error;
  }
};

/**
 * Verify biometric template
 * @param {number} userId - User ID
 * @param {Object} verificationData - Verification data
 * @returns {Object} Verification result
 */
const verifyBiometricTemplate = async (userId, verificationData) => {
  try {
    const { templateData: template, templateType } = verificationData;

    // Get user's enrolled template
    const enrolledTemplates = await query(
      'SELECT id, template_data, quality FROM biometric_data WHERE user_id = ? AND template_type = ? AND used_for_verification = false ORDER BY created_at DESC LIMIT 1',
      [userId, templateType]
    );

    if (enrolledTemplates.length === 0) {
      return {
        success: false,
        verified: false,
        reason: 'No enrolled template found',
        confidence: 0
      };
    }

    const enrolledTemplate = enrolledTemplates[0];

    // In a real implementation, you would use actual biometric matching algorithms
    // For now, we'll simulate verification with basic string comparison
    const confidence = simulateBiometricMatching(enrolledTemplate.template_data, template);
    const verified = confidence >= 0.7; // 70% confidence threshold

    // Log verification attempt
    await query(
      `INSERT INTO audit_logs (user_id, action, resource_type, resource_id, details)
       VALUES (?, 'VERIFY', 'biometric', ?, ?)`,
      [userId, enrolledTemplate.id, JSON.stringify({ 
        templateType, 
        verified, 
        confidence: Math.round(confidence * 100) 
      })]
    );

    // Update biometric data usage
    if (verified) {
      await query(
        'UPDATE biometric_data SET last_used = NOW() WHERE id = ?',
        [enrolledTemplate.id]
      );
    }

    logger.info(`Biometric verification for user ${userId}: ${verified ? 'SUCCESS' : 'FAILED'} (${Math.round(confidence * 100)}%)`);

    return {
      success: true,
      verified,
      confidence: Math.round(confidence * 100),
      templateType,
      reason: verified ? 'Verification successful' : 'Biometric data does not match'
    };
  } catch (error) {
    logger.error('Error verifying biometric template:', error);
    throw error;
  }
};

/**
 * Simulate biometric matching (replace with real algorithm)
 * @param {string} enrolledTemplate - Enrolled template data
 * @param {string} verificationTemplate - Template to verify
 * @returns {number} Confidence score (0-1)
 */
const simulateBiometricMatching = (enrolledTemplate, verificationTemplate) => {
  // This is a simple simulation - in reality you'd use sophisticated algorithms
  // like minutiae matching for fingerprints or facial feature comparison
  
  if (!enrolledTemplate || !verificationTemplate) {
    return 0;
  }

  // Simple similarity check based on string comparison
  const enrolled = enrolledTemplate.toLowerCase();
  const verification = verificationTemplate.toLowerCase();

  if (enrolled === verification) {
    return 1.0; // Perfect match
  }

  // Calculate similarity based on common characters
  const commonChars = enrolled.split('').filter(char => verification.includes(char)).length;
  const maxLength = Math.max(enrolled.length, verification.length);
  
  // Add some randomness to simulate real-world variation
  const baseConfidence = commonChars / maxLength;
  const randomFactor = (Math.random() - 0.5) * 0.2; // ±10% variation
  
  return Math.max(0, Math.min(1, baseConfidence + randomFactor));
};

/**
 * Get user's biometric templates
 * @param {number} userId - User ID
 * @returns {Array} User's biometric templates
 */
const getUserBiometricTemplates = async (userId) => {
  try {
    const templates = await query(
      `SELECT 
        id, template_type, quality, last_used, created_at,
        CASE WHEN template_data IS NOT NULL THEN true ELSE false END as has_template
      FROM biometric_data 
      WHERE user_id = ? AND used_for_verification = false
      ORDER BY created_at DESC`,
      [userId]
    );

    return templates;
  } catch (error) {
    logger.error('Error getting user biometric templates:', error);
    throw new Error('Failed to retrieve biometric templates');
  }
};

/**
 * Delete biometric template
 * @param {number} userId - User ID
 * @param {string} templateType - Template type to delete
 * @returns {boolean} Success status
 */
const deleteBiometricTemplate = async (userId, templateType) => {
  try {
    const result = await query(
      'DELETE FROM biometric_data WHERE user_id = ? AND template_type = ? AND used_for_verification = false',
      [userId, templateType]
    );

    if (result.affectedRows === 0) {
      throw new Error('Template not found or cannot be deleted');
    }

    // Log the deletion
    await query(
      `INSERT INTO audit_logs (user_id, action, resource_type, resource_id, details)
       VALUES (?, 'DELETE', 'biometric', ?, ?)`,
      [userId, 0, JSON.stringify({ templateType, action: 'template_deleted' })]
    );

    logger.info(`Biometric template deleted for user ${userId}, type: ${templateType}`);
    return true;
  } catch (error) {
    logger.error('Error deleting biometric template:', error);
    throw error;
  }
};

/**
 * Get biometric statistics
 * @param {Object} filters - Filter options
 * @returns {Object} Biometric statistics
 */
const getBiometricStats = async (filters = {}) => {
  try {
    let whereClause = 'WHERE used_for_verification = false';
    const params = [];

    if (filters.startDate) {
      whereClause += ' AND DATE(created_at) >= ?';
      params.push(filters.startDate);
    }

    if (filters.endDate) {
      whereClause += ' AND DATE(created_at) <= ?';
      params.push(filters.endDate);
    }

    const stats = await query(`
      SELECT 
        COUNT(*) as total_templates,
        COUNT(DISTINCT user_id) as users_with_biometrics,
        SUM(CASE WHEN template_type = 'fingerprint' THEN 1 ELSE 0 END) as fingerprint_templates,
        SUM(CASE WHEN template_type = 'face' THEN 1 ELSE 0 END) as face_templates,
        AVG(quality) as avg_quality,
        SUM(CASE WHEN last_used IS NOT NULL THEN 1 ELSE 0 END) as used_templates,
        SUM(CASE WHEN last_used >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) as recently_used
      FROM biometric_data
      ${whereClause}
    `, params);

    // Get verification statistics
    const verificationStats = await query(`
      SELECT 
        COUNT(*) as total_verifications,
        SUM(CASE WHEN JSON_EXTRACT(details, '$.verified') = true THEN 1 ELSE 0 END) as successful_verifications,
        AVG(JSON_EXTRACT(details, '$.confidence')) as avg_confidence
      FROM audit_logs
      WHERE action = 'VERIFY' AND resource_type = 'biometric'
      ${filters.startDate ? 'AND DATE(created_at) >= ?' : ''}
      ${filters.endDate ? 'AND DATE(created_at) <= ?' : ''}
    `, [
      ...(filters.startDate ? [filters.startDate] : []),
      ...(filters.endDate ? [filters.endDate] : [])
    ]);

    return {
      ...stats[0],
      ...verificationStats[0]
    };
  } catch (error) {
    logger.error('Error getting biometric stats:', error);
    throw new Error('Failed to retrieve biometric statistics');
  }
};

/**
 * Get biometric verification history
 * @param {number} userId - User ID
 * @param {number} page - Page number
 * @param {number} limit - Results per page
 * @returns {Object} Verification history with pagination
 */
const getBiometricHistory = async (userId, page = 1, limit = 20) => {
  try {
    const offset = (page - 1) * limit;

    // Get total count
    const countResult = await query(
      'SELECT COUNT(*) as total FROM audit_logs WHERE user_id = ? AND action = "VERIFY" AND resource_type = "biometric"',
      [userId]
    );
    const total = countResult[0].total;

    // Get verification history
    const history = await query(`
      SELECT 
        id, created_at, details
      FROM audit_logs
      WHERE user_id = ? AND action = 'VERIFY' AND resource_type = 'biometric'
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `, [userId, limit, offset]);

    // Parse details for each record
    history.forEach(record => {
      try {
        record.details = JSON.parse(record.details);
      } catch (error) {
        logger.warn('Failed to parse verification details:', record.id);
        record.details = {};
      }
    });

    return {
      history,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    };
  } catch (error) {
    logger.error('Error getting biometric history:', error);
    throw new Error('Failed to retrieve biometric history');
  }
};

module.exports = {
  enrollBiometricTemplate,
  verifyBiometricTemplate,
  getUserBiometricTemplates,
  deleteBiometricTemplate,
  getBiometricStats,
  getBiometricHistory
};