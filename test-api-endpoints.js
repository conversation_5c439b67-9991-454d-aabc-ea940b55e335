/**
 * Quick API Endpoint Test Script
 * Run this to verify approval system endpoints are working
 * 
 * Usage: node test-api-endpoints.js
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api';

// Test credentials
const EMPLOYEE_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'password123'
};

const MANAGER_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'password123'
};

let employeeToken = '';
let managerToken = '';

async function login(credentials) {
  try {
    const response = await axios.post(`${BASE_URL}/auth/login`, credentials);
    return response.data.token;
  } catch (error) {
    console.error('Login failed:', error.response?.data || error.message);
    return null;
  }
}

async function testEndpoint(name, method, url, data = null, token = '') {
  try {
    const config = {
      method,
      url: `${BASE_URL}${url}`,
      headers: token ? { Authorization: `Bearer ${token}` } : {},
    };
    
    if (data) {
      config.data = data;
    }

    const response = await axios(config);
    console.log(`✅ ${name}: SUCCESS (${response.status})`);
    return response.data;
  } catch (error) {
    console.error(`❌ ${name}: FAILED (${error.response?.status || 'Network Error'})`);
    console.error(`   Error: ${error.response?.data?.message || error.message}`);
    return null;
  }
}

async function runTests() {
  console.log('🚀 Starting API Endpoint Tests for Approval System\n');

  // Test 1: Login as employee
  console.log('1. Testing Authentication...');
  employeeToken = await login(EMPLOYEE_CREDENTIALS);
  if (!employeeToken) {
    console.error('❌ Employee login failed - cannot continue tests');
    return;
  }
  console.log('✅ Employee login successful\n');

  // Test 2: Login as manager
  managerToken = await login(MANAGER_CREDENTIALS);
  if (!managerToken) {
    console.error('❌ Manager login failed - cannot continue tests');
    return;
  }
  console.log('✅ Manager login successful\n');

  // Test 3: Get employee's approval requests
  console.log('2. Testing Employee Endpoints...');
  await testEndpoint(
    'Get My Approval Requests',
    'GET',
    '/approvals/my-requests',
    null,
    employeeToken
  );

  // Test 4: Create approval request
  const newRequest = {
    timeLogId: 1,
    requestType: 'correction',
    requestData: {
      clockIn: '2024-01-15T08:45:00',
      clockOut: '2024-01-15T17:15:00'
    },
    reason: 'API test - time correction needed'
  };

  const createdRequest = await testEndpoint(
    'Create Approval Request',
    'POST',
    '/approvals',
    newRequest,
    employeeToken
  );

  console.log('');

  // Test 5: Manager endpoints
  console.log('3. Testing Manager Endpoints...');
  await testEndpoint(
    'Get Pending Approvals',
    'GET',
    '/approvals/pending',
    null,
    managerToken
  );

  await testEndpoint(
    'Get All Approvals',
    'GET',
    '/approvals',
    null,
    managerToken
  );

  await testEndpoint(
    'Get Approval Stats',
    'GET',
    '/approvals/stats',
    null,
    managerToken
  );

  // Test 6: Process approval (if we created one)
  if (createdRequest && createdRequest.id) {
    const approvalData = {
      status: 'approved',
      comments: 'API test approval - looks good'
    };

    await testEndpoint(
      'Process Approval Request',
      'PUT',
      `/approvals/${createdRequest.id}`,
      approvalData,
      managerToken
    );
  }

  console.log('');

  // Test 7: Error handling tests
  console.log('4. Testing Error Handling...');
  
  // Test unauthorized access
  await testEndpoint(
    'Unauthorized Access (No Token)',
    'GET',
    '/approvals/pending',
    null,
    ''
  );

  // Test invalid approval ID
  await testEndpoint(
    'Invalid Approval ID',
    'PUT',
    '/approvals/99999',
    { status: 'approved' },
    managerToken
  );

  // Test invalid request data
  await testEndpoint(
    'Invalid Request Data',
    'POST',
    '/approvals',
    { invalidField: 'test' },
    employeeToken
  );

  console.log('\n🎉 API Endpoint Tests Complete!');
  console.log('\nNext Steps:');
  console.log('1. If all tests passed, the backend is working correctly');
  console.log('2. Open http://localhost:3000 to test the frontend');
  console.log('3. Follow the MANUAL_TESTING_GUIDE.md for complete testing');
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Run the tests
runTests().catch(console.error);
