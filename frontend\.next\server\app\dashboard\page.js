/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/page";
exports.ids = ["app/dashboard/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5Cijureidini%5CWorkspace%5Cflexair_timekeeping_app%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cijureidini%5CWorkspace%5Cflexair_timekeeping_app%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5Cijureidini%5CWorkspace%5Cflexair_timekeeping_app%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cijureidini%5CWorkspace%5Cflexair_timekeeping_app%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?41d8\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/../node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/../node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(rsc)/./src/app/dashboard/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/../node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/dashboard/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/page\",\n        pathname: \"/dashboard\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5Cijureidini%5CWorkspace%5Cflexair_timekeeping_app%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cijureidini%5CWorkspace%5Cflexair_timekeeping_app%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(ssr)/./src/app/dashboard/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNpanVyZWlkaW5pJTVDJTVDV29ya3NwYWNlJTVDJTVDZmxleGFpcl90aW1la2VlcGluZ19hcHAlNUMlNUNmcm9udGVuZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2Rhc2hib2FyZCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvS0FBdUkiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mbGV4YWlyLWZyb250ZW5kLz9kMTViIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcaWp1cmVpZGluaVxcXFxXb3Jrc3BhY2VcXFxcZmxleGFpcl90aW1la2VlcGluZ19hcHBcXFxcZnJvbnRlbmRcXFxcc3JjXFxcXGFwcFxcXFxkYXNoYm9hcmRcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(ssr)/./src/contexts/AuthContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNpanVyZWlkaW5pJTVDJTVDV29ya3NwYWNlJTVDJTVDZmxleGFpcl90aW1la2VlcGluZ19hcHAlNUMlNUNmcm9udGVuZCU1QyU1Q3NyYyU1QyU1Q2NvbnRleHRzJTVDJTVDQXV0aENvbnRleHQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQXV0aFByb3ZpZGVyJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2lqdXJlaWRpbmklNUMlNUNXb3Jrc3BhY2UlNUMlNUNmbGV4YWlyX3RpbWVrZWVwaW5nX2FwcCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNmb250JTVDJTVDZ29vZ2xlJTVDJTVDdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJzcmMlNUMlNUMlNUMlNUNhcHAlNUMlNUMlNUMlNUNsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIySW50ZXIlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJpbnRlciU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNpanVyZWlkaW5pJTVDJTVDV29ya3NwYWNlJTVDJTVDZmxleGFpcl90aW1la2VlcGluZ19hcHAlNUMlNUNmcm9udGVuZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3S0FBMEsiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mbGV4YWlyLWZyb250ZW5kLz82NjM5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiQXV0aFByb3ZpZGVyXCJdICovIFwiQzpcXFxcVXNlcnNcXFxcaWp1cmVpZGluaVxcXFxXb3Jrc3BhY2VcXFxcZmxleGFpcl90aW1la2VlcGluZ19hcHBcXFxcZnJvbnRlbmRcXFxcc3JjXFxcXGNvbnRleHRzXFxcXEF1dGhDb250ZXh0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/app-router.js */ \"(ssr)/../node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/client-page.js */ \"(ssr)/../node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/../node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/../node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/../node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/../node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/auth/ProtectedRoute */ \"(ssr)/./src/components/auth/ProtectedRoute.tsx\");\n/* harmony import */ var _hooks_useApprovalNotifications__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useApprovalNotifications */ \"(ssr)/./src/hooks/useApprovalNotifications.ts\");\n/* harmony import */ var _components_approvals_NotificationBadge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/approvals/NotificationBadge */ \"(ssr)/./src/components/approvals/NotificationBadge.tsx\");\n/* harmony import */ var _components_approvals_ApprovalNotificationSummary__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/approvals/ApprovalNotificationSummary */ \"(ssr)/./src/components/approvals/ApprovalNotificationSummary.tsx\");\n/**\r\n * Dashboard Page\r\n * Main application dashboard with time tracking and statistics\r\n */ /* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n/**\r\n * Dashboard component with user information and quick actions\r\n */ function DashboardContent() {\n    const { user, logout } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const notifications = (0,_hooks_useApprovalNotifications__WEBPACK_IMPORTED_MODULE_4__.useApprovalNotifications)();\n    /**\r\n   * Handle user logout\r\n   */ const handleLogout = async ()=>{\n        try {\n            await logout();\n        } catch (error) {\n            console.error(\"Logout failed:\", error);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center py-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900\",\n                                        children: \"Dashboard\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 40,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            \"Welcome back, \",\n                                            user?.firstName,\n                                            \"!\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 41,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 39,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: [\n                                            \"Role: \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: user?.role\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 45,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 44,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleLogout,\n                                        className: \"bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium\",\n                                        children: \"Logout\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 47,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 43,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 36,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-4 py-6 sm:px-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white overflow-hidden shadow rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-4 py-5 sm:p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg leading-6 font-medium text-gray-900\",\n                                                children: \"User Information\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 66,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: \"Name:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 71,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \" \",\n                                                            user?.firstName,\n                                                            \" \",\n                                                            user?.lastName\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 70,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: \"Email:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 74,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \" \",\n                                                            user?.email\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 73,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: \"Role:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 77,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \" \",\n                                                            user?.role\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 76,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: \"Status:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 80,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: `ml-1 ${user?.isActive ? \"text-green-600\" : \"text-red-600\"}`,\n                                                                children: user?.isActive ? \"Active\" : \"Inactive\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 81,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 79,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 69,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white overflow-hidden shadow rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-4 py-5 sm:p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg leading-6 font-medium text-gray-900\",\n                                                children: \"Quick Actions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 92,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4 space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/time\",\n                                                        className: \"w-full block text-center bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium transition duration-200\",\n                                                        children: \"Time Tracking\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 96,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/approvals\",\n                                                        className: \"w-full block text-center bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-md text-sm font-medium transition duration-200 relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Approval Management\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 106,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            (notifications.pendingCount > 0 || notifications.myPendingRequests > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_approvals_NotificationBadge__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                count: notifications.pendingCount + notifications.myPendingRequests,\n                                                                className: \"absolute -top-2 -right-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 108,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 102,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    (user?.role === \"admin\" || user?.role === \"manager\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/users\",\n                                                        className: \"w-full block text-center bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition duration-200\",\n                                                        children: \"User Management\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 115,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"w-full bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-md text-sm font-medium\",\n                                                        children: \"Reports & Analytics\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 122,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white overflow-hidden shadow rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-4 py-5 sm:p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg leading-6 font-medium text-gray-900\",\n                                                children: \"Notifications\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_approvals_ApprovalNotificationSummary__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    notifications: notifications,\n                                                    showDetails: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 136,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-8 bg-indigo-50 rounded-lg p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold text-indigo-900 mb-2\",\n                                    children: \"\\uD83C\\uDF89 Welcome to Flexair Timekeeping!\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-indigo-700\",\n                                    children: [\n                                        \"Your authentication system is working perfectly. You are successfully logged in as\",\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: [\n                                                user?.firstName,\n                                                \" \",\n                                                user?.lastName\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" with\",\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: user?.role\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" privileges.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 text-sm text-indigo-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"✅ Authentication: Working\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"✅ Protected Routes: Working\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"✅ User Session: Active\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"✅ JWT Tokens: Valid\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 bg-gray-100 rounded-lg p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900 mb-3\",\n                                    children: \"Feature Status\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-1 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-center text-green-700\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mr-2\",\n                                                    children: \"✅\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 171,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Authentication System - Complete\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-center text-green-700\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mr-2\",\n                                                    children: \"✅\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 175,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Time Tracking Interface - Complete\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-center text-green-700\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mr-2\",\n                                                    children: \"✅\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"User Management Interface - Complete\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-center text-green-700\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mr-2\",\n                                                    children: \"✅\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Approval Workflow Interface - Complete\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-center text-gray-700\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mr-2\",\n                                                    children: \"⏳\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 187,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Biometric Integration - Planned\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-center text-gray-700\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mr-2\",\n                                                    children: \"⏳\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 191,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Reports & Analytics Dashboard - Planned\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, this);\n}\n/**\r\n * Dashboard page with authentication protection\r\n */ function DashboardPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        requireAuth: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DashboardContent, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 208,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 207,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/approvals/ApprovalNotificationSummary.tsx":
/*!******************************************************************!*\
  !*** ./src/components/approvals/ApprovalNotificationSummary.tsx ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ApprovalNotificationSummary)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _NotificationBadge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./NotificationBadge */ \"(ssr)/./src/components/approvals/NotificationBadge.tsx\");\n/**\n * Approval notification summary component\n */ \n\n\n\nfunction ApprovalNotificationSummary({ notifications, showDetails = false }) {\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const { pendingCount, myPendingRequests, isLoading } = notifications;\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-sm text-gray-500\",\n            children: \"Loading notifications...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalNotificationSummary.tsx\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, this);\n    }\n    const hasNotifications = pendingCount > 0 || myPendingRequests > 0;\n    if (!hasNotifications) {\n        return showDetails ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-sm text-gray-500\",\n            children: \"No pending approvals\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalNotificationSummary.tsx\",\n            lineNumber: 34,\n            columnNumber: 7\n        }, this) : null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-1\",\n        children: [\n            (user?.role === \"admin\" || user?.role === \"manager\") && pendingCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between text-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-600\",\n                        children: \"Pending approvals to review:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalNotificationSummary.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NotificationBadge__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        count: pendingCount\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalNotificationSummary.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalNotificationSummary.tsx\",\n                lineNumber: 43,\n                columnNumber: 9\n            }, this),\n            myPendingRequests > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between text-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-600\",\n                        children: \"My pending requests:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalNotificationSummary.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NotificationBadge__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        count: myPendingRequests,\n                        className: \"bg-blue-600\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalNotificationSummary.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalNotificationSummary.tsx\",\n                lineNumber: 50,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalNotificationSummary.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/approvals/ApprovalNotificationSummary.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/approvals/NotificationBadge.tsx":
/*!********************************************************!*\
  !*** ./src/components/approvals/NotificationBadge.tsx ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotificationBadge)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/**\n * Notification badge component\n */ \n\nfunction NotificationBadge({ count, className = \"\", maxCount = 99 }) {\n    if (count <= 0) return null;\n    const displayCount = count > maxCount ? `${maxCount}+` : count.toString();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: `inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-red-600 rounded-full ${className}`,\n        children: displayCount\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\NotificationBadge.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9hcHByb3ZhbHMvTm90aWZpY2F0aW9uQmFkZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQUE7O0NBRUM7QUFFeUI7QUFRWCxTQUFTQyxrQkFBa0IsRUFDeENDLEtBQUssRUFDTEMsWUFBWSxFQUFFLEVBQ2RDLFdBQVcsRUFBRSxFQUNVO0lBQ3ZCLElBQUlGLFNBQVMsR0FBRyxPQUFPO0lBRXZCLE1BQU1HLGVBQWVILFFBQVFFLFdBQVcsQ0FBQyxFQUFFQSxTQUFTLENBQUMsQ0FBQyxHQUFHRixNQUFNSSxRQUFRO0lBRXZFLHFCQUNFLDhEQUFDQztRQUFLSixXQUFXLENBQUMsb0hBQW9ILEVBQUVBLFVBQVUsQ0FBQztrQkFDaEpFOzs7Ozs7QUFHUCIsInNvdXJjZXMiOlsid2VicGFjazovL2ZsZXhhaXItZnJvbnRlbmQvLi9zcmMvY29tcG9uZW50cy9hcHByb3ZhbHMvTm90aWZpY2F0aW9uQmFkZ2UudHN4P2FmZDQiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBOb3RpZmljYXRpb24gYmFkZ2UgY29tcG9uZW50XG4gKi9cblxuaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcblxuZXhwb3J0IGludGVyZmFjZSBOb3RpZmljYXRpb25CYWRnZVByb3BzIHtcbiAgY291bnQ6IG51bWJlcjtcbiAgY2xhc3NOYW1lPzogc3RyaW5nO1xuICBtYXhDb3VudD86IG51bWJlcjtcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTm90aWZpY2F0aW9uQmFkZ2UoeyBcbiAgY291bnQsIFxuICBjbGFzc05hbWUgPSAnJywgXG4gIG1heENvdW50ID0gOTkgXG59OiBOb3RpZmljYXRpb25CYWRnZVByb3BzKSB7XG4gIGlmIChjb3VudCA8PSAwKSByZXR1cm4gbnVsbDtcblxuICBjb25zdCBkaXNwbGF5Q291bnQgPSBjb3VudCA+IG1heENvdW50ID8gYCR7bWF4Q291bnR9K2AgOiBjb3VudC50b1N0cmluZygpO1xuXG4gIHJldHVybiAoXG4gICAgPHNwYW4gY2xhc3NOYW1lPXtgaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHB4LTIgcHktMSB0ZXh0LXhzIGZvbnQtYm9sZCBsZWFkaW5nLW5vbmUgdGV4dC13aGl0ZSBiZy1yZWQtNjAwIHJvdW5kZWQtZnVsbCAke2NsYXNzTmFtZX1gfT5cbiAgICAgIHtkaXNwbGF5Q291bnR9XG4gICAgPC9zcGFuPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiTm90aWZpY2F0aW9uQmFkZ2UiLCJjb3VudCIsImNsYXNzTmFtZSIsIm1heENvdW50IiwiZGlzcGxheUNvdW50IiwidG9TdHJpbmciLCJzcGFuIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/approvals/NotificationBadge.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/auth/ProtectedRoute.tsx":
/*!************************************************!*\
  !*** ./src/components/auth/ProtectedRoute.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProtectedRoute),\n/* harmony export */   withAuth: () => (/* binding */ withAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/../node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/**\r\n * Protected Route Component\r\n * Wraps components that require authentication\r\n */ /* __next_internal_client_entry_do_not_use__ default,withAuth auto */ \n\n\n\n/**\r\n * Protected route component that handles authentication and authorization\r\n * @param children - Components to render if user is authorized\r\n * @param requireAuth - Whether authentication is required (default: true)\r\n * @param requiredRole - Minimum role required to access the route\r\n * @param redirectTo - URL to redirect to if not authorized\r\n * @param fallback - Component to show while loading\r\n */ function ProtectedRoute({ children, requireAuth = true, requiredRole, redirectTo = \"/auth/login\", fallback }) {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user, loading, isAuthenticated } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (loading) return; // Wait for auth state to be determined\n        // If authentication is required but user is not authenticated\n        if (requireAuth && !isAuthenticated) {\n            router.push(redirectTo);\n            return;\n        }\n        // If specific role is required, check user role\n        if (requiredRole && user && !hasRequiredRole(user.role, requiredRole)) {\n            router.push(\"/unauthorized\");\n            return;\n        }\n    }, [\n        loading,\n        isAuthenticated,\n        user,\n        requireAuth,\n        requiredRole,\n        router,\n        redirectTo\n    ]);\n    /**\r\n   * Check if user has required role\r\n   * @param userRole - Current user's role\r\n   * @param requiredRole - Required role\r\n   * @returns True if user has sufficient role\r\n   */ const hasRequiredRole = (userRole, requiredRole)=>{\n        const roleHierarchy = {\n            admin: 3,\n            manager: 2,\n            employee: 1\n        };\n        return roleHierarchy[userRole] >= roleHierarchy[requiredRole];\n    };\n    // Show loading state\n    if (loading) {\n        return fallback || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoadingSpinner, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n            lineNumber: 75,\n            columnNumber: 24\n        }, this);\n    }\n    // Show unauthorized if auth is required but user is not authenticated\n    if (requireAuth && !isAuthenticated) {\n        return null; // Router will handle redirect\n    }\n    // Show unauthorized if role is required but user doesn't have it\n    if (requiredRole && user && !hasRequiredRole(user.role, requiredRole)) {\n        return null; // Router will handle redirect\n    }\n    // Render children if all checks pass\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n/**\r\n * Loading spinner component\r\n */ function LoadingSpinner() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"animate-spin -ml-1 mr-3 h-8 w-8 text-indigo-600\",\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                            className: \"opacity-25\",\n                            cx: \"12\",\n                            cy: \"12\",\n                            r: \"10\",\n                            stroke: \"currentColor\",\n                            strokeWidth: \"4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            className: \"opacity-75\",\n                            fill: \"currentColor\",\n                            d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mt-2 text-sm text-gray-600\",\n                    children: \"Loading...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                    lineNumber: 119,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n            lineNumber: 98,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n        lineNumber: 97,\n        columnNumber: 5\n    }, this);\n}\n/**\r\n * Higher-order component for protecting pages\r\n * @param Component - Component to protect\r\n * @param options - Protection options\r\n * @returns Protected component\r\n */ function withAuth(Component, options = {}) {\n    const ProtectedComponent = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProtectedRoute, {\n            ...options,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...props\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n            lineNumber: 136,\n            columnNumber: 5\n        }, this);\n    ProtectedComponent.displayName = `withAuth(${Component.displayName || Component.name})`;\n    return ProtectedComponent;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/auth/ProtectedRoute.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(ssr)/./src/lib/auth.ts\");\n/**\r\n * Authentication Context Provider\r\n * Manages global authentication state and provides auth methods\r\n */ /* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n/**\r\n * Authentication Provider Component\r\n * Wraps the app and provides authentication state and methods\r\n * @param children - Child components to render\r\n */ function AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    /**\r\n   * Clear any authentication errors\r\n   */ const clearError = ()=>setError(null);\n    /**\r\n   * Initialize authentication state on mount\r\n   */ (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        initializeAuth();\n    }, []);\n    /**\r\n   * Initialize authentication state\r\n   * Checks for existing tokens and validates them\r\n   */ const initializeAuth = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            if (!(0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.isAuthenticated)()) {\n                setLoading(false);\n                return;\n            }\n            const token = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.getAccessToken)();\n            if (!token || (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.isTokenExpired)(token)) {\n                // Try to refresh token\n                try {\n                    await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.refreshTokens)();\n                } catch (error) {\n                    (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.clearTokens)();\n                    setLoading(false);\n                    return;\n                }\n            }\n            // Verify token and get user data\n            const { valid, user: userData } = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.verifyToken)();\n            if (valid && userData) {\n                setUser(userData);\n            } else {\n                (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.clearTokens)();\n            }\n        } catch (error) {\n            console.error(\"Auth initialization error:\", error);\n            (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.clearTokens)();\n        } finally{\n            setLoading(false);\n        }\n    };\n    /**\r\n   * Login user with credentials\r\n   * @param credentials - Login credentials\r\n   */ const login = async (credentials)=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const response = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.login)(credentials);\n            setUser(response.user);\n        } catch (error) {\n            setError(error.message || \"Login failed\");\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    /**\r\n   * Register new user\r\n   * @param userData - Registration data\r\n   */ const register = async (userData)=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const response = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.register)(userData);\n            setUser(response.user);\n        } catch (error) {\n            setError(error.message || \"Registration failed\");\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    /**\r\n   * Logout current user\r\n   */ const logout = async ()=>{\n        try {\n            setLoading(true);\n            await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.logout)();\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n        } finally{\n            setUser(null);\n            setLoading(false);\n        }\n    };\n    /**\r\n   * Refresh current user data\r\n   */ const refreshUser = async ()=>{\n        try {\n            const userData = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.getCurrentUser)();\n            setUser(userData);\n        } catch (error) {\n            console.error(\"Failed to refresh user data:\", error);\n        }\n    };\n    const value = {\n        user,\n        loading,\n        error,\n        isAuthenticated: !!user,\n        login,\n        register,\n        logout,\n        clearError,\n        refreshUser\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 186,\n        columnNumber: 5\n    }, this);\n}\n/**\r\n * Hook to use authentication context\r\n * @returns Authentication context\r\n * @throws Error if used outside AuthProvider\r\n */ function useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useApprovalNotifications.ts":
/*!***********************************************!*\
  !*** ./src/hooks/useApprovalNotifications.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useApprovalNotifications: () => (/* binding */ useApprovalNotifications)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_approvals__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/approvals */ \"(ssr)/./src/lib/approvals.ts\");\n/**\n * Custom hook for approval notifications\n * Provides real-time notification data for pending approvals\n */ \n\n\n/**\n * Hook for managing approval notifications\n */ function useApprovalNotifications() {\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__.useAuth)();\n    const [pendingCount, setPendingCount] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    const [myPendingRequests, setMyPendingRequests] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    /**\n   * Load notification data\n   */ const loadNotifications = async ()=>{\n        if (!user) return;\n        setIsLoading(true);\n        setError(null);\n        try {\n            // Load pending approvals count for managers/admins\n            if (user.role === \"admin\" || user.role === \"manager\") {\n                try {\n                    const stats = await (0,_lib_approvals__WEBPACK_IMPORTED_MODULE_2__.getApprovalStats)();\n                    setPendingCount(stats.pendingRequests);\n                } catch (err) {\n                    console.warn(\"Failed to load approval stats:\", err);\n                    setPendingCount(0);\n                }\n            }\n            // Load my pending requests count for all users\n            try {\n                const myRequests = await (0,_lib_approvals__WEBPACK_IMPORTED_MODULE_2__.getPendingApprovals)({\n                    status: \"pending\",\n                    limit: 1 // We only need the count\n                });\n                setMyPendingRequests(myRequests.pagination?.total || 0);\n            } catch (err) {\n                console.warn(\"Failed to load my pending requests:\", err);\n                setMyPendingRequests(0);\n            }\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"Failed to load notifications\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    /**\n   * Refresh notifications\n   */ const refresh = async ()=>{\n        await loadNotifications();\n    };\n    /**\n   * Load notifications on mount and user change\n   */ (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        loadNotifications();\n    }, [\n        user\n    ]);\n    /**\n   * Auto-refresh notifications every 30 seconds\n   */ (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const interval = setInterval(()=>{\n            loadNotifications();\n        }, 30000); // 30 seconds\n        return ()=>clearInterval(interval);\n    }, [\n        user\n    ]);\n    return {\n        pendingCount,\n        myPendingRequests,\n        isLoading,\n        error,\n        refresh\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useApprovalNotifications.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/approvals.ts":
/*!******************************!*\
  !*** ./src/lib/approvals.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createApprovalRequest: () => (/* binding */ createApprovalRequest),\n/* harmony export */   formatRequestData: () => (/* binding */ formatRequestData),\n/* harmony export */   getApprovalById: () => (/* binding */ getApprovalById),\n/* harmony export */   getApprovalStats: () => (/* binding */ getApprovalStats),\n/* harmony export */   getApprovals: () => (/* binding */ getApprovals),\n/* harmony export */   getMyApprovalRequests: () => (/* binding */ getMyApprovalRequests),\n/* harmony export */   getPendingApprovals: () => (/* binding */ getPendingApprovals),\n/* harmony export */   getRequestTypeDisplayName: () => (/* binding */ getRequestTypeDisplayName),\n/* harmony export */   getStatusBadgeColor: () => (/* binding */ getStatusBadgeColor),\n/* harmony export */   processApproval: () => (/* binding */ processApproval)\n/* harmony export */ });\n/* harmony import */ var _auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./auth */ \"(ssr)/./src/lib/auth.ts\");\n/**\n * Approval API service functions\n * Handles all approval-related API calls\n */ \nconst API_BASE_URL = \"http://localhost:5002/api\" || 0;\n/**\n * Make authenticated API request\n */ async function makeRequest(endpoint, options = {}) {\n    const token = (0,_auth__WEBPACK_IMPORTED_MODULE_0__.getAccessToken)();\n    const response = await fetch(`${API_BASE_URL}${endpoint}`, {\n        ...options,\n        headers: {\n            \"Content-Type\": \"application/json\",\n            \"Authorization\": `Bearer ${token}`,\n            ...options.headers\n        }\n    });\n    if (!response.ok) {\n        const errorData = await response.json().catch(()=>({}));\n        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);\n    }\n    return response.json();\n}\n/**\n * Create a new approval request\n */ async function createApprovalRequest(data) {\n    const response = await makeRequest(\"/approvals\", {\n        method: \"POST\",\n        body: JSON.stringify(data)\n    });\n    if (!response.success || !response.data) {\n        throw new Error(response.error || \"Failed to create approval request\");\n    }\n    return response.data;\n}\n/**\n * Get my approval requests\n */ async function getMyApprovalRequests(filters = {}) {\n    const queryParams = new URLSearchParams();\n    Object.entries(filters).forEach(([key, value])=>{\n        if (value !== undefined && value !== null) {\n            queryParams.append(key, value.toString());\n        }\n    });\n    const response = await makeRequest(`/approvals/my-requests?${queryParams.toString()}`);\n    if (!response.success) {\n        throw new Error(\"Failed to fetch approval requests\");\n    }\n    return {\n        requests: response.data,\n        pagination: response.pagination\n    };\n}\n/**\n * Get pending approvals (manager/admin only)\n */ async function getPendingApprovals(filters = {}) {\n    const queryParams = new URLSearchParams();\n    Object.entries(filters).forEach(([key, value])=>{\n        if (value !== undefined && value !== null) {\n            queryParams.append(key, value.toString());\n        }\n    });\n    const response = await makeRequest(`/approvals/pending?${queryParams.toString()}`);\n    if (!response.success) {\n        throw new Error(\"Failed to fetch pending approvals\");\n    }\n    return {\n        requests: response.data,\n        pagination: response.pagination\n    };\n}\n/**\n * Get all approvals with filtering\n */ async function getApprovals(filters = {}) {\n    const queryParams = new URLSearchParams();\n    Object.entries(filters).forEach(([key, value])=>{\n        if (value !== undefined && value !== null) {\n            queryParams.append(key, value.toString());\n        }\n    });\n    const response = await makeRequest(`/approvals?${queryParams.toString()}`);\n    if (!response.success) {\n        throw new Error(\"Failed to fetch approvals\");\n    }\n    return {\n        requests: response.data,\n        pagination: response.pagination\n    };\n}\n/**\n * Get approval by ID\n */ async function getApprovalById(id) {\n    const response = await makeRequest(`/approvals/${id}`);\n    if (!response.success || !response.data) {\n        throw new Error(response.error || \"Failed to fetch approval\");\n    }\n    return response.data;\n}\n/**\n * Process approval (approve/reject) - manager/admin only\n */ async function processApproval(id, data) {\n    const response = await makeRequest(`/approvals/${id}`, {\n        method: \"PUT\",\n        body: JSON.stringify(data)\n    });\n    if (!response.success || !response.data) {\n        throw new Error(response.error || \"Failed to process approval\");\n    }\n    return response.data;\n}\n/**\n * Get approval statistics (admin/manager only)\n */ async function getApprovalStats(filters = {}) {\n    const queryParams = new URLSearchParams();\n    Object.entries(filters).forEach(([key, value])=>{\n        if (value !== undefined && value !== null) {\n            queryParams.append(key, value.toString());\n        }\n    });\n    const response = await makeRequest(`/approvals/stats?${queryParams.toString()}`);\n    if (!response.success || !response.data) {\n        throw new Error(response.error || \"Failed to fetch approval statistics\");\n    }\n    return response.data;\n}\n/**\n * Utility functions\n */ /**\n * Get status badge color for approval status\n */ function getStatusBadgeColor(status) {\n    switch(status){\n        case \"pending\":\n            return \"bg-yellow-100 text-yellow-800\";\n        case \"approved\":\n            return \"bg-green-100 text-green-800\";\n        case \"rejected\":\n            return \"bg-red-100 text-red-800\";\n        default:\n            return \"bg-gray-100 text-gray-800\";\n    }\n}\n/**\n * Get request type display name\n */ function getRequestTypeDisplayName(type) {\n    switch(type){\n        case \"correction\":\n            return \"Time Correction\";\n        case \"overtime\":\n            return \"Overtime Request\";\n        case \"leave\":\n            return \"Leave Request\";\n        default:\n            return type;\n    }\n}\n/**\n * Format approval request data for display\n */ function formatRequestData(requestType, requestData) {\n    try {\n        switch(requestType){\n            case \"correction\":\n                return `Clock In: ${requestData.clockIn || \"N/A\"}, Clock Out: ${requestData.clockOut || \"N/A\"}`;\n            case \"overtime\":\n                return `Hours: ${requestData.hours || \"N/A\"}, Date: ${requestData.date || \"N/A\"}`;\n            case \"leave\":\n                return `From: ${requestData.startDate || \"N/A\"}, To: ${requestData.endDate || \"N/A\"}`;\n            default:\n                return JSON.stringify(requestData);\n        }\n    } catch  {\n        return \"Invalid request data\";\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/approvals.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiRequest: () => (/* binding */ apiRequest),\n/* harmony export */   clearTokens: () => (/* binding */ clearTokens),\n/* harmony export */   decodeToken: () => (/* binding */ decodeToken),\n/* harmony export */   getAccessToken: () => (/* binding */ getAccessToken),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   getRefreshToken: () => (/* binding */ getRefreshToken),\n/* harmony export */   isAuthenticated: () => (/* binding */ isAuthenticated),\n/* harmony export */   isTokenExpired: () => (/* binding */ isTokenExpired),\n/* harmony export */   login: () => (/* binding */ login),\n/* harmony export */   logout: () => (/* binding */ logout),\n/* harmony export */   refreshTokens: () => (/* binding */ refreshTokens),\n/* harmony export */   register: () => (/* binding */ register),\n/* harmony export */   setTokens: () => (/* binding */ setTokens),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/**\r\n * Authentication utilities and API calls\r\n * Handles login, registration, token management, and user session\r\n */ const API_BASE = \"http://localhost:5002/api\" || 0;\n/**\r\n * API response wrapper for error handling\r\n */ class ApiError extends Error {\n    constructor(message, status, code){\n        super(message);\n        this.status = status;\n        this.code = code;\n        this.name = \"ApiError\";\n    }\n}\n/**\r\n * Make authenticated API request with automatic token refresh\r\n * @param url - API endpoint URL\r\n * @param options - Fetch options\r\n * @returns Response data\r\n */ async function apiRequest(url, options = {}) {\n    const token = getAccessToken();\n    const config = {\n        headers: {\n            \"Content-Type\": \"application/json\",\n            ...token && {\n                Authorization: `Bearer ${token}`\n            },\n            ...options.headers\n        },\n        ...options\n    };\n    const response = await fetch(`${API_BASE}${url}`, config);\n    if (!response.ok) {\n        const errorData = await response.json().catch(()=>({}));\n        throw new ApiError(errorData.error?.message || \"Request failed\", response.status, errorData.error?.code);\n    }\n    return response.json();\n}\n/**\r\n * Login user with email and password\r\n * @param credentials - Login credentials\r\n * @returns Authentication response with user data and tokens\r\n */ async function login(credentials) {\n    const response = await apiRequest(\"/auth/login\", {\n        method: \"POST\",\n        body: JSON.stringify(credentials)\n    });\n    // Store tokens\n    setTokens(response.tokens);\n    return response;\n}\n/**\r\n * Register new user account\r\n * @param userData - Registration data\r\n * @returns Authentication response with user data and tokens\r\n */ async function register(userData) {\n    const response = await apiRequest(\"/auth/register\", {\n        method: \"POST\",\n        body: JSON.stringify(userData)\n    });\n    // Store tokens\n    setTokens(response.tokens);\n    return response;\n}\n/**\r\n * Logout user and clear tokens\r\n */ async function logout() {\n    const refreshToken = getRefreshToken();\n    try {\n        await apiRequest(\"/auth/logout\", {\n            method: \"POST\",\n            body: JSON.stringify({\n                refreshToken\n            })\n        });\n    } catch (error) {\n        // Continue with logout even if API call fails\n        console.warn(\"Logout API call failed:\", error);\n    }\n    clearTokens();\n}\n/**\r\n * Get current user profile\r\n * @returns Current user data\r\n */ async function getCurrentUser() {\n    const response = await apiRequest(\"/auth/profile\");\n    return response.user;\n}\n/**\r\n * Verify if current token is valid\r\n * @returns Token validity and user data\r\n */ async function verifyToken() {\n    try {\n        const response = await apiRequest(\"/auth/verify\");\n        return {\n            valid: response.valid,\n            user: response.user\n        };\n    } catch (error) {\n        return {\n            valid: false\n        };\n    }\n}\n/**\r\n * Refresh access token using refresh token\r\n * @returns New authentication tokens\r\n */ async function refreshTokens() {\n    const refreshToken = getRefreshToken();\n    if (!refreshToken) {\n        throw new Error(\"No refresh token available\");\n    }\n    const response = await fetch(`${API_BASE}/auth/refresh`, {\n        method: \"POST\",\n        headers: {\n            \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n            refreshToken\n        })\n    });\n    if (!response.ok) {\n        clearTokens();\n        throw new Error(\"Token refresh failed\");\n    }\n    const data = await response.json();\n    setTokens(data.tokens);\n    return data.tokens;\n}\n/**\r\n * Store authentication tokens in localStorage\r\n * @param tokens - Authentication tokens to store\r\n */ function setTokens(tokens) {\n    if (false) {}\n}\n/**\r\n * Get stored access token\r\n * @returns Access token or null\r\n */ function getAccessToken() {\n    if (false) {}\n    return null;\n}\n/**\r\n * Get stored refresh token\r\n * @returns Refresh token or null\r\n */ function getRefreshToken() {\n    if (false) {}\n    return null;\n}\n/**\r\n * Clear all stored authentication tokens\r\n */ function clearTokens() {\n    if (false) {}\n}\n/**\r\n * Check if user is currently authenticated\r\n * @returns True if user has valid tokens\r\n */ function isAuthenticated() {\n    return !!(getAccessToken() && getRefreshToken());\n}\n/**\r\n * Decode JWT token payload without verification\r\n * @param token - JWT token to decode\r\n * @returns Decoded payload or null\r\n */ function decodeToken(token) {\n    try {\n        const payload = token.split(\".\")[1];\n        return JSON.parse(atob(payload));\n    } catch (error) {\n        return null;\n    }\n}\n/**\r\n * Check if token is expired\r\n * @param token - JWT token to check\r\n * @returns True if token is expired\r\n */ function isTokenExpired(token) {\n    const decoded = decodeToken(token);\n    if (!decoded || !decoded.exp) return true;\n    return Date.now() >= decoded.exp * 1000;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"fbdb7e167290\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZmxleGFpci1mcm9udGVuZC8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/MTc0ZCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImZiZGI3ZTE2NzI5MFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ \"(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js\");\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\\Users\\<USER>\\Workspace\\flexair_timekeeping_app\\frontend\\src\\app\\dashboard\\page.tsx#default`));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/dashboard/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/../node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(rsc)/./src/contexts/AuthContext.tsx\");\n/**\r\n * Root layout component for the Flexair Timekeeping App\r\n * Provides global styling, context providers, and common layout structure\r\n */ \n\n\n\nconst metadata = {\n    title: \"Flexair Timekeeping\",\n    description: \"Modern timekeeping application with biometric integration\",\n    keywords: [\n        \"timekeeping\",\n        \"attendance\",\n        \"biometric\",\n        \"HR\",\n        \"workforce management\"\n    ],\n    authors: [\n        {\n            name: \"Flexair Team\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\",\n    robots: \"noindex, nofollow\"\n};\n/**\r\n * Root layout component with authentication context\r\n * @param children - Child components to render\r\n */ function RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className)} h-full bg-gray-50 antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-full\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 34,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 33,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ e0),\n/* harmony export */   useAuth: () => (/* binding */ e1)\n/* harmony export */ });\n/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ \"(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js\");\n\n\nconst e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\\Users\\<USER>\\Workspace\\flexair_timekeeping_app\\frontend\\src\\contexts\\AuthContext.tsx#AuthProvider`);\n\nconst e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\\Users\\<USER>\\Workspace\\flexair_timekeeping_app\\frontend\\src\\contexts\\AuthContext.tsx#useAuth`);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/contexts/AuthContext.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5Cijureidini%5CWorkspace%5Cflexair_timekeeping_app%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cijureidini%5CWorkspace%5Cflexair_timekeeping_app%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();