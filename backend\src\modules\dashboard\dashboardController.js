/**
 * Dashboard controller
 * Handles HTTP requests for dashboard data and analytics
 */

const dashboardService = require('./dashboardService');
const { logger } = require('../../shared/logger');

/**
 * Get comprehensive dashboard data
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getDashboardData = async (req, res) => {
  try {
    const userId = req.user.id;
    const userRole = req.user.role;
    const { startDate, endDate } = req.query;

    const filters = {};
    if (startDate) filters.startDate = startDate;
    if (endDate) filters.endDate = endDate;

    const dashboardData = await dashboardService.getDashboardData(
      userId,
      userRole,
      filters
    );

    res.json({
      success: true,
      data: dashboardData
    });
  } catch (error) {
    logger.error('Get dashboard data error:', error);
    res.status(500).json({
      error: 'Failed to retrieve dashboard data',
      code: 'DASHBOARD_ERROR'
    });
  }
};

/**
 * Get time tracking trends
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getTimeTrackingTrends = async (req, res) => {
  try {
    const userId = req.user.id;
    const userRole = req.user.role;
    const { days = 7 } = req.query;

    const trends = await dashboardService.getTimeTrackingTrends(
      userId,
      userRole,
      parseInt(days)
    );

    res.json({
      success: true,
      trends
    });
  } catch (error) {
    logger.error('Get time tracking trends error:', error);
    res.status(500).json({
      error: 'Failed to retrieve time tracking trends',
      code: 'TRENDS_ERROR'
    });
  }
};

/**
 * Get quick stats summary
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getQuickStats = async (req, res) => {
  try {
    const userId = req.user.id;
    const userRole = req.user.role;

    // Get today's data
    const today = new Date().toISOString().split('T')[0];
    const todayData = await dashboardService.getDashboardData(
      userId,
      userRole,
      { startDate: today, endDate: today }
    );

    // Extract quick stats
    const quickStats = {
      todayHours: todayData.timeStats?.hours_this_week || 0,
      todaySessions: todayData.timeStats?.sessions_today || 0,
      activeSessions: todayData.timeStats?.active_sessions || 0,
      pendingApprovals: todayData.approvalStats?.pending_requests || 0,
      hasActiveSession: todayData.pendingItems?.activeSessions?.length > 0
    };

    res.json({
      success: true,
      stats: quickStats
    });
  } catch (error) {
    logger.error('Get quick stats error:', error);
    res.status(500).json({
      error: 'Failed to retrieve quick stats',
      code: 'QUICK_STATS_ERROR'
    });
  }
};

module.exports = {
  getDashboardData,
  getTimeTrackingTrends,
  getQuickStats
};