/**
 * User management controller
 * Handles HTTP requests for user operations
 */

const userService = require('./userService');
const { logger } = require('../../shared/logger');

/**
 * Get all users with pagination and filtering
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getAllUsers = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      role,
      isActive,
      search
    } = req.query;

    const filters = {};
    if (role) filters.role = role;
    if (isActive !== undefined) filters.isActive = isActive === 'true';
    if (search) filters.search = search;

    const result = await userService.getAllUsers(
      filters,
      parseInt(page),
      parseInt(limit)
    );



    res.json({
      success: true,
      ...result
    });
  } catch (error) {
    logger.error('Get all users error:', error);
    res.status(500).json({
      error: 'Failed to retrieve users',
      code: 'USERS_FETCH_ERROR'
    });
  }
};

/**
 * Get user by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getUserById = async (req, res) => {
  try {
    const { id } = req.params;
    const user = await userService.getUserById(parseInt(id));

    if (!user) {
      return res.status(404).json({
        error: 'User not found',
        code: 'USER_NOT_FOUND'
      });
    }

    res.json({
      success: true,
      user
    });
  } catch (error) {
    logger.error('Get user by ID error:', error);
    res.status(500).json({
      error: 'Failed to retrieve user',
      code: 'USER_FETCH_ERROR'
    });
  }
};

/**
 * Create new user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const createUser = async (req, res) => {
  try {
    const userData = req.body;
    const user = await userService.createUser(userData);

    logger.info(`User created: ${user.email} by ${req.user.email}`);
    
    res.status(201).json({
      success: true,
      message: 'User created successfully',
      user
    });
  } catch (error) {
    logger.error('Create user error:', error);
    
    if (error.message === 'User with this email already exists') {
      return res.status(409).json({
        error: error.message,
        code: 'EMAIL_EXISTS'
      });
    }

    res.status(500).json({
      error: 'Failed to create user',
      code: 'USER_CREATE_ERROR'
    });
  }
};

/**
 * Update user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const updateUser = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;
    const updatedBy = req.user.id;

    const user = await userService.updateUser(
      parseInt(id),
      updateData,
      updatedBy
    );

    logger.info(`User updated: ${user.email} by ${req.user.email}`);

    res.json({
      success: true,
      message: 'User updated successfully',
      user
    });
  } catch (error) {
    logger.error('Update user error:', error);

    if (error.message === 'User not found') {
      return res.status(404).json({
        error: error.message,
        code: 'USER_NOT_FOUND'
      });
    }

    if (error.message === 'Email already in use by another user') {
      return res.status(409).json({
        error: error.message,
        code: 'EMAIL_EXISTS'
      });
    }

    res.status(500).json({
      error: 'Failed to update user',
      code: 'USER_UPDATE_ERROR'
    });
  }
};

/**
 * Delete user (soft delete)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const deleteUser = async (req, res) => {
  try {
    const { id } = req.params;
    const deletedBy = req.user.id;
    const userId = parseInt(id);

    // Prevent self-deletion
    if (userId === deletedBy) {
      return res.status(400).json({
        error: 'Cannot delete your own account',
        code: 'SELF_DELETE_FORBIDDEN'
      });
    }

    await userService.deleteUser(userId, deletedBy);

    logger.info(`User deleted: ID ${userId} by ${req.user.email}`);

    res.json({
      success: true,
      message: 'User deactivated successfully'
    });
  } catch (error) {
    logger.error('Delete user error:', error);
    
    if (error.message === 'User not found') {
      return res.status(404).json({
        error: error.message,
        code: 'USER_NOT_FOUND'
      });
    }

    res.status(500).json({
      error: 'Failed to delete user',
      code: 'USER_DELETE_ERROR'
    });
  }
};

/**
 * Update user password
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const updatePassword = async (req, res) => {
  try {
    const { id } = req.params;
    const { password } = req.body;
    const userId = parseInt(id);

    // Validate password is provided
    if (!password || password.length < 8) {
      return res.status(400).json({
        error: 'Password must be at least 8 characters',
        code: 'INVALID_PASSWORD'
      });
    }

    await userService.updateUserPassword(userId, password);

    logger.info(`Password updated for user ID ${userId} by ${req.user.email}`);

    res.json({
      success: true,
      message: 'Password updated successfully'
    });
  } catch (error) {
    logger.error('Update password error:', error);
    
    if (error.message === 'User not found') {
      return res.status(404).json({
        error: error.message,
        code: 'USER_NOT_FOUND'
      });
    }

    res.status(500).json({
      error: 'Failed to update password',
      code: 'PASSWORD_UPDATE_ERROR'
    });
  }
};

/**
 * Get current user profile
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getCurrentUser = async (req, res) => {
  try {
    const user = await userService.getUserById(req.user.id);
    
    if (!user) {
      return res.status(404).json({
        error: 'User not found',
        code: 'USER_NOT_FOUND'
      });
    }

    res.json({
      success: true,
      user
    });
  } catch (error) {
    logger.error('Get current user error:', error);
    res.status(500).json({
      error: 'Failed to retrieve user profile',
      code: 'PROFILE_FETCH_ERROR'
    });
  }
};

/**
 * Update current user profile
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const updateCurrentUser = async (req, res) => {
  try {
    const updateData = req.body;
    const userId = req.user.id;

    // Remove role from update data for non-admin users
    if (req.user.role !== 'admin') {
      delete updateData.role;
      delete updateData.isActive;
    }

    const user = await userService.updateUser(userId, updateData, userId);

    res.json({
      success: true,
      message: 'Profile updated successfully',
      user
    });
  } catch (error) {
    logger.error('Update current user error:', error);
    
    if (error.message === 'Email already in use by another user') {
      return res.status(409).json({
        error: error.message,
        code: 'EMAIL_EXISTS'
      });
    }

    res.status(500).json({
      error: 'Failed to update profile',
      code: 'PROFILE_UPDATE_ERROR'
    });
  }
};

/**
 * Get user statistics (admin only)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getUserStats = async (req, res) => {
  try {
    const stats = await userService.getUserStats();

    res.json({
      success: true,
      stats
    });
  } catch (error) {
    logger.error('Get user stats error:', error);
    res.status(500).json({
      error: 'Failed to retrieve user statistics',
      code: 'STATS_FETCH_ERROR'
    });
  }
};

module.exports = {
  getAllUsers,
  getUserById,
  createUser,
  updateUser,
  deleteUser,
  updatePassword,
  getCurrentUser,
  updateCurrentUser,
  getUserStats
};