'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { 
  createUser, 
  updateUser, 
  User, 
  CreateUserData, 
  UpdateUserData 
} from '@/lib/users';

interface UserFormProps {
  user?: User | null;
  onSuccess?: (user: User) => void;
  onCancel?: () => void;
  mode?: 'create' | 'edit';
}

/**
 * UserForm component for creating and editing users
 * Includes validation, role selection, and proper error handling
 */
export default function UserForm({ user, onSuccess, onCancel, mode = 'create' }: UserFormProps) {
  const { user: currentUser } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [message, setMessage] = useState('');

  // Form data state
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    firstName: '',
    lastName: '',
    department: '',
    role: 'employee' as 'admin' | 'manager' | 'employee',
    employeeId: '',
    phone: '',
    isActive: true
  });

  // Form validation state
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});

  // Initialize form data when editing
  useEffect(() => {
    if (mode === 'edit' && user) {
      setFormData({
        email: user.email || '',
        password: '',
        confirmPassword: '',
        firstName: user.firstName || '',
        lastName: user.lastName || '',
        department: user.department || '',
        role: user.role || 'employee',
        employeeId: user.employeeId || '',
        phone: user.phone || '',
        isActive: user.isActive ?? true
      });
    }
  }, [user, mode]);

  /**
   * Handle input changes
   */
  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  /**
   * Handle input blur (for validation)
   */
  const handleInputBlur = (field: string) => {
    setTouched(prev => ({ ...prev, [field]: true }));
    validateField(field, formData[field as keyof typeof formData]);
  };

  /**
   * Validate individual field
   */
  const validateField = (field: string, value: any): string => {
    let error = '';

    switch (field) {
      case 'email':
        if (!value) {
          error = 'Email is required';
        } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
          error = 'Please enter a valid email address';
        }
        break;

      case 'password':
        if (mode === 'create' && !value) {
          error = 'Password is required';
        } else if (value && value.length < 8) {
          error = 'Password must be at least 8 characters long';
        } else if (value && !/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(value)) {
          error = 'Password must contain at least one uppercase letter, one lowercase letter, and one number';
        }
        break;

      case 'confirmPassword':
        if (formData.password && value !== formData.password) {
          error = 'Passwords do not match';
        }
        break;

      case 'firstName':
        if (!value) {
          error = 'First name is required';
        } else if (value.length < 2) {
          error = 'First name must be at least 2 characters long';
        }
        break;

      case 'lastName':
        if (!value) {
          error = 'Last name is required';
        } else if (value.length < 2) {
          error = 'Last name must be at least 2 characters long';
        }
        break;

      case 'employeeId':
        if (value && !/^[A-Za-z0-9-_]+$/.test(value)) {
          error = 'Employee ID can only contain letters, numbers, hyphens, and underscores';
        }
        break;

      case 'phone':
        if (value && !/^\+?[\d\s\-\(\)]+$/.test(value)) {
          error = 'Please enter a valid phone number';
        }
        break;
    }

    setErrors(prev => ({ ...prev, [field]: error }));
    return error;
  };

  /**
   * Validate entire form
   */
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};
    
    // Validate all required fields
    Object.keys(formData).forEach(field => {
      const error = validateField(field, formData[field as keyof typeof formData]);
      if (error) {
        newErrors[field] = error;
      }
    });

    setErrors(newErrors);
    setTouched(Object.keys(formData).reduce((acc, key) => ({ ...acc, [key]: true }), {}));
    
    return Object.keys(newErrors).length === 0;
  };

  /**
   * Handle form submission
   */
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      setError('Please fix the errors below');
      return;
    }

    setLoading(true);
    setError('');
    setMessage('');

    try {
      let result: User;

      if (mode === 'create') {
        const createData: CreateUserData = {
          email: formData.email,
          password: formData.password,
          firstName: formData.firstName,
          lastName: formData.lastName,
          department: formData.department || undefined,
          role: formData.role,
          employeeId: formData.employeeId || undefined,
          phone: formData.phone || undefined,
        };
        
        result = await createUser(createData);
        setMessage('User created successfully!');
      } else {
        const updateData: UpdateUserData = {
          email: formData.email,
          firstName: formData.firstName,
          lastName: formData.lastName,
          department: formData.department || undefined,
          role: formData.role,
          employeeId: formData.employeeId || undefined,
          phone: formData.phone || undefined,
          isActive: formData.isActive,
        };
        
        result = await updateUser(user!.id, updateData);
        setMessage('User updated successfully!');
      }

      if (onSuccess) {
        onSuccess(result);
      }
    } catch (err: any) {
      console.error('Form submission error:', err);
      
      if (err.message?.includes('email already exists') || err.message?.includes('EMAIL_EXISTS')) {
        setErrors(prev => ({ ...prev, email: 'This email is already in use' }));
        setError('Email address is already in use');
      } else {
        setError(err.message || `Failed to ${mode} user. Please try again.`);
      }
    } finally {
      setLoading(false);
    }
  };

  /**
   * Check if current user can edit roles
   */
  const canEditRole = currentUser?.role === 'admin';
  const canEditStatus = currentUser?.role === 'admin';

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-800">
          {mode === 'create' ? 'Create New User' : 'Edit User'}
        </h2>
        <p className="text-gray-600 mt-1">
          {mode === 'create' 
            ? 'Add a new user to the system' 
            : 'Update user information and settings'
          }
        </p>
      </div>

      {/* Messages */}
      {(message || error) && (
        <div className="mb-6">
          {message && (
            <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-md">
              {message}
            </div>
          )}
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
              {error}
            </div>
          )}
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Personal Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 mb-2">
              First Name *
            </label>
            <input
              type="text"
              id="firstName"
              value={formData.firstName}
              onChange={(e) => handleInputChange('firstName', e.target.value)}
              onBlur={() => handleInputBlur('firstName')}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 ${
                errors.firstName && touched.firstName ? 'border-red-300' : 'border-gray-300'
              }`}
              disabled={loading}
            />
            {errors.firstName && touched.firstName && (
              <p className="mt-1 text-sm text-red-600">{errors.firstName}</p>
            )}
          </div>

          <div>
            <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 mb-2">
              Last Name *
            </label>
            <input
              type="text"
              id="lastName"
              value={formData.lastName}
              onChange={(e) => handleInputChange('lastName', e.target.value)}
              onBlur={() => handleInputBlur('lastName')}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 ${
                errors.lastName && touched.lastName ? 'border-red-300' : 'border-gray-300'
              }`}
              disabled={loading}
            />
            {errors.lastName && touched.lastName && (
              <p className="mt-1 text-sm text-red-600">{errors.lastName}</p>
            )}
          </div>
        </div>

        {/* Contact Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
              Email Address *
            </label>
            <input
              type="email"
              id="email"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              onBlur={() => handleInputBlur('email')}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 ${
                errors.email && touched.email ? 'border-red-300' : 'border-gray-300'
              }`}
              disabled={loading}
            />
            {errors.email && touched.email && (
              <p className="mt-1 text-sm text-red-600">{errors.email}</p>
            )}
          </div>

          <div>
            <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
              Phone Number
            </label>
            <input
              type="tel"
              id="phone"
              value={formData.phone}
              onChange={(e) => handleInputChange('phone', e.target.value)}
              onBlur={() => handleInputBlur('phone')}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 ${
                errors.phone && touched.phone ? 'border-red-300' : 'border-gray-300'
              }`}
              disabled={loading}
              placeholder="+****************"
            />
            {errors.phone && touched.phone && (
              <p className="mt-1 text-sm text-red-600">{errors.phone}</p>
            )}
          </div>
        </div>

        {/* Work Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label htmlFor="department" className="block text-sm font-medium text-gray-700 mb-2">
              Department
            </label>
            <input
              type="text"
              id="department"
              value={formData.department}
              onChange={(e) => handleInputChange('department', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
              disabled={loading}
              placeholder="e.g., Engineering, HR, Sales"
            />
          </div>

          <div>
            <label htmlFor="employeeId" className="block text-sm font-medium text-gray-700 mb-2">
              Employee ID
            </label>
            <input
              type="text"
              id="employeeId"
              value={formData.employeeId}
              onChange={(e) => handleInputChange('employeeId', e.target.value)}
              onBlur={() => handleInputBlur('employeeId')}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 ${
                errors.employeeId && touched.employeeId ? 'border-red-300' : 'border-gray-300'
              }`}
              disabled={loading}
              placeholder="e.g., EMP001"
            />
            {errors.employeeId && touched.employeeId && (
              <p className="mt-1 text-sm text-red-600">{errors.employeeId}</p>
            )}
          </div>
        </div>

        {/* Role and Status */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label htmlFor="role" className="block text-sm font-medium text-gray-700 mb-2">
              Role
            </label>
            <select
              id="role"
              value={formData.role}
              onChange={(e) => handleInputChange('role', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
              disabled={loading || !canEditRole}
            >
              <option value="employee">Employee</option>
              <option value="manager">Manager</option>
              <option value="admin">Admin</option>
            </select>
            {!canEditRole && (
              <p className="mt-1 text-sm text-gray-500">Only admins can change user roles</p>
            )}
          </div>

          {mode === 'edit' && canEditStatus && (
            <div>
              <label htmlFor="isActive" className="block text-sm font-medium text-gray-700 mb-2">
                Status
              </label>
              <select
                id="isActive"
                value={formData.isActive.toString()}
                onChange={(e) => handleInputChange('isActive', e.target.value === 'true')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                disabled={loading}
              >
                <option value="true">Active</option>
                <option value="false">Inactive</option>
              </select>
            </div>
          )}
        </div>

        {/* Password Fields (only for create mode or when changing password) */}
        {mode === 'create' && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
                Password *
              </label>
              <input
                type="password"
                id="password"
                value={formData.password}
                onChange={(e) => handleInputChange('password', e.target.value)}
                onBlur={() => handleInputBlur('password')}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 ${
                  errors.password && touched.password ? 'border-red-300' : 'border-gray-300'
                }`}
                disabled={loading}
              />
              {errors.password && touched.password && (
                <p className="mt-1 text-sm text-red-600">{errors.password}</p>
              )}
            </div>

            <div>
              <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-2">
                Confirm Password *
              </label>
              <input
                type="password"
                id="confirmPassword"
                value={formData.confirmPassword}
                onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                onBlur={() => handleInputBlur('confirmPassword')}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 ${
                  errors.confirmPassword && touched.confirmPassword ? 'border-red-300' : 'border-gray-300'
                }`}
                disabled={loading}
              />
              {errors.confirmPassword && touched.confirmPassword && (
                <p className="mt-1 text-sm text-red-600">{errors.confirmPassword}</p>
              )}
            </div>
          </div>
        )}

        {/* Form Actions */}
        <div className="flex flex-col sm:flex-row gap-4 pt-6 border-t border-gray-200">
          <button
            type="submit"
            disabled={loading}
            className="flex-1 bg-indigo-600 hover:bg-indigo-700 disabled:bg-gray-400 text-white font-medium py-2 px-4 rounded-md transition duration-200"
          >
            {loading ? (
              <div className="flex items-center justify-center">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                {mode === 'create' ? 'Creating...' : 'Updating...'}
              </div>
            ) : (
              mode === 'create' ? 'Create User' : 'Update User'
            )}
          </button>
          
          {onCancel && (
            <button
              type="button"
              onClick={onCancel}
              disabled={loading}
              className="flex-1 bg-gray-300 hover:bg-gray-400 disabled:bg-gray-200 text-gray-700 font-medium py-2 px-4 rounded-md transition duration-200"
            >
              Cancel
            </button>
          )}
        </div>
      </form>
    </div>
  );
}
