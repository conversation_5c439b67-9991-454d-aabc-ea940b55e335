'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import UserForm from '@/components/users/UserForm';
import { User } from '@/lib/users';

/**
 * New user creation page
 * Allows admins to create new user accounts
 */
function NewUserPage() {
  const router = useRouter();

  /**
   * Handle successful user creation
   */
  const handleUserSuccess = (user: User) => {
    // Redirect to the user's profile page
    router.push(`/users/${user.id}`);
  };

  /**
   * Handle cancel action
   */
  const handleCancel = () => {
    router.push('/users');
  };

  return (
    <ProtectedRoute requiredRoles={['admin']}>
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <header className="bg-white shadow">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-6">
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => router.push('/users')}
                  className="text-gray-500 hover:text-gray-700"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7"></path>
                  </svg>
                </button>
                <div>
                  <h1 className="text-3xl font-bold text-gray-900">Create New User</h1>
                  <p className="text-sm text-gray-600">Add a new user account to the system</p>
                </div>
              </div>

              {/* Navigation breadcrumbs */}
              <nav className="flex items-center space-x-2 text-sm text-gray-500">
                <button
                  onClick={() => router.push('/dashboard')}
                  className="hover:text-gray-700"
                >
                  Dashboard
                </button>
                <span>/</span>
                <button
                  onClick={() => router.push('/users')}
                  className="hover:text-gray-700"
                >
                  Users
                </button>
                <span>/</span>
                <span className="text-gray-900 font-medium">New User</span>
              </nav>
            </div>
          </div>
        </header>

        {/* Main Content */}
        <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <UserForm
            mode="create"
            onSuccess={handleUserSuccess}
            onCancel={handleCancel}
          />
        </main>
      </div>
    </ProtectedRoute>
  );
}

export default NewUserPage;
