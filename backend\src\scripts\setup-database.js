/**
 * Database setup script
 * Creates the MySQL database and initial tables for the Flexair Timekeeping App
 */

const mysql = require('mysql2/promise');
const dotenv = require('dotenv');
const path = require('path');

// Load environment variables
dotenv.config({ path: path.join(__dirname, '../../.env') });

/**
 * Database schema SQL statements
 */
const schema = {
  database: `CREATE DATABASE IF NOT EXISTS \`${process.env.DB_NAME || 'flexair_timekeeping'}\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;`,
  
  users: `CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(191) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHA<PERSON>(100) NOT NULL,
    last_name VA<PERSON><PERSON><PERSON>(100) NOT NULL,
    department VARCHAR(100),
    role ENUM('admin', 'manager', 'employee') DEFAULT 'employee',
    employee_id VARCHAR(50) UNIQUE,
    phone VARCHAR(20),
    is_active BOOLEAN DEFAULT true,
    email_verified BOOLEAN DEFAULT false,
    biometric_enabled BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT NULL,
    last_login DATETIME NULL,
    
    INDEX idx_email (email),
    INDEX idx_employee_id (employee_id),
    INDEX idx_department (department),
    INDEX idx_role (role),
    INDEX idx_active (is_active)
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;`,

  refresh_tokens: `CREATE TABLE IF NOT EXISTS refresh_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    token VARCHAR(191) NOT NULL,
    expires_at DATETIME NOT NULL,
    is_revoked BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    revoked_at DATETIME NULL,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_token (token),
    INDEX idx_expires (expires_at),
    INDEX idx_revoked (is_revoked)
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;`,

  token_blacklist: `CREATE TABLE IF NOT EXISTS token_blacklist (
    id INT AUTO_INCREMENT PRIMARY KEY,
    token VARCHAR(191) UNIQUE NOT NULL,
    expires_at DATETIME NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_token (token),
    INDEX idx_expires (expires_at)
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;`,

  password_reset_tokens: `CREATE TABLE IF NOT EXISTS password_reset_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    token VARCHAR(255) NOT NULL,
    expires_at DATETIME NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    used_at DATETIME NULL,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_token (token),
    INDEX idx_expires (expires_at)
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;`,

  time_logs: `CREATE TABLE IF NOT EXISTS time_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    clock_in DATETIME NOT NULL,
    clock_out DATETIME NULL,
    break_duration INT DEFAULT 0 COMMENT 'Break duration in minutes',
    total_hours DECIMAL(4,2) DEFAULT 0.00,
    location VARCHAR(255),
    notes TEXT,
    status ENUM('active', 'completed', 'pending_approval') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT NULL,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_clock_in (clock_in),
    INDEX idx_status (status),
    INDEX idx_created_date (created_at)
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;`,

  approvals: `CREATE TABLE IF NOT EXISTS approvals (
    id INT AUTO_INCREMENT PRIMARY KEY,
    time_log_id INT NOT NULL,
    user_id INT NOT NULL,
    manager_id INT NOT NULL,
    status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    comments TEXT,
    approved_at DATETIME NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT NULL,
    
    FOREIGN KEY (time_log_id) REFERENCES time_logs(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (manager_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_time_log_id (time_log_id),
    INDEX idx_user_id (user_id),
    INDEX idx_manager_id (manager_id),
    INDEX idx_status (status)
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;`,

  biometric_data: `CREATE TABLE IF NOT EXISTS biometric_data (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    biometric_type ENUM('fingerprint', 'face', 'palm') NOT NULL,
    template_data LONGBLOB NOT NULL,
    template_hash VARCHAR(255) NOT NULL,
    quality_score DECIMAL(3,2),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT NULL,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_type (biometric_type),
    INDEX idx_hash (template_hash),
    INDEX idx_active (is_active)
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;`,

  audit_logs: `CREATE TABLE IF NOT EXISTS audit_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50) NOT NULL,
    resource_id INT,
    old_values TEXT,
    new_values TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_resource (resource_type, resource_id),
    INDEX idx_created_at (created_at)
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;`,

  settings: `CREATE TABLE IF NOT EXISTS settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    key_name VARCHAR(191) UNIQUE NOT NULL,
    value TEXT,
    description TEXT,
    type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    is_public BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT NULL,
    
    INDEX idx_key (key_name),
    INDEX idx_public (is_public)
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;`
};

/**
 * Default settings data
 */
const defaultSettings = [
  {
    key_name: 'company_name',
    value: 'Flexair Technologies',
    description: 'Company name displayed in the application',
    type: 'string',
    is_public: true
  },
  {
    key_name: 'work_hours_per_day',
    value: '8',
    description: 'Standard work hours per day',
    type: 'number',
    is_public: true
  },
  {
    key_name: 'break_duration',
    value: '60',
    description: 'Standard break duration in minutes',
    type: 'number',
    is_public: true
  },
  {
    key_name: 'biometric_enabled',
    value: 'true',
    description: 'Enable biometric authentication',
    type: 'boolean',
    is_public: true
  },
  {
    key_name: 'approval_required',
    value: 'true',
    description: 'Require manager approval for time logs',
    type: 'boolean',
    is_public: true
  },
  {
    key_name: 'timezone',
    value: 'UTC',
    description: 'Application timezone',
    type: 'string',
    is_public: true
  }
];

/**
 * Create database connection
 */
async function createConnection() {
  return await mysql.createConnection({
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 3306,
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    multipleStatements: true
  });
}

/**
 * Setup database and tables
 */
async function setupDatabase() {
  let connection;
  
  try {
    console.log('🔗 Connecting to MySQL server...');
    connection = await createConnection();
    
    console.log('📊 Creating database...');
    await connection.query(schema.database);
    
    console.log('🔄 Switching to database...');
    await connection.query(`USE \`${process.env.DB_NAME || 'flexair_timekeeping'}\``);
    
    console.log('📋 Creating tables...');
    const tables = Object.keys(schema).filter(key => key !== 'database');
    
    for (const table of tables) {
      console.log(`  Creating table: ${table}`);
      await connection.query(schema[table]);
    }
    
    console.log('⚙️  Inserting default settings...');
    for (const setting of defaultSettings) {
      const insertQuery = `
        INSERT INTO settings (key_name, value, description, type, is_public)
        VALUES (?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
          value = VALUES(value),
          description = VALUES(description),
          type = VALUES(type),
          is_public = VALUES(is_public)
      `;
      
      await connection.execute(insertQuery, [
        setting.key_name,
        setting.value,
        setting.description,
        setting.type,
        setting.is_public
      ]);
    }
    
    console.log('✅ Database setup completed successfully!');
    console.log('');
    console.log('Next steps:');
    console.log('1. Copy backend/.env.example to backend/.env');
    console.log('2. Update database credentials in backend/.env');
    console.log('3. Run: npm run dev');
    
  } catch (error) {
    console.error('❌ Database setup failed:', error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

/**
 * Main execution
 */
if (require.main === module) {
  setupDatabase();
}

module.exports = { setupDatabase, schema, defaultSettings };