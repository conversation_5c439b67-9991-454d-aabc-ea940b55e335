/**
 * Tests for useApprovalNotifications hook and related components
 */

import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { renderHook } from '@testing-library/react';
import { useAuth } from '@/contexts/AuthContext';
import { 
  useApprovalNotifications, 
  NotificationBadge, 
  ApprovalNotificationSummary 
} from '@/hooks/useApprovalNotifications';
import * as approvalsLib from '@/lib/approvals';

// Mock the auth context
jest.mock('@/contexts/AuthContext', () => ({
  useAuth: jest.fn(),
}));

// Mock the approvals library
jest.mock('@/lib/approvals', () => ({
  getApprovalStats: jest.fn(),
  getPendingApprovals: jest.fn(),
}));

const mockUseAuth = useAuth as jest.MockedFunction<typeof useAuth>;
const mockGetApprovalStats = approvalsLib.getApprovalStats as jest.MockedFunction<typeof approvalsLib.getApprovalStats>;
const mockGetPendingApprovals = approvalsLib.getPendingApprovals as jest.MockedFunction<typeof approvalsLib.getPendingApprovals>;

// Mock timers
jest.useFakeTimers();

describe('useApprovalNotifications', () => {
  const mockManagerUser = {
    id: 1,
    email: '<EMAIL>',
    firstName: 'Manager',
    lastName: 'Smith',
    role: 'manager' as const,
  };

  const mockEmployeeUser = {
    id: 2,
    email: '<EMAIL>',
    firstName: 'John',
    lastName: 'Doe',
    role: 'employee' as const,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockGetApprovalStats.mockResolvedValue({
      totalRequests: 10,
      pendingRequests: 3,
      approvedRequests: 5,
      rejectedRequests: 2,
      averageProcessingTime: 24,
      requestsByType: {
        correction: 4,
        overtime: 3,
        leave: 3,
      },
    });
    mockGetPendingApprovals.mockResolvedValue({
      requests: [],
      pagination: { page: 1, limit: 1, total: 2, totalPages: 1 },
    });
  });

  afterEach(() => {
    jest.runOnlyPendingTimers();
    jest.useRealTimers();
  });

  describe('Hook Behavior', () => {
    it('loads notifications for manager users', async () => {
      mockUseAuth.mockReturnValue({ user: mockManagerUser } as any);

      const { result } = renderHook(() => useApprovalNotifications());

      expect(result.current.isLoading).toBe(true);

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(mockGetApprovalStats).toHaveBeenCalled();
      expect(mockGetPendingApprovals).toHaveBeenCalled();
      expect(result.current.pendingCount).toBe(3);
      expect(result.current.myPendingRequests).toBe(2);
    });

    it('loads notifications for employee users', async () => {
      mockUseAuth.mockReturnValue({ user: mockEmployeeUser } as any);

      const { result } = renderHook(() => useApprovalNotifications());

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(mockGetApprovalStats).not.toHaveBeenCalled();
      expect(mockGetPendingApprovals).toHaveBeenCalled();
      expect(result.current.pendingCount).toBe(0);
      expect(result.current.myPendingRequests).toBe(2);
    });

    it('handles no user gracefully', async () => {
      mockUseAuth.mockReturnValue({ user: null } as any);

      const { result } = renderHook(() => useApprovalNotifications());

      expect(result.current.isLoading).toBe(false);
      expect(result.current.pendingCount).toBe(0);
      expect(result.current.myPendingRequests).toBe(0);
      expect(mockGetApprovalStats).not.toHaveBeenCalled();
      expect(mockGetPendingApprovals).not.toHaveBeenCalled();
    });

    it('handles API errors gracefully', async () => {
      mockUseAuth.mockReturnValue({ user: mockManagerUser } as any);
      mockGetApprovalStats.mockRejectedValue(new Error('API Error'));
      mockGetPendingApprovals.mockRejectedValue(new Error('API Error'));

      const { result } = renderHook(() => useApprovalNotifications());

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.error).toBe('API Error');
      expect(result.current.pendingCount).toBe(0);
      expect(result.current.myPendingRequests).toBe(0);
    });

    it('auto-refreshes notifications every 30 seconds', async () => {
      mockUseAuth.mockReturnValue({ user: mockManagerUser } as any);

      renderHook(() => useApprovalNotifications());

      // Initial load
      await waitFor(() => {
        expect(mockGetApprovalStats).toHaveBeenCalledTimes(1);
      });

      // Fast-forward 30 seconds
      jest.advanceTimersByTime(30000);

      await waitFor(() => {
        expect(mockGetApprovalStats).toHaveBeenCalledTimes(2);
      });
    });

    it('provides refresh function', async () => {
      mockUseAuth.mockReturnValue({ user: mockManagerUser } as any);

      const { result } = renderHook(() => useApprovalNotifications());

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      // Call refresh
      await result.current.refresh();

      expect(mockGetApprovalStats).toHaveBeenCalledTimes(2);
      expect(mockGetPendingApprovals).toHaveBeenCalledTimes(2);
    });
  });
});

describe('NotificationBadge', () => {
  it('renders badge with count', () => {
    render(<NotificationBadge count={5} />);
    expect(screen.getByText('5')).toBeInTheDocument();
  });

  it('renders badge with max count', () => {
    render(<NotificationBadge count={150} maxCount={99} />);
    expect(screen.getByText('99+')).toBeInTheDocument();
  });

  it('does not render when count is 0', () => {
    const { container } = render(<NotificationBadge count={0} />);
    expect(container.firstChild).toBeNull();
  });

  it('does not render when count is negative', () => {
    const { container } = render(<NotificationBadge count={-1} />);
    expect(container.firstChild).toBeNull();
  });

  it('applies custom className', () => {
    render(<NotificationBadge count={5} className="custom-class" />);
    const badge = screen.getByText('5');
    expect(badge).toHaveClass('custom-class');
  });
});

describe('ApprovalNotificationSummary', () => {
  const mockNotifications = {
    pendingCount: 3,
    myPendingRequests: 2,
    isLoading: false,
    error: null,
    refresh: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('shows loading state', () => {
    const loadingNotifications = { ...mockNotifications, isLoading: true };
    render(<ApprovalNotificationSummary notifications={loadingNotifications} />);
    expect(screen.getByText('Loading notifications...')).toBeInTheDocument();
  });

  it('shows no notifications message when showDetails is true', () => {
    const emptyNotifications = { 
      ...mockNotifications, 
      pendingCount: 0, 
      myPendingRequests: 0 
    };
    render(
      <ApprovalNotificationSummary 
        notifications={emptyNotifications} 
        showDetails={true} 
      />
    );
    expect(screen.getByText('No pending approvals')).toBeInTheDocument();
  });

  it('renders nothing when no notifications and showDetails is false', () => {
    const emptyNotifications = { 
      ...mockNotifications, 
      pendingCount: 0, 
      myPendingRequests: 0 
    };
    const { container } = render(
      <ApprovalNotificationSummary 
        notifications={emptyNotifications} 
        showDetails={false} 
      />
    );
    expect(container.firstChild).toBeNull();
  });

  it('shows pending approvals for managers', () => {
    mockUseAuth.mockReturnValue({ 
      user: { role: 'manager' } 
    } as any);

    render(<ApprovalNotificationSummary notifications={mockNotifications} />);
    
    expect(screen.getByText('Pending approvals to review:')).toBeInTheDocument();
    expect(screen.getByText('3')).toBeInTheDocument();
  });

  it('shows my pending requests', () => {
    mockUseAuth.mockReturnValue({ 
      user: { role: 'employee' } 
    } as any);

    render(<ApprovalNotificationSummary notifications={mockNotifications} />);
    
    expect(screen.getByText('My pending requests:')).toBeInTheDocument();
    expect(screen.getByText('2')).toBeInTheDocument();
  });

  it('shows both types of notifications for managers', () => {
    mockUseAuth.mockReturnValue({ 
      user: { role: 'admin' } 
    } as any);

    render(<ApprovalNotificationSummary notifications={mockNotifications} />);
    
    expect(screen.getByText('Pending approvals to review:')).toBeInTheDocument();
    expect(screen.getByText('My pending requests:')).toBeInTheDocument();
    expect(screen.getByText('3')).toBeInTheDocument();
    expect(screen.getByText('2')).toBeInTheDocument();
  });

  it('does not show manager notifications for employees', () => {
    mockUseAuth.mockReturnValue({ 
      user: { role: 'employee' } 
    } as any);

    render(<ApprovalNotificationSummary notifications={mockNotifications} />);
    
    expect(screen.queryByText('Pending approvals to review:')).not.toBeInTheDocument();
    expect(screen.getByText('My pending requests:')).toBeInTheDocument();
  });

  it('uses different badge colors for different notification types', () => {
    mockUseAuth.mockReturnValue({ 
      user: { role: 'admin' } 
    } as any);

    render(<ApprovalNotificationSummary notifications={mockNotifications} />);
    
    const badges = screen.getAllByText(/[0-9]+/);
    expect(badges).toHaveLength(2);
    
    // Check that badges have different classes (one red, one blue)
    const parentElements = badges.map(badge => badge.parentElement);
    const classNames = parentElements.map(el => el?.className || '');
    
    expect(classNames.some(className => className.includes('bg-red-600'))).toBe(true);
    expect(classNames.some(className => className.includes('bg-blue-600'))).toBe(true);
  });
});
