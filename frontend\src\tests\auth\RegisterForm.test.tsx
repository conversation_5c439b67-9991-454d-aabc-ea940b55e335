/**
 * RegisterForm Component Tests
 * Tests registration form functionality, validation, and user interactions
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { useRouter } from 'next/navigation';
import RegisterForm from '@/components/auth/RegisterForm';
import { useAuth } from '@/contexts/AuthContext';

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
}));

// Mock auth context
jest.mock('@/contexts/AuthContext', () => ({
  useAuth: jest.fn(),
}));

const mockRegister = jest.fn();
const mockClearError = jest.fn();
const mockPush = jest.fn();

const defaultAuthContextValue = {
  register: mockRegister,
  loading: false,
  error: null,
  clearError: mockClearError,
  user: null,
  isAuthenticated: false,
  login: jest.fn(),
  logout: jest.fn(),
  refreshUser: jest.fn(),
};

describe('RegisterForm', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockReturnValue({ push: mockPush });
    (useAuth as jest.Mock).mockReturnValue(defaultAuthContextValue);
  });

  /**
   * Test: Form renders correctly with all required fields
   */
  it('renders registration form with required fields', () => {
    render(<RegisterForm />);
    
    expect(screen.getByRole('heading', { name: /create your account/i })).toBeInTheDocument();
    expect(screen.getByLabelText(/first name/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/last name/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/email address/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/department/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/^password$/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/confirm password/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /create account/i })).toBeInTheDocument();
  });

  /**
   * Test: Required field validation
   */
  it('validates required fields', async () => {
    const user = userEvent.setup();
    render(<RegisterForm />);
    
    const submitButton = screen.getByRole('button', { name: /create account/i });
    
    // Submit empty form
    await user.click(submitButton);
    
    expect(screen.getByText(/first name is required/i)).toBeInTheDocument();
    expect(screen.getByText(/last name is required/i)).toBeInTheDocument();
    expect(screen.getByText(/email is required/i)).toBeInTheDocument();
    expect(screen.getByText(/password is required/i)).toBeInTheDocument();
    expect(screen.getByText(/please confirm your password/i)).toBeInTheDocument();
  });

  /**
   * Test: Password strength validation
   */
  it('validates password strength', async () => {
    const user = userEvent.setup();
    render(<RegisterForm />);
    
    const passwordField = screen.getByLabelText(/^password$/i);
    const submitButton = screen.getByRole('button', { name: /create account/i });
    
    // Test weak password
    await user.type(passwordField, 'weak');
    await user.click(submitButton);
    
    expect(screen.getByText(/password must contain uppercase, lowercase, number and special character/i)).toBeInTheDocument();
  });

  /**
   * Test: Password confirmation validation
   */
  it('validates password confirmation', async () => {
    const user = userEvent.setup();
    render(<RegisterForm />);
    
    const passwordField = screen.getByLabelText(/^password$/i);
    const confirmPasswordField = screen.getByLabelText(/confirm password/i);
    const submitButton = screen.getByRole('button', { name: /create account/i });
    
    // Test mismatched passwords
    await user.type(passwordField, 'Password123!');
    await user.type(confirmPasswordField, 'DifferentPassword123!');
    await user.click(submitButton);
    
    expect(screen.getByText(/passwords do not match/i)).toBeInTheDocument();
  });

  /**
   * Test: Successful form submission
   */
  it('submits form with valid data', async () => {
    const user = userEvent.setup();
    render(<RegisterForm />);
    
    // Fill form with valid data
    await user.type(screen.getByLabelText(/first name/i), 'John');
    await user.type(screen.getByLabelText(/last name/i), 'Doe');
    await user.type(screen.getByLabelText(/email address/i), '<EMAIL>');
    await user.type(screen.getByLabelText(/department/i), 'IT');
    await user.type(screen.getByLabelText(/^password$/i), 'Password123!');
    await user.type(screen.getByLabelText(/confirm password/i), 'Password123!');
    
    // Submit form
    await user.click(screen.getByRole('button', { name: /create account/i }));
    
    expect(mockRegister).toHaveBeenCalledWith({
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      department: 'IT',
      password: 'Password123!',
    });
  });

  /**
   * Test: Loading state is displayed correctly
   */
  it('shows loading state during submission', () => {
    (useAuth as jest.Mock).mockReturnValue({
      ...defaultAuthContextValue,
      loading: true,
    });
    
    render(<RegisterForm />);
    
    expect(screen.getByText(/creating account.../i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /creating account.../i })).toBeDisabled();
  });

  /**
   * Test: Error message is displayed when registration fails
   */
  it('displays error message when registration fails', () => {
    (useAuth as jest.Mock).mockReturnValue({
      ...defaultAuthContextValue,
      error: 'Email already exists',
    });
    
    render(<RegisterForm />);
    
    expect(screen.getByText(/email already exists/i)).toBeInTheDocument();
  });

  /**
   * Test: Navigation link to login page
   */
  it('contains link to login page', () => {
    render(<RegisterForm />);
    
    expect(screen.getByRole('link', { name: /sign in here/i })).toBeInTheDocument();
  });
});