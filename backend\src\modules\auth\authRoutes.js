/**
 * Authentication routes
 * Handles user login, registration, password reset, and token management
 */

const express = require('express');
const { authRateLimiter, passwordResetLimiter } = require('../../shared/middleware/rateLimiter');
const { asyncErrorHandler } = require('../../shared/middleware/errorHandler');
const authController = require('./authController');
const { authenticateToken } = require('../../shared/middleware/authMiddleware');
const { authValidation } = require('../../shared/middleware/validation');

const router = express.Router();

/**
 * @route POST /api/auth/register
 * @desc Register a new user
 * @access Public
 */
router.post('/register',
  authRateLimiter,
  ...authValidation.register,
  asyncErrorHandler(authController.register)
);

/**
 * @route POST /api/auth/login
 * @desc Authenticate user and return token
 * @access Public
 */
router.post('/login',
  authRateLimiter,
  ...authValidation.login,
  asyncError<PERSON>andler(authController.login)
);

/**
 * @route POST /api/auth/logout
 * @desc Logout user and invalidate token
 * @access Private
 */
router.post('/logout',
  authenticateToken,
  asyncErrorHandler(authController.logout)
);

/**
 * @route POST /api/auth/refresh-token
 * @desc Refresh access token using refresh token
 * @access Public
 */
router.post('/refresh-token',
  asyncErrorHandler(authController.refreshToken)
);

/**
 * @route POST /api/auth/forgot-password
 * @desc Send password reset email
 * @access Public
 */
router.post('/forgot-password',
  passwordResetLimiter,
  ...authValidation.forgotPassword,
  asyncErrorHandler(authController.forgotPassword)
);

/**
 * @route POST /api/auth/reset-password
 * @desc Reset password using reset token
 * @access Public
 */
router.post('/reset-password',
  ...authValidation.resetPassword,
  asyncErrorHandler(authController.resetPassword)
);

/**
 * @route POST /api/auth/change-password
 * @desc Change password for authenticated user
 * @access Private
 */
router.post('/change-password',
  authenticateToken,
  asyncErrorHandler(authController.changePassword)
);

/**
 * @route GET /api/auth/verify
 * @desc Verify token and return user info
 * @access Private
 */
router.get('/verify',
  authenticateToken,
  asyncErrorHandler(authController.verifyToken)
);

/**
 * @route GET /api/auth/profile
 * @desc Get current user profile
 * @access Private
 */
router.get('/profile',
  authenticateToken,
  asyncErrorHandler(authController.getProfile)
);

/**
 * @route PUT /api/auth/profile
 * @desc Update current user profile
 * @access Private
 */
router.put('/profile',
  authenticateToken,
  asyncErrorHandler(authController.updateProfile)
);

module.exports = router;