/**
 * Database Setup Wizard
 * Interactive setup for online MySQL database
 */

const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

/**
 * Prompt user for input
 * @param {string} question - Question to ask
 * @returns {Promise<string>} User input
 */
function prompt(question) {
  return new Promise((resolve) => {
    rl.question(question, resolve);
  });
}

/**
 * Test database connection
 * @param {Object} config - Database configuration
 * @returns {Promise<boolean>} Connection success
 */
async function testConnection(config) {
  try {
    console.log('\n🔗 Testing connection...');
    const connection = await mysql.createConnection({
      host: config.host,
      port: config.port,
      user: config.user,
      password: config.password,
      connectTimeout: 15000
    });

    await connection.execute('SELECT 1');
    console.log('✅ Connection successful!');
    await connection.end();
    return true;
  } catch (error) {
    console.log('❌ Connection failed:', error.message);
    return false;
  }
}

/**
 * Update .env file with new credentials
 * @param {Object} config - Database configuration
 */
function updateEnvFile(config) {
  const envPath = path.join(__dirname, '.env');
  let envContent = '';

  try {
    envContent = fs.readFileSync(envPath, 'utf8');
  } catch (error) {
    console.log('Creating new .env file...');
  }

  // Update or add database configuration
  const dbConfig = `# Database Configuration - FreeSQLDatabase
DB_HOST=${config.host}
DB_PORT=${config.port}
DB_NAME=${config.database}
DB_USER=${config.user}
DB_PASSWORD=${config.password}`;

  // Replace existing DB config or add new one
  if (envContent.includes('DB_HOST=')) {
    envContent = envContent.replace(
      /# Database Configuration.*?\nDB_HOST=.*?\nDB_PORT=.*?\nDB_NAME=.*?\nDB_USER=.*?\nDB_PASSWORD=.*?\n/s,
      dbConfig + '\n\n'
    );
  } else {
    envContent = dbConfig + '\n\n' + envContent;
  }

  fs.writeFileSync(envPath, envContent);
  console.log('✅ .env file updated successfully!');
}

/**
 * Run database setup
 * @param {Object} config - Database configuration
 */
async function runDatabaseSetup(config) {
  try {
    console.log('\n🚀 Running database setup...');
    
    // Import and run setup
    const { setupDatabase } = require('./src/scripts/setup-database.js');
    
    // Temporarily set environment variables
    process.env.DB_HOST = config.host;
    process.env.DB_PORT = config.port;
    process.env.DB_NAME = config.database;
    process.env.DB_USER = config.user;
    process.env.DB_PASSWORD = config.password;
    
    await setupDatabase();
    console.log('✅ Database setup completed!');
    return true;
  } catch (error) {
    console.log('❌ Database setup failed:', error.message);
    return false;
  }
}

/**
 * Main setup wizard
 */
async function setupWizard() {
  console.log('🧙‍♂️ Flexair Timekeeping Database Setup Wizard');
  console.log('===============================================\n');
  
  console.log('📋 Please have your FreeSQLDatabase credentials ready.');
  console.log('   You should have received an email with:');
  console.log('   - Host (e.g., sql12.freesqldatabase.com)');
  console.log('   - Database Name');
  console.log('   - Username');
  console.log('   - Password');
  console.log('   - Port (usually 3306)\n');

  const config = {};

  try {
    // Get database credentials
    config.host = await prompt('🌐 Database Host: ');
    config.port = await prompt('🔌 Database Port [3306]: ') || '3306';
    config.database = await prompt('📊 Database Name: ');
    config.user = await prompt('👤 Username: ');
    config.password = await prompt('🔐 Password: ');

    console.log('\n📝 Configuration Summary:');
    console.log(`   Host: ${config.host}`);
    console.log(`   Port: ${config.port}`);
    console.log(`   Database: ${config.database}`);
    console.log(`   User: ${config.user}`);
    console.log(`   Password: ${'*'.repeat(config.password.length)}`);

    const confirm = await prompt('\n✅ Proceed with this configuration? (y/N): ');
    if (confirm.toLowerCase() !== 'y' && confirm.toLowerCase() !== 'yes') {
      console.log('❌ Setup cancelled.');
      rl.close();
      return;
    }

    // Test connection
    const connectionOk = await testConnection(config);
    if (!connectionOk) {
      console.log('\n❌ Cannot proceed without valid database connection.');
      console.log('   Please check your credentials and try again.');
      rl.close();
      return;
    }

    // Update .env file
    updateEnvFile(config);

    // Run database setup
    const setupOk = await runDatabaseSetup(config);
    if (setupOk) {
      console.log('\n🎉 Setup completed successfully!');
      console.log('🚀 You can now run: npm run dev');
    } else {
      console.log('\n❌ Setup encountered errors. Please check the logs above.');
    }

  } catch (error) {
    console.log('\n❌ Setup wizard error:', error.message);
  } finally {
    rl.close();
  }
}

// Run wizard
if (require.main === module) {
  setupWizard().catch(console.error);
}

module.exports = { setupWizard };