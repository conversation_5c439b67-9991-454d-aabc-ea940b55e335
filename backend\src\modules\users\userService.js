/**
 * User management service layer
 * Handles user CRUD operations and business logic
 */

const bcrypt = require('bcrypt');
const { query } = require('../../shared/database');
const { logger } = require('../../shared/logger');

/**
 * Get all users with pagination and filtering
 * @param {Object} filters - Filter options
 * @param {number} page - Page number
 * @param {number} limit - Results per page
 * @returns {Object} Users data with pagination info
 */
const getAllUsers = async (filters = {}, page = 1, limit = 20) => {
  try {
    const offset = (page - 1) * limit;
    let whereClause = 'WHERE 1=1';
    const params = [];

    // Apply filters
    if (filters.role) {
      whereClause += ' AND role = ?';
      params.push(filters.role);
    }

    if (filters.isActive !== undefined) {
      whereClause += ' AND is_active = ?';
      params.push(filters.isActive);
    }

    if (filters.search) {
      whereClause += ' AND (first_name LIKE ? OR last_name LIKE ? OR email LIKE ?)';
      const searchTerm = `%${filters.search}%`;
      params.push(searchTerm, searchTerm, searchTerm);
    }

    // Get total count
    const countQuery = `SELECT COUNT(*) as total FROM users ${whereClause}`;
    const countResult = await query(countQuery, params);
    const total = countResult[0].total;

    // Get users
    const userQuery = `
      SELECT
        id, email, first_name, last_name, department, role, employee_id, phone,
        is_active, email_verified, biometric_enabled, last_login, created_at, updated_at
      FROM users
      ${whereClause}
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `;

    const rawUsers = await query(userQuery, [...params, limit, offset]);

    // Transform users to API format
    const users = rawUsers.map(user => transformUserObject(user));

    return {
      users,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    };
  } catch (error) {
    logger.error('Error getting users:', error);
    throw new Error('Failed to retrieve users');
  }
};

/**
 * Get user by ID
 * @param {number} userId - User ID
 * @returns {Object|null} User data or null if not found
 */
const getUserById = async (userId) => {
  try {
    const users = await query(
      `SELECT
        id, email, first_name, last_name, department, role, employee_id, phone,
        is_active, email_verified, biometric_enabled, last_login, created_at, updated_at
      FROM users
      WHERE id = ?`,
      [userId]
    );

    const user = users.length > 0 ? users[0] : null;
    return user ? transformUserObject(user) : null;
  } catch (error) {
    logger.error('Error getting user by ID:', error);
    throw new Error('Failed to retrieve user');
  }
};

/**
 * Get user by email
 * @param {string} email - User email
 * @returns {Object|null} User data or null if not found
 */
const getUserByEmail = async (email) => {
  try {
    const users = await query(
      `SELECT
        id, email, first_name, last_name, department, role, employee_id, phone,
        is_active, email_verified, biometric_enabled, password_hash, last_login, created_at, updated_at
      FROM users
      WHERE email = ?`,
      [email]
    );

    const user = users.length > 0 ? users[0] : null;
    // Don't transform for getUserByEmail as it's used for authentication and needs password_hash
    return user;
  } catch (error) {
    logger.error('Error getting user by email:', error);
    throw new Error('Failed to retrieve user');
  }
};

/**
 * Create new user
 * @param {Object} userData - User data
 * @returns {Object} Created user data
 */
const createUser = async (userData) => {
  try {
    const { email, password, firstName, lastName, role = 'employee' } = userData;

    // Check if user already exists
    const existingUser = await getUserByEmail(email);
    if (existingUser) {
      throw new Error('User with this email already exists');
    }

    // Hash password if provided
    let passwordHash = null;
    if (password) {
      passwordHash = await bcrypt.hash(password, 12);
    }

    // Insert user
    const result = await query(
      `INSERT INTO users (email, password_hash, first_name, last_name, role, is_active)
       VALUES (?, ?, ?, ?, ?, ?)`,
      [email, passwordHash, firstName, lastName, role, true]
    );

    const userId = result.insertId;
    
    // Log user creation
    await query(
      `INSERT INTO audit_logs (user_id, action, resource_type, resource_id, new_values)
       VALUES (?, 'CREATE', 'user', ?, ?)`,
      [userId, userId, JSON.stringify({ email, role })]
    );

    // Return created user (without password hash)
    return await getUserById(userId);
  } catch (error) {
    logger.error('Error creating user:', error);
    throw error;
  }
};

/**
 * Update user
 * @param {number} userId - User ID
 * @param {Object} updateData - Update data
 * @param {number} updatedBy - ID of user making the update
 * @returns {Object} Updated user data
 */
const updateUser = async (userId, updateData, updatedBy) => {
  try {
    const existingUser = await getUserById(userId);
    if (!existingUser) {
      throw new Error('User not found');
    }

    const updateFields = [];
    const params = [];

    // Handle email update
    if (updateData.email && updateData.email !== existingUser.email) {
      const emailExists = await getUserByEmail(updateData.email);
      if (emailExists && emailExists.id !== userId) {
        throw new Error('Email already in use by another user');
      }
      updateFields.push('email = ?');
      params.push(updateData.email);
    }

    // Handle other fields
    const allowedFields = ['firstName', 'lastName', 'department', 'role', 'employeeId', 'phone', 'isActive'];
    const fieldMap = {
      firstName: 'first_name',
      lastName: 'last_name',
      department: 'department',
      role: 'role',
      employeeId: 'employee_id',
      phone: 'phone',
      isActive: 'is_active'
    };

    allowedFields.forEach(field => {
      if (updateData[field] !== undefined) {
        updateFields.push(`${fieldMap[field]} = ?`);
        params.push(updateData[field]);
      }
    });

    if (updateFields.length === 0) {
      return existingUser; // No changes to make
    }

    // Add updated_at and user ID for WHERE clause
    updateFields.push('updated_at = NOW()');
    params.push(userId);

    const updateQuery = `UPDATE users SET ${updateFields.join(', ')} WHERE id = ?`;
    await query(updateQuery, params);

    // Log the update
    await query(
      `INSERT INTO audit_logs (user_id, action, resource_type, resource_id, new_values)
       VALUES (?, 'UPDATE', 'user', ?, ?)`,
      [updatedBy, userId, JSON.stringify(updateData)]
    );

    // Return updated user
    return await getUserById(userId);
  } catch (error) {
    logger.error('Error updating user:', error);
    throw error;
  }
};

/**
 * Delete user (soft delete - deactivate)
 * @param {number} userId - User ID
 * @param {number} deletedBy - ID of user performing deletion
 * @returns {boolean} Success status
 */
const deleteUser = async (userId, deletedBy) => {
  try {
    const user = await getUserById(userId);
    if (!user) {
      throw new Error('User not found');
    }

    // Soft delete - deactivate user
    await query(
      'UPDATE users SET is_active = false, updated_at = NOW() WHERE id = ?',
      [userId]
    );

    // Log the deletion
    await query(
      `INSERT INTO audit_logs (user_id, action, resource_type, resource_id, new_values)
       VALUES (?, 'DELETE', 'user', ?, ?)`,
      [deletedBy, userId, JSON.stringify({ email: user.email, deactivated: true })]
    );

    return true;
  } catch (error) {
    logger.error('Error deleting user:', error);
    throw error;
  }
};

/**
 * Update user password
 * @param {number} userId - User ID
 * @param {string} newPassword - New password
 * @returns {boolean} Success status
 */
const updateUserPassword = async (userId, newPassword) => {
  try {
    const user = await getUserById(userId);
    if (!user) {
      throw new Error('User not found');
    }

    const passwordHash = await bcrypt.hash(newPassword, 12);
    
    await query(
      'UPDATE users SET password_hash = ?, updated_at = NOW() WHERE id = ?',
      [passwordHash, userId]
    );

    // Log password change
    await query(
      `INSERT INTO audit_logs (user_id, action, resource_type, resource_id, new_values)
       VALUES (?, 'UPDATE', 'user', ?, ?)`,
      [userId, userId, JSON.stringify({ action: 'password_change' })]
    );

    return true;
  } catch (error) {
    logger.error('Error updating password:', error);
    throw error;
  }
};

/**
 * Update user last login
 * @param {number} userId - User ID
 * @returns {boolean} Success status
 */
const updateLastLogin = async (userId) => {
  try {
    await query(
      'UPDATE users SET last_login = NOW() WHERE id = ?',
      [userId]
    );
    return true;
  } catch (error) {
    logger.error('Error updating last login:', error);
    return false; // Non-critical error
  }
};

/**
 * Get user statistics
 * @returns {Object} User statistics
 */
const getUserStats = async () => {
  try {
    const stats = await query(`
      SELECT 
        COUNT(*) as total_users,
        SUM(CASE WHEN is_active = true THEN 1 ELSE 0 END) as active_users,
        SUM(CASE WHEN role = 'admin' THEN 1 ELSE 0 END) as admin_count,
        SUM(CASE WHEN role = 'manager' THEN 1 ELSE 0 END) as manager_count,
        SUM(CASE WHEN role = 'employee' THEN 1 ELSE 0 END) as employee_count,
        SUM(CASE WHEN last_login >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) as active_last_30_days
      FROM users
    `);

    return stats[0];
  } catch (error) {
    logger.error('Error getting user stats:', error);
    throw new Error('Failed to retrieve user statistics');
  }
};

/**
 * Transform database user object to API response format
 * Converts snake_case to camelCase for frontend compatibility
 * @param {Object} user - User object from database
 * @returns {Object} Transformed user object
 */
const transformUserObject = (user) => {
  if (!user) return null;

  return {
    id: user.id,
    email: user.email,
    firstName: user.first_name,
    lastName: user.last_name,
    department: user.department,
    role: user.role,
    employeeId: user.employee_id,
    phone: user.phone,
    isActive: Boolean(user.is_active), // Ensure boolean conversion
    emailVerified: Boolean(user.email_verified),
    biometricEnabled: Boolean(user.biometric_enabled),
    lastLogin: user.last_login,
    createdAt: user.created_at,
    updatedAt: user.updated_at,
    // Keep original fields for backward compatibility if needed
    first_name: user.first_name,
    last_name: user.last_name,
    is_active: user.is_active,
    last_login: user.last_login,
    created_at: user.created_at,
    updated_at: user.updated_at
  };
};

module.exports = {
  getAllUsers,
  getUserById,
  getUserByEmail,
  createUser,
  updateUser,
  deleteUser,
  updateUserPassword,
  updateLastLogin,
  transformUserObject,
  getUserStats
};