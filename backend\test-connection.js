/**
 * Test online MySQL connection
 * Verifies database connectivity before running setup
 */

const mysql = require('mysql2/promise');
const dotenv = require('dotenv');
const path = require('path');

// Load environment variables
dotenv.config();

/**
 * Test database connection
 */
async function testConnection() {
  console.log('🔗 Testing online MySQL connection...');
  console.log(`Host: ${process.env.DB_HOST}`);
  console.log(`Port: ${process.env.DB_PORT}`);
  console.log(`Database: ${process.env.DB_NAME}`);
  console.log(`User: ${process.env.DB_USER}`);
  console.log('');

  try {
    // Test connection without database first
    console.log('📡 Connecting to MySQL server...');
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST,
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      connectTimeout: 10000
    });

    console.log('✅ Connected to MySQL server successfully!');

    // Test basic query
    console.log('🔍 Testing basic query...');
    const [rows] = await connection.execute('SELECT 1 as test, NOW() as server_time');
    console.log('✅ Query executed successfully:', rows[0]);

    // Check if database exists
    console.log('📊 Checking if database exists...');
    const [databases] = await connection.query('SHOW DATABASES');
    
    const dbExists = databases.some(db => Object.values(db)[0] === process.env.DB_NAME);
    
    if (dbExists) {
      console.log('✅ Database exists:', process.env.DB_NAME);
      
      // Test database connection
      await connection.query(`USE \`${process.env.DB_NAME}\``);
      const [tables] = await connection.query('SHOW TABLES');
      console.log(`📋 Found ${tables.length} tables in database`);
    } else {
      console.log('⚠️  Database does not exist yet (will be created during setup)');
    }

    await connection.end();
    
    console.log('\n🎉 Connection test successful!');
    console.log('🚀 Ready to run: npm run db:setup');
    
    return true;
  } catch (error) {
    console.log('\n❌ Connection test failed:');
    console.log('Error:', error.message);
    
    if (error.code === 'ENOTFOUND') {
      console.log('\n💡 Possible issues:');
      console.log('   - Check DB_HOST in .env file');
      console.log('   - Verify internet connection');
    } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.log('\n💡 Possible issues:');
      console.log('   - Check DB_USER and DB_PASSWORD in .env file');
      console.log('   - Verify credentials with your MySQL provider');
    } else if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Possible issues:');
      console.log('   - Check DB_PORT in .env file');
      console.log('   - Verify the MySQL service is running');
    }
    
    console.log('\n🔧 Current configuration:');
    console.log(`   DB_HOST=${process.env.DB_HOST}`);
    console.log(`   DB_PORT=${process.env.DB_PORT}`);
    console.log(`   DB_USER=${process.env.DB_USER}`);
    console.log(`   DB_PASSWORD=${process.env.DB_PASSWORD ? '[SET]' : '[EMPTY]'}`);
    
    return false;
  }
}

// Run test
if (require.main === module) {
  testConnection().catch(console.error);
}

module.exports = { testConnection };