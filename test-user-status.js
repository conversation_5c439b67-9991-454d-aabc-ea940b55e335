/**
 * Test script to verify user status toggle functionality
 */

require('dotenv').config({ path: './backend/.env' });
const mysql = require('mysql2/promise');

async function testUserStatus() {
  console.log('🧪 Testing user status toggle functionality...\n');

  const connection = await mysql.createConnection({
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME
  });

  try {
    // Get all users and their current status
    console.log('📋 Current user status:');
    const [users] = await connection.execute(
      'SELECT id, email, first_name, last_name, is_active FROM users ORDER BY id'
    );

    users.forEach(user => {
      console.log(`   ${user.id}: ${user.first_name} ${user.last_name} (${user.email}) - ${user.is_active ? 'ACTIVE' : 'INACTIVE'}`);
    });

    console.log('\n🔄 Testing status toggle...');
    
    // Find a user to test with (preferably not the admin)
    const testUser = users.find(u => u.email !== '<EMAIL>') || users[0];
    console.log(`   Testing with user: ${testUser.first_name} ${testUser.last_name} (ID: ${testUser.id})`);
    console.log(`   Current status: ${testUser.is_active ? 'ACTIVE' : 'INACTIVE'}`);

    // Toggle the status
    const newStatus = !testUser.is_active;
    console.log(`   Updating user ${testUser.id} from ${testUser.is_active} to ${newStatus}`);

    const [updateResult] = await connection.execute(
      'UPDATE users SET is_active = ?, updated_at = NOW() WHERE id = ?',
      [newStatus, testUser.id]
    );

    console.log(`   Update result:`, updateResult);
    console.log(`   ✅ Updated status to: ${newStatus ? 'ACTIVE' : 'INACTIVE'}`);

    // Verify the change immediately
    const [updatedUser] = await connection.execute(
      'SELECT id, email, first_name, last_name, is_active FROM users WHERE id = ?',
      [testUser.id]
    );

    console.log(`   Database shows: ${updatedUser[0].is_active} (type: ${typeof updatedUser[0].is_active})`);
    console.log(`   Expected: ${newStatus} (type: ${typeof newStatus})`);

    if (Boolean(updatedUser[0].is_active) === Boolean(newStatus)) {
      console.log(`   ✅ Status change verified in database`);
    } else {
      console.log(`   ❌ Status change NOT reflected in database`);
      console.log(`   Raw database value: ${JSON.stringify(updatedUser[0].is_active)}`);
    }

    // Toggle back to original status
    await connection.execute(
      'UPDATE users SET is_active = ?, updated_at = NOW() WHERE id = ?',
      [testUser.is_active, testUser.id]
    );

    console.log(`   🔄 Restored original status: ${testUser.is_active ? 'ACTIVE' : 'INACTIVE'}`);

    console.log('\n📊 Final user status:');
    const [finalUsers] = await connection.execute(
      'SELECT id, email, first_name, last_name, is_active FROM users ORDER BY id'
    );

    finalUsers.forEach(user => {
      console.log(`   ${user.id}: ${user.first_name} ${user.last_name} (${user.email}) - ${user.is_active ? 'ACTIVE' : 'INACTIVE'}`);
    });

    console.log('\n✅ User status toggle test completed successfully!');
    console.log('\n💡 If the frontend still shows incorrect status:');
    console.log('   1. Check browser developer tools for API response');
    console.log('   2. Clear browser cache and refresh');
    console.log('   3. Check if the frontend is properly handling boolean values');

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await connection.end();
  }
}

testUserStatus();
