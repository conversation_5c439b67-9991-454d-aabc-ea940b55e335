/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/approvals/page";
exports.ids = ["app/approvals/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapprovals%2Fpage&page=%2Fapprovals%2Fpage&appPaths=%2Fapprovals%2Fpage&pagePath=private-next-app-dir%2Fapprovals%2Fpage.tsx&appDir=C%3A%5CUsers%5Cijureidini%5CWorkspace%5Cflexair_timekeeping_app%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cijureidini%5CWorkspace%5Cflexair_timekeeping_app%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapprovals%2Fpage&page=%2Fapprovals%2Fpage&appPaths=%2Fapprovals%2Fpage&pagePath=private-next-app-dir%2Fapprovals%2Fpage.tsx&appDir=C%3A%5CUsers%5Cijureidini%5CWorkspace%5Cflexair_timekeeping_app%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cijureidini%5CWorkspace%5Cflexair_timekeeping_app%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?41d8\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/../node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/../node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'approvals',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/approvals/page.tsx */ \"(rsc)/./src/app/approvals/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/../node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/approvals/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/approvals/page\",\n        pathname: \"/approvals\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapprovals%2Fpage&page=%2Fapprovals%2Fpage&appPaths=%2Fapprovals%2Fpage&pagePath=private-next-app-dir%2Fapprovals%2Fpage.tsx&appDir=C%3A%5CUsers%5Cijureidini%5CWorkspace%5Cflexair_timekeeping_app%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cijureidini%5CWorkspace%5Cflexair_timekeeping_app%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Capprovals%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Capprovals%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/approvals/page.tsx */ \"(ssr)/./src/app/approvals/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNpanVyZWlkaW5pJTVDJTVDV29ya3NwYWNlJTVDJTVDZmxleGFpcl90aW1la2VlcGluZ19hcHAlNUMlNUNmcm9udGVuZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2FwcHJvdmFscyU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvS0FBdUkiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mbGV4YWlyLWZyb250ZW5kLz85MDM5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcaWp1cmVpZGluaVxcXFxXb3Jrc3BhY2VcXFxcZmxleGFpcl90aW1la2VlcGluZ19hcHBcXFxcZnJvbnRlbmRcXFxcc3JjXFxcXGFwcFxcXFxhcHByb3ZhbHNcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Capprovals%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(ssr)/./src/contexts/AuthContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNpanVyZWlkaW5pJTVDJTVDV29ya3NwYWNlJTVDJTVDZmxleGFpcl90aW1la2VlcGluZ19hcHAlNUMlNUNmcm9udGVuZCU1QyU1Q3NyYyU1QyU1Q2NvbnRleHRzJTVDJTVDQXV0aENvbnRleHQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQXV0aFByb3ZpZGVyJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2lqdXJlaWRpbmklNUMlNUNXb3Jrc3BhY2UlNUMlNUNmbGV4YWlyX3RpbWVrZWVwaW5nX2FwcCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNmb250JTVDJTVDZ29vZ2xlJTVDJTVDdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJzcmMlNUMlNUMlNUMlNUNhcHAlNUMlNUMlNUMlNUNsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIySW50ZXIlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJpbnRlciU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNpanVyZWlkaW5pJTVDJTVDV29ya3NwYWNlJTVDJTVDZmxleGFpcl90aW1la2VlcGluZ19hcHAlNUMlNUNmcm9udGVuZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3S0FBMEsiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mbGV4YWlyLWZyb250ZW5kLz82NjM5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiQXV0aFByb3ZpZGVyXCJdICovIFwiQzpcXFxcVXNlcnNcXFxcaWp1cmVpZGluaVxcXFxXb3Jrc3BhY2VcXFxcZmxleGFpcl90aW1la2VlcGluZ19hcHBcXFxcZnJvbnRlbmRcXFxcc3JjXFxcXGNvbnRleHRzXFxcXEF1dGhDb250ZXh0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/app-router.js */ \"(ssr)/../node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/client-page.js */ \"(ssr)/../node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/../node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/../node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/../node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/../node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/approvals/page.tsx":
/*!************************************!*\
  !*** ./src/app/approvals/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ApprovalsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/auth/ProtectedRoute */ \"(ssr)/./src/components/auth/ProtectedRoute.tsx\");\n/* harmony import */ var _components_approvals_ManagerApprovalInterface__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/approvals/ManagerApprovalInterface */ \"(ssr)/./src/components/approvals/ManagerApprovalInterface.tsx\");\n/* harmony import */ var _components_approvals_ApprovalHistory__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/approvals/ApprovalHistory */ \"(ssr)/./src/components/approvals/ApprovalHistory.tsx\");\n/* harmony import */ var _lib_approvals__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/approvals */ \"(ssr)/./src/lib/approvals.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n/**\n * Approval Dashboard Page\n * Main page for approval workflow with tabs for different views\n */ function ApprovalsPage() {\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"my-requests\");\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [statsLoading, setStatsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Check if user can manage approvals (admin or manager)\n    const canManageApprovals = user?.role === \"admin\" || user?.role === \"manager\";\n    /**\n   * Load approval statistics for managers/admins\n   */ const loadStats = async ()=>{\n        if (!canManageApprovals) return;\n        try {\n            setStatsLoading(true);\n            const approvalStats = await (0,_lib_approvals__WEBPACK_IMPORTED_MODULE_6__.getApprovalStats)();\n            setStats(approvalStats);\n        } catch (error) {\n            console.error(\"Failed to load approval stats:\", error);\n        } finally{\n            setStatsLoading(false);\n        }\n    };\n    /**\n   * Load stats on component mount\n   */ (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadStats();\n    }, [\n        canManageApprovals\n    ]);\n    /**\n   * Handle approval processed (refresh stats)\n   */ const handleApprovalProcessed = ()=>{\n        loadStats();\n    };\n    /**\n   * Get tab configuration based on user role\n   */ const getTabs = ()=>{\n        const baseTabs = [\n            {\n                id: \"my-requests\",\n                name: \"My Requests\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-5 h-5\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 11\n                }, this)\n            }\n        ];\n        if (canManageApprovals) {\n            baseTabs.push({\n                id: \"pending-approvals\",\n                name: \"Pending Approvals\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-5 h-5\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 13\n                }, this),\n                badge: stats?.pendingRequests || 0\n            }, {\n                id: \"all-history\",\n                name: \"All History\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-5 h-5\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 13\n                }, this)\n            });\n        }\n        return baseTabs;\n    };\n    /**\n   * Set default tab based on user role\n   */ (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (canManageApprovals && stats?.pendingRequests && stats.pendingRequests > 0) {\n            setActiveTab(\"pending-approvals\");\n        }\n    }, [\n        canManageApprovals,\n        stats\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900\",\n                                children: \"Approval Management\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-2 text-gray-600\",\n                                children: canManageApprovals ? \"Manage approval requests and review team submissions\" : \"View and track your approval requests\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 11\n                    }, this),\n                    canManageApprovals && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white overflow-hidden shadow rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-5\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-6 w-6 text-gray-400\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                                        lineNumber: 134,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                                    lineNumber: 133,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-5 w-0 flex-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                            className: \"text-sm font-medium text-gray-500 truncate\",\n                                                            children: \"Total Requests\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                                            lineNumber: 139,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                            className: \"text-lg font-medium text-gray-900\",\n                                                            children: statsLoading ? \"...\" : stats?.totalRequests || 0\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                                            lineNumber: 140,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                                    lineNumber: 138,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white overflow-hidden shadow rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-5\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-6 w-6 text-yellow-400\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                                        lineNumber: 154,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-5 w-0 flex-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                            className: \"text-sm font-medium text-gray-500 truncate\",\n                                                            children: \"Pending\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                                            lineNumber: 159,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                            className: \"text-lg font-medium text-yellow-600\",\n                                                            children: statsLoading ? \"...\" : stats?.pendingRequests || 0\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                                            lineNumber: 160,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                                    lineNumber: 158,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white overflow-hidden shadow rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-5\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-6 w-6 text-green-400\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                                        lineNumber: 174,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                                    lineNumber: 173,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-5 w-0 flex-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                            className: \"text-sm font-medium text-gray-500 truncate\",\n                                                            children: \"Approved\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                                            lineNumber: 179,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                            className: \"text-lg font-medium text-green-600\",\n                                                            children: statsLoading ? \"...\" : stats?.approvedRequests || 0\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                                            lineNumber: 180,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white overflow-hidden shadow rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-5\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-6 w-6 text-red-400\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                                        lineNumber: 194,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                                lineNumber: 192,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-5 w-0 flex-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                            className: \"text-sm font-medium text-gray-500 truncate\",\n                                                            children: \"Rejected\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                                            lineNumber: 199,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                            className: \"text-lg font-medium text-red-600\",\n                                                            children: statsLoading ? \"...\" : stats?.rejectedRequests || 0\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                                            lineNumber: 200,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white shadow rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"-mb-px flex space-x-8 px-6\",\n                                    \"aria-label\": \"Tabs\",\n                                    children: getTabs().map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab(tab.id),\n                                            className: `${activeTab === tab.id ? \"border-blue-500 text-blue-600\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"} whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2`,\n                                            children: [\n                                                tab.icon,\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: tab.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                                    lineNumber: 226,\n                                                    columnNumber: 21\n                                                }, this),\n                                                tab.badge !== undefined && tab.badge > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded-full\",\n                                                    children: tab.badge\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, tab.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: [\n                                    activeTab === \"my-requests\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_approvals_ApprovalHistory__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        viewMode: \"my-requests\",\n                                        showFilters: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 17\n                                    }, this),\n                                    activeTab === \"pending-approvals\" && canManageApprovals && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_approvals_ManagerApprovalInterface__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        onApprovalProcessed: handleApprovalProcessed\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 17\n                                    }, this),\n                                    activeTab === \"all-history\" && canManageApprovals && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_approvals_ApprovalHistory__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        viewMode: \"all-approvals\",\n                                        showFilters: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n                lineNumber: 114,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n            lineNumber: 113,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\approvals\\\\page.tsx\",\n        lineNumber: 112,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/approvals/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/approvals/ApprovalHistory.tsx":
/*!******************************************************!*\
  !*** ./src/components/approvals/ApprovalHistory.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ApprovalHistory)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_approvals__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/approvals */ \"(ssr)/./src/lib/approvals.ts\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n/**\n * ApprovalHistory Component\n * Displays approval request history with status tracking and filtering\n */ function ApprovalHistory({ viewMode = \"my-requests\", showFilters = true }) {\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const [approvals, setApprovals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [pagination, setPagination] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Filters\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        page: 1,\n        limit: 20\n    });\n    /**\n   * Load approvals based on view mode\n   */ const loadApprovals = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            let result;\n            if (viewMode === \"my-requests\") {\n                result = await (0,_lib_approvals__WEBPACK_IMPORTED_MODULE_2__.getMyApprovalRequests)(filters);\n            } else {\n                result = await (0,_lib_approvals__WEBPACK_IMPORTED_MODULE_2__.getApprovals)(filters);\n            }\n            setApprovals(result.requests);\n            setPagination(result.pagination);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"Failed to load approval history\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    /**\n   * Load approvals on component mount and filter changes\n   */ (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadApprovals();\n    }, [\n        filters,\n        viewMode\n    ]);\n    /**\n   * Handle filter changes\n   */ const handleFilterChange = (key, value)=>{\n        setFilters((prev)=>({\n                ...prev,\n                [key]: value,\n                page: 1 // Reset to first page when filters change\n            }));\n    };\n    /**\n   * Handle pagination\n   */ const handlePageChange = (newPage)=>{\n        setFilters((prev)=>({\n                ...prev,\n                page: newPage\n            }));\n    };\n    /**\n   * Format date for display\n   */ const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString(\"en-US\", {\n            year: \"numeric\",\n            month: \"short\",\n            day: \"numeric\",\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    /**\n   * Get status icon\n   */ const getStatusIcon = (status)=>{\n        switch(status){\n            case \"pending\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-5 h-5 text-yellow-500\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                    lineNumber: 115,\n                    columnNumber: 11\n                }, this);\n            case \"approved\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-5 h-5 text-green-500\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 11\n                }, this);\n            case \"rejected\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-5 h-5 text-red-500\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                    lineNumber: 127,\n                    columnNumber: 11\n                }, this);\n            default:\n                return null;\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                lineNumber: 139,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n            lineNumber: 138,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-md\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-6 py-4 border-b border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-gray-900\",\n                        children: viewMode === \"my-requests\" ? \"My Approval Requests\" : \"All Approval Requests\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 9\n                    }, this),\n                    showFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 flex flex-wrap gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: filters.status || \"\",\n                                onChange: (e)=>handleFilterChange(\"status\", e.target.value || undefined),\n                                className: \"px-3 py-2 border border-gray-300 rounded-md text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        children: \"All Status\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"pending\",\n                                        children: \"Pending\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"approved\",\n                                        children: \"Approved\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"rejected\",\n                                        children: \"Rejected\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: filters.requestType || \"\",\n                                onChange: (e)=>handleFilterChange(\"requestType\", e.target.value || undefined),\n                                className: \"px-3 py-2 border border-gray-300 rounded-md text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        children: \"All Types\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"correction\",\n                                        children: \"Time Correction\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"overtime\",\n                                        children: \"Overtime\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"leave\",\n                                        children: \"Leave\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"date\",\n                                value: filters.startDate || \"\",\n                                onChange: (e)=>handleFilterChange(\"startDate\", e.target.value || undefined),\n                                className: \"px-3 py-2 border border-gray-300 rounded-md text-sm\",\n                                placeholder: \"Start Date\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"date\",\n                                value: filters.endDate || \"\",\n                                onChange: (e)=>handleFilterChange(\"endDate\", e.target.value || undefined),\n                                className: \"px-3 py-2 border border-gray-300 rounded-md text-sm\",\n                                placeholder: \"End Date\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                lineNumber: 147,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mx-6 mt-4 p-3 bg-red-50 border border-red-200 rounded-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-red-600\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                    lineNumber: 199,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                lineNumber: 198,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"overflow-x-auto\",\n                children: approvals.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-6 py-8 text-center text-gray-500\",\n                    children: \"No approval requests found.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                    lineNumber: 206,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                    className: \"min-w-full divide-y divide-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                            className: \"bg-gray-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                        children: \"Status\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                        children: \"Type\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                        children: \"Request Details\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                        children: \"Reason\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 17\n                                    }, this),\n                                    viewMode === \"all-approvals\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                        children: \"Requester\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                        children: \"Submitted\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                        children: \"Processed\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                        children: \"Comments\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                            lineNumber: 211,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                            className: \"bg-white divide-y divide-gray-200\",\n                            children: approvals.map((approval)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    className: \"hover:bg-gray-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    getStatusIcon(approval.status),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: `ml-2 inline-flex px-2 py-1 text-xs font-semibold rounded-full ${(0,_lib_approvals__WEBPACK_IMPORTED_MODULE_2__.getStatusBadgeColor)(approval.status)}`,\n                                                        children: approval.status.charAt(0).toUpperCase() + approval.status.slice(1)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-gray-900\",\n                                                children: (0,_lib_approvals__WEBPACK_IMPORTED_MODULE_2__.getRequestTypeDisplayName)(approval.requestType)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-900\",\n                                                children: (0,_lib_approvals__WEBPACK_IMPORTED_MODULE_2__.formatRequestData)(approval.requestType, approval.requestData)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-900 max-w-xs truncate\",\n                                                title: approval.reason,\n                                                children: approval.reason\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 19\n                                        }, this),\n                                        viewMode === \"all-approvals\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm font-medium text-gray-900\",\n                                                    children: approval.requesterName || \"Unknown\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: approval.requesterEmail\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                            children: formatDate(approval.createdAt)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                            children: approval.approvedAt ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: formatDate(approval.approvedAt)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    approval.approverName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-400\",\n                                                        children: [\n                                                            \"by \",\n                                                            approval.approverName\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                                        lineNumber: 285,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 23\n                                            }, this) : \"-\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-900 max-w-xs truncate\",\n                                                title: approval.comments,\n                                                children: approval.comments || \"-\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                            lineNumber: 294,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, approval.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                    lineNumber: 210,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                lineNumber: 204,\n                columnNumber: 7\n            }, this),\n            pagination && pagination.totalPages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-6 py-4 border-t border-gray-200 flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-gray-700\",\n                        children: [\n                            \"Showing \",\n                            (pagination.page - 1) * pagination.limit + 1,\n                            \" to\",\n                            \" \",\n                            Math.min(pagination.page * pagination.limit, pagination.total),\n                            \" of\",\n                            \" \",\n                            pagination.total,\n                            \" results\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                        lineNumber: 309,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handlePageChange(pagination.page - 1),\n                                disabled: pagination.page <= 1,\n                                className: \"px-3 py-1 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: \"Previous\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"px-3 py-1 text-sm font-medium text-gray-700\",\n                                children: [\n                                    \"Page \",\n                                    pagination.page,\n                                    \" of \",\n                                    pagination.totalPages\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handlePageChange(pagination.page + 1),\n                                disabled: pagination.page >= pagination.totalPages,\n                                className: \"px-3 py-1 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: \"Next\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                        lineNumber: 315,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                lineNumber: 308,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n        lineNumber: 145,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/approvals/ApprovalHistory.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/approvals/ManagerApprovalInterface.tsx":
/*!***************************************************************!*\
  !*** ./src/components/approvals/ManagerApprovalInterface.tsx ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ManagerApprovalInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_approvals__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/approvals */ \"(ssr)/./src/lib/approvals.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n/**\n * ManagerApprovalInterface Component\n * Interface for managers to view, approve, and reject pending approval requests\n */ function ManagerApprovalInterface({ onApprovalProcessed }) {\n    const [approvals, setApprovals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedApprovals, setSelectedApprovals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [processModal, setProcessModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [comments, setComments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [processing, setProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Filters\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        page: 1,\n        limit: 20,\n        status: \"pending\"\n    });\n    /**\n   * Load pending approvals\n   */ const loadApprovals = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const result = await (0,_lib_approvals__WEBPACK_IMPORTED_MODULE_2__.getPendingApprovals)(filters);\n            setApprovals(result.requests);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"Failed to load approvals\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    /**\n   * Load approvals on component mount and filter changes\n   */ (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadApprovals();\n    }, [\n        filters\n    ]);\n    /**\n   * Handle filter changes\n   */ const handleFilterChange = (key, value)=>{\n        setFilters((prev)=>({\n                ...prev,\n                [key]: value,\n                page: 1 // Reset to first page when filters change\n            }));\n    };\n    /**\n   * Handle individual approval selection\n   */ const handleApprovalSelect = (approvalId)=>{\n        setSelectedApprovals((prev)=>{\n            const newSet = new Set(prev);\n            if (newSet.has(approvalId)) {\n                newSet.delete(approvalId);\n            } else {\n                newSet.add(approvalId);\n            }\n            return newSet;\n        });\n    };\n    /**\n   * Handle select all approvals\n   */ const handleSelectAll = ()=>{\n        if (selectedApprovals.size === approvals.length) {\n            setSelectedApprovals(new Set());\n        } else {\n            setSelectedApprovals(new Set(approvals.map((a)=>a.id)));\n        }\n    };\n    /**\n   * Open process modal\n   */ const openProcessModal = (approval, action)=>{\n        setProcessModal({\n            approval,\n            action\n        });\n        setComments(\"\");\n    };\n    /**\n   * Close process modal\n   */ const closeProcessModal = ()=>{\n        setProcessModal(null);\n        setComments(\"\");\n    };\n    /**\n   * Process single approval\n   */ const handleProcessApproval = async ()=>{\n        if (!processModal) return;\n        setProcessing(true);\n        try {\n            await (0,_lib_approvals__WEBPACK_IMPORTED_MODULE_2__.processApproval)(processModal.approval.id, {\n                status: processModal.action === \"approve\" ? \"approved\" : \"rejected\",\n                comments: comments.trim() || undefined\n            });\n            // Refresh the list\n            await loadApprovals();\n            // Clear selection\n            setSelectedApprovals(new Set());\n            // Close modal\n            closeProcessModal();\n            if (onApprovalProcessed) {\n                onApprovalProcessed();\n            }\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"Failed to process approval\");\n        } finally{\n            setProcessing(false);\n        }\n    };\n    /**\n   * Process bulk approvals\n   */ const handleBulkProcess = async (action)=>{\n        if (selectedApprovals.size === 0) return;\n        setProcessing(true);\n        try {\n            const promises = Array.from(selectedApprovals).map((id)=>(0,_lib_approvals__WEBPACK_IMPORTED_MODULE_2__.processApproval)(id, {\n                    status: action === \"approve\" ? \"approved\" : \"rejected\",\n                    comments: `Bulk ${action}d by manager`\n                }));\n            await Promise.all(promises);\n            // Refresh the list\n            await loadApprovals();\n            // Clear selection\n            setSelectedApprovals(new Set());\n            if (onApprovalProcessed) {\n                onApprovalProcessed();\n            }\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"Failed to process bulk approvals\");\n        } finally{\n            setProcessing(false);\n        }\n    };\n    /**\n   * Format date for display\n   */ const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString(\"en-US\", {\n            year: \"numeric\",\n            month: \"short\",\n            day: \"numeric\",\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                lineNumber: 207,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n            lineNumber: 206,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-md\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-6 py-4 border-b border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-gray-900\",\n                                children: [\n                                    \"Pending Approvals (\",\n                                    approvals.length,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 11\n                            }, this),\n                            selectedApprovals.size > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleBulkProcess(\"approve\"),\n                                        disabled: processing,\n                                        className: \"px-3 py-1 text-sm font-medium text-white bg-green-600 rounded-md hover:bg-green-700 disabled:opacity-50\",\n                                        children: [\n                                            \"Approve (\",\n                                            selectedApprovals.size,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleBulkProcess(\"reject\"),\n                                        disabled: processing,\n                                        className: \"px-3 py-1 text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700 disabled:opacity-50\",\n                                        children: [\n                                            \"Reject (\",\n                                            selectedApprovals.size,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 flex flex-wrap gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: filters.requestType || \"\",\n                                onChange: (e)=>handleFilterChange(\"requestType\", e.target.value || undefined),\n                                className: \"px-3 py-2 border border-gray-300 rounded-md text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        children: \"All Types\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"correction\",\n                                        children: \"Time Correction\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"overtime\",\n                                        children: \"Overtime\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"leave\",\n                                        children: \"Leave\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"date\",\n                                value: filters.startDate || \"\",\n                                onChange: (e)=>handleFilterChange(\"startDate\", e.target.value || undefined),\n                                className: \"px-3 py-2 border border-gray-300 rounded-md text-sm\",\n                                placeholder: \"Start Date\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"date\",\n                                value: filters.endDate || \"\",\n                                onChange: (e)=>handleFilterChange(\"endDate\", e.target.value || undefined),\n                                className: \"px-3 py-2 border border-gray-300 rounded-md text-sm\",\n                                placeholder: \"End Date\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                lineNumber: 215,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mx-6 mt-4 p-3 bg-red-50 border border-red-200 rounded-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-red-600\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                    lineNumber: 276,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                lineNumber: 275,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"overflow-x-auto\",\n                children: approvals.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-6 py-8 text-center text-gray-500\",\n                    children: \"No pending approvals found.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                    lineNumber: 283,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                    className: \"min-w-full divide-y divide-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                            className: \"bg-gray-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-6 py-3 text-left\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            checked: selectedApprovals.size === approvals.length && approvals.length > 0,\n                                            onChange: handleSelectAll,\n                                            className: \"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                        children: \"Employee\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                        children: \"Type\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                        children: \"Request Details\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                        children: \"Reason\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                        children: \"Submitted\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                        children: \"Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                            className: \"bg-white divide-y divide-gray-200\",\n                            children: approvals.map((approval)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    className: \"hover:bg-gray-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: selectedApprovals.has(approval.id),\n                                                onChange: ()=>handleApprovalSelect(approval.id),\n                                                className: \"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                                lineNumber: 322,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm font-medium text-gray-900\",\n                                                    children: approval.requesterName || \"Unknown\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                                    lineNumber: 330,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: approval.requesterEmail\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: `inline-flex px-2 py-1 text-xs font-semibold rounded-full ${(0,_lib_approvals__WEBPACK_IMPORTED_MODULE_2__.getStatusBadgeColor)(\"pending\")}`,\n                                                children: (0,_lib_approvals__WEBPACK_IMPORTED_MODULE_2__.getRequestTypeDisplayName)(approval.requestType)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                            lineNumber: 337,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-900\",\n                                                children: (0,_lib_approvals__WEBPACK_IMPORTED_MODULE_2__.formatRequestData)(approval.requestType, approval.requestData)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                                lineNumber: 343,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-900 max-w-xs truncate\",\n                                                children: approval.reason\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                                lineNumber: 348,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                            lineNumber: 347,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                            children: formatDate(approval.createdAt)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                            lineNumber: 352,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>openProcessModal(approval, \"approve\"),\n                                                    className: \"text-green-600 hover:text-green-900\",\n                                                    children: \"Approve\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                                    lineNumber: 356,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>openProcessModal(approval, \"reject\"),\n                                                    className: \"text-red-600 hover:text-red-900\",\n                                                    children: \"Reject\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                                    lineNumber: 362,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                            lineNumber: 355,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, approval.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                            lineNumber: 318,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                    lineNumber: 287,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                lineNumber: 281,\n                columnNumber: 7\n            }, this),\n            processModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-4\",\n                                children: [\n                                    processModal.action === \"approve\" ? \"Approve\" : \"Reject\",\n                                    \" Request\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                lineNumber: 381,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4 p-3 bg-gray-50 rounded-md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Employee:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                                lineNumber: 387,\n                                                columnNumber: 19\n                                            }, this),\n                                            \" \",\n                                            processModal.approval.requesterName\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                        lineNumber: 386,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Type:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                                lineNumber: 390,\n                                                columnNumber: 19\n                                            }, this),\n                                            \" \",\n                                            (0,_lib_approvals__WEBPACK_IMPORTED_MODULE_2__.getRequestTypeDisplayName)(processModal.approval.requestType)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                        lineNumber: 389,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Reason:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                                lineNumber: 393,\n                                                columnNumber: 19\n                                            }, this),\n                                            \" \",\n                                            processModal.approval.reason\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                lineNumber: 385,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"Comments (Optional)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                        lineNumber: 398,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        value: comments,\n                                        onChange: (e)=>setComments(e.target.value),\n                                        rows: 3,\n                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                        placeholder: \"Add any comments...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                        lineNumber: 401,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                lineNumber: 397,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-end space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: closeProcessModal,\n                                        className: \"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50\",\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                        lineNumber: 411,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleProcessApproval,\n                                        disabled: processing,\n                                        className: `px-4 py-2 text-sm font-medium text-white rounded-md disabled:opacity-50 ${processModal.action === \"approve\" ? \"bg-green-600 hover:bg-green-700\" : \"bg-red-600 hover:bg-red-700\"}`,\n                                        children: processing ? \"Processing...\" : processModal.action === \"approve\" ? \"Approve\" : \"Reject\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                        lineNumber: 417,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                lineNumber: 410,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                        lineNumber: 380,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                    lineNumber: 379,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                lineNumber: 378,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n        lineNumber: 213,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/approvals/ManagerApprovalInterface.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/auth/ProtectedRoute.tsx":
/*!************************************************!*\
  !*** ./src/components/auth/ProtectedRoute.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProtectedRoute),\n/* harmony export */   withAuth: () => (/* binding */ withAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/../node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/**\r\n * Protected Route Component\r\n * Wraps components that require authentication\r\n */ /* __next_internal_client_entry_do_not_use__ default,withAuth auto */ \n\n\n\n/**\r\n * Protected route component that handles authentication and authorization\r\n * @param children - Components to render if user is authorized\r\n * @param requireAuth - Whether authentication is required (default: true)\r\n * @param requiredRole - Minimum role required to access the route\r\n * @param redirectTo - URL to redirect to if not authorized\r\n * @param fallback - Component to show while loading\r\n */ function ProtectedRoute({ children, requireAuth = true, requiredRole, redirectTo = \"/auth/login\", fallback }) {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user, loading, isAuthenticated } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (loading) return; // Wait for auth state to be determined\n        // If authentication is required but user is not authenticated\n        if (requireAuth && !isAuthenticated) {\n            router.push(redirectTo);\n            return;\n        }\n        // If specific role is required, check user role\n        if (requiredRole && user && !hasRequiredRole(user.role, requiredRole)) {\n            router.push(\"/unauthorized\");\n            return;\n        }\n    }, [\n        loading,\n        isAuthenticated,\n        user,\n        requireAuth,\n        requiredRole,\n        router,\n        redirectTo\n    ]);\n    /**\r\n   * Check if user has required role\r\n   * @param userRole - Current user's role\r\n   * @param requiredRole - Required role\r\n   * @returns True if user has sufficient role\r\n   */ const hasRequiredRole = (userRole, requiredRole)=>{\n        const roleHierarchy = {\n            admin: 3,\n            manager: 2,\n            employee: 1\n        };\n        return roleHierarchy[userRole] >= roleHierarchy[requiredRole];\n    };\n    // Show loading state\n    if (loading) {\n        return fallback || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoadingSpinner, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n            lineNumber: 75,\n            columnNumber: 24\n        }, this);\n    }\n    // Show unauthorized if auth is required but user is not authenticated\n    if (requireAuth && !isAuthenticated) {\n        return null; // Router will handle redirect\n    }\n    // Show unauthorized if role is required but user doesn't have it\n    if (requiredRole && user && !hasRequiredRole(user.role, requiredRole)) {\n        return null; // Router will handle redirect\n    }\n    // Render children if all checks pass\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n/**\r\n * Loading spinner component\r\n */ function LoadingSpinner() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"animate-spin -ml-1 mr-3 h-8 w-8 text-indigo-600\",\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                            className: \"opacity-25\",\n                            cx: \"12\",\n                            cy: \"12\",\n                            r: \"10\",\n                            stroke: \"currentColor\",\n                            strokeWidth: \"4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            className: \"opacity-75\",\n                            fill: \"currentColor\",\n                            d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mt-2 text-sm text-gray-600\",\n                    children: \"Loading...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                    lineNumber: 119,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n            lineNumber: 98,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n        lineNumber: 97,\n        columnNumber: 5\n    }, this);\n}\n/**\r\n * Higher-order component for protecting pages\r\n * @param Component - Component to protect\r\n * @param options - Protection options\r\n * @returns Protected component\r\n */ function withAuth(Component, options = {}) {\n    const ProtectedComponent = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProtectedRoute, {\n            ...options,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...props\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n            lineNumber: 136,\n            columnNumber: 5\n        }, this);\n    ProtectedComponent.displayName = `withAuth(${Component.displayName || Component.name})`;\n    return ProtectedComponent;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/auth/ProtectedRoute.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(ssr)/./src/lib/auth.ts\");\n/**\r\n * Authentication Context Provider\r\n * Manages global authentication state and provides auth methods\r\n */ /* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n/**\r\n * Authentication Provider Component\r\n * Wraps the app and provides authentication state and methods\r\n * @param children - Child components to render\r\n */ function AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    /**\r\n   * Clear any authentication errors\r\n   */ const clearError = ()=>setError(null);\n    /**\r\n   * Initialize authentication state on mount\r\n   */ (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        initializeAuth();\n    }, []);\n    /**\r\n   * Initialize authentication state\r\n   * Checks for existing tokens and validates them\r\n   */ const initializeAuth = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            if (!(0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.isAuthenticated)()) {\n                setLoading(false);\n                return;\n            }\n            const token = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.getAccessToken)();\n            if (!token || (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.isTokenExpired)(token)) {\n                // Try to refresh token\n                try {\n                    await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.refreshTokens)();\n                } catch (error) {\n                    (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.clearTokens)();\n                    setLoading(false);\n                    return;\n                }\n            }\n            // Verify token and get user data\n            const { valid, user: userData } = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.verifyToken)();\n            if (valid && userData) {\n                setUser(userData);\n            } else {\n                (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.clearTokens)();\n            }\n        } catch (error) {\n            console.error(\"Auth initialization error:\", error);\n            (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.clearTokens)();\n        } finally{\n            setLoading(false);\n        }\n    };\n    /**\r\n   * Login user with credentials\r\n   * @param credentials - Login credentials\r\n   */ const login = async (credentials)=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const response = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.login)(credentials);\n            setUser(response.user);\n        } catch (error) {\n            setError(error.message || \"Login failed\");\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    /**\r\n   * Register new user\r\n   * @param userData - Registration data\r\n   */ const register = async (userData)=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const response = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.register)(userData);\n            setUser(response.user);\n        } catch (error) {\n            setError(error.message || \"Registration failed\");\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    /**\r\n   * Logout current user\r\n   */ const logout = async ()=>{\n        try {\n            setLoading(true);\n            await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.logout)();\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n        } finally{\n            setUser(null);\n            setLoading(false);\n        }\n    };\n    /**\r\n   * Refresh current user data\r\n   */ const refreshUser = async ()=>{\n        try {\n            const userData = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.getCurrentUser)();\n            setUser(userData);\n        } catch (error) {\n            console.error(\"Failed to refresh user data:\", error);\n        }\n    };\n    const value = {\n        user,\n        loading,\n        error,\n        isAuthenticated: !!user,\n        login,\n        register,\n        logout,\n        clearError,\n        refreshUser\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 186,\n        columnNumber: 5\n    }, this);\n}\n/**\r\n * Hook to use authentication context\r\n * @returns Authentication context\r\n * @throws Error if used outside AuthProvider\r\n */ function useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/approvals.ts":
/*!******************************!*\
  !*** ./src/lib/approvals.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createApprovalRequest: () => (/* binding */ createApprovalRequest),\n/* harmony export */   formatRequestData: () => (/* binding */ formatRequestData),\n/* harmony export */   getApprovalById: () => (/* binding */ getApprovalById),\n/* harmony export */   getApprovalStats: () => (/* binding */ getApprovalStats),\n/* harmony export */   getApprovals: () => (/* binding */ getApprovals),\n/* harmony export */   getMyApprovalRequests: () => (/* binding */ getMyApprovalRequests),\n/* harmony export */   getPendingApprovals: () => (/* binding */ getPendingApprovals),\n/* harmony export */   getRequestTypeDisplayName: () => (/* binding */ getRequestTypeDisplayName),\n/* harmony export */   getStatusBadgeColor: () => (/* binding */ getStatusBadgeColor),\n/* harmony export */   processApproval: () => (/* binding */ processApproval)\n/* harmony export */ });\n/* harmony import */ var _auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./auth */ \"(ssr)/./src/lib/auth.ts\");\n/**\n * Approval API service functions\n * Handles all approval-related API calls\n */ \nconst API_BASE_URL = \"http://localhost:5002/api\" || 0;\n/**\n * Make authenticated API request\n */ async function makeRequest(endpoint, options = {}) {\n    const token = (0,_auth__WEBPACK_IMPORTED_MODULE_0__.getAccessToken)();\n    const response = await fetch(`${API_BASE_URL}${endpoint}`, {\n        ...options,\n        headers: {\n            \"Content-Type\": \"application/json\",\n            \"Authorization\": `Bearer ${token}`,\n            ...options.headers\n        }\n    });\n    if (!response.ok) {\n        const errorData = await response.json().catch(()=>({}));\n        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);\n    }\n    return response.json();\n}\n/**\n * Create a new approval request\n */ async function createApprovalRequest(data) {\n    const response = await makeRequest(\"/approvals\", {\n        method: \"POST\",\n        body: JSON.stringify(data)\n    });\n    if (!response.success || !response.data) {\n        throw new Error(response.error || \"Failed to create approval request\");\n    }\n    return response.data;\n}\n/**\n * Get my approval requests\n */ async function getMyApprovalRequests(filters = {}) {\n    const queryParams = new URLSearchParams();\n    Object.entries(filters).forEach(([key, value])=>{\n        if (value !== undefined && value !== null) {\n            queryParams.append(key, value.toString());\n        }\n    });\n    const response = await makeRequest(`/approvals/my-requests?${queryParams.toString()}`);\n    if (!response.success) {\n        throw new Error(\"Failed to fetch approval requests\");\n    }\n    return {\n        requests: response.data,\n        pagination: response.pagination\n    };\n}\n/**\n * Get pending approvals (manager/admin only)\n */ async function getPendingApprovals(filters = {}) {\n    const queryParams = new URLSearchParams();\n    Object.entries(filters).forEach(([key, value])=>{\n        if (value !== undefined && value !== null) {\n            queryParams.append(key, value.toString());\n        }\n    });\n    const response = await makeRequest(`/approvals/pending?${queryParams.toString()}`);\n    if (!response.success) {\n        throw new Error(\"Failed to fetch pending approvals\");\n    }\n    return {\n        requests: response.data,\n        pagination: response.pagination\n    };\n}\n/**\n * Get all approvals with filtering\n */ async function getApprovals(filters = {}) {\n    const queryParams = new URLSearchParams();\n    Object.entries(filters).forEach(([key, value])=>{\n        if (value !== undefined && value !== null) {\n            queryParams.append(key, value.toString());\n        }\n    });\n    const response = await makeRequest(`/approvals?${queryParams.toString()}`);\n    if (!response.success) {\n        throw new Error(\"Failed to fetch approvals\");\n    }\n    return {\n        requests: response.data,\n        pagination: response.pagination\n    };\n}\n/**\n * Get approval by ID\n */ async function getApprovalById(id) {\n    const response = await makeRequest(`/approvals/${id}`);\n    if (!response.success || !response.data) {\n        throw new Error(response.error || \"Failed to fetch approval\");\n    }\n    return response.data;\n}\n/**\n * Process approval (approve/reject) - manager/admin only\n */ async function processApproval(id, data) {\n    const response = await makeRequest(`/approvals/${id}`, {\n        method: \"PUT\",\n        body: JSON.stringify(data)\n    });\n    if (!response.success || !response.data) {\n        throw new Error(response.error || \"Failed to process approval\");\n    }\n    return response.data;\n}\n/**\n * Get approval statistics (admin/manager only)\n */ async function getApprovalStats(filters = {}) {\n    const queryParams = new URLSearchParams();\n    Object.entries(filters).forEach(([key, value])=>{\n        if (value !== undefined && value !== null) {\n            queryParams.append(key, value.toString());\n        }\n    });\n    const response = await makeRequest(`/approvals/stats?${queryParams.toString()}`);\n    if (!response.success || !response.data) {\n        throw new Error(response.error || \"Failed to fetch approval statistics\");\n    }\n    return response.data;\n}\n/**\n * Utility functions\n */ /**\n * Get status badge color for approval status\n */ function getStatusBadgeColor(status) {\n    switch(status){\n        case \"pending\":\n            return \"bg-yellow-100 text-yellow-800\";\n        case \"approved\":\n            return \"bg-green-100 text-green-800\";\n        case \"rejected\":\n            return \"bg-red-100 text-red-800\";\n        default:\n            return \"bg-gray-100 text-gray-800\";\n    }\n}\n/**\n * Get request type display name\n */ function getRequestTypeDisplayName(type) {\n    switch(type){\n        case \"correction\":\n            return \"Time Correction\";\n        case \"overtime\":\n            return \"Overtime Request\";\n        case \"leave\":\n            return \"Leave Request\";\n        default:\n            return type;\n    }\n}\n/**\n * Format approval request data for display\n */ function formatRequestData(requestType, requestData) {\n    try {\n        switch(requestType){\n            case \"correction\":\n                return `Clock In: ${requestData.clockIn || \"N/A\"}, Clock Out: ${requestData.clockOut || \"N/A\"}`;\n            case \"overtime\":\n                return `Hours: ${requestData.hours || \"N/A\"}, Date: ${requestData.date || \"N/A\"}`;\n            case \"leave\":\n                return `From: ${requestData.startDate || \"N/A\"}, To: ${requestData.endDate || \"N/A\"}`;\n            default:\n                return JSON.stringify(requestData);\n        }\n    } catch  {\n        return \"Invalid request data\";\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/approvals.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiRequest: () => (/* binding */ apiRequest),\n/* harmony export */   clearTokens: () => (/* binding */ clearTokens),\n/* harmony export */   decodeToken: () => (/* binding */ decodeToken),\n/* harmony export */   getAccessToken: () => (/* binding */ getAccessToken),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   getRefreshToken: () => (/* binding */ getRefreshToken),\n/* harmony export */   isAuthenticated: () => (/* binding */ isAuthenticated),\n/* harmony export */   isTokenExpired: () => (/* binding */ isTokenExpired),\n/* harmony export */   login: () => (/* binding */ login),\n/* harmony export */   logout: () => (/* binding */ logout),\n/* harmony export */   refreshTokens: () => (/* binding */ refreshTokens),\n/* harmony export */   register: () => (/* binding */ register),\n/* harmony export */   setTokens: () => (/* binding */ setTokens),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/**\r\n * Authentication utilities and API calls\r\n * Handles login, registration, token management, and user session\r\n */ const API_BASE = \"http://localhost:5002/api\" || 0;\n/**\r\n * API response wrapper for error handling\r\n */ class ApiError extends Error {\n    constructor(message, status, code){\n        super(message);\n        this.status = status;\n        this.code = code;\n        this.name = \"ApiError\";\n    }\n}\n/**\r\n * Make authenticated API request with automatic token refresh\r\n * @param url - API endpoint URL\r\n * @param options - Fetch options\r\n * @returns Response data\r\n */ async function apiRequest(url, options = {}) {\n    const token = getAccessToken();\n    const config = {\n        headers: {\n            \"Content-Type\": \"application/json\",\n            ...token && {\n                Authorization: `Bearer ${token}`\n            },\n            ...options.headers\n        },\n        ...options\n    };\n    const response = await fetch(`${API_BASE}${url}`, config);\n    if (!response.ok) {\n        const errorData = await response.json().catch(()=>({}));\n        throw new ApiError(errorData.error?.message || \"Request failed\", response.status, errorData.error?.code);\n    }\n    return response.json();\n}\n/**\r\n * Login user with email and password\r\n * @param credentials - Login credentials\r\n * @returns Authentication response with user data and tokens\r\n */ async function login(credentials) {\n    const response = await apiRequest(\"/auth/login\", {\n        method: \"POST\",\n        body: JSON.stringify(credentials)\n    });\n    // Store tokens\n    setTokens(response.tokens);\n    return response;\n}\n/**\r\n * Register new user account\r\n * @param userData - Registration data\r\n * @returns Authentication response with user data and tokens\r\n */ async function register(userData) {\n    const response = await apiRequest(\"/auth/register\", {\n        method: \"POST\",\n        body: JSON.stringify(userData)\n    });\n    // Store tokens\n    setTokens(response.tokens);\n    return response;\n}\n/**\r\n * Logout user and clear tokens\r\n */ async function logout() {\n    const refreshToken = getRefreshToken();\n    try {\n        await apiRequest(\"/auth/logout\", {\n            method: \"POST\",\n            body: JSON.stringify({\n                refreshToken\n            })\n        });\n    } catch (error) {\n        // Continue with logout even if API call fails\n        console.warn(\"Logout API call failed:\", error);\n    }\n    clearTokens();\n}\n/**\r\n * Get current user profile\r\n * @returns Current user data\r\n */ async function getCurrentUser() {\n    const response = await apiRequest(\"/auth/profile\");\n    return response.user;\n}\n/**\r\n * Verify if current token is valid\r\n * @returns Token validity and user data\r\n */ async function verifyToken() {\n    try {\n        const response = await apiRequest(\"/auth/verify\");\n        return {\n            valid: response.valid,\n            user: response.user\n        };\n    } catch (error) {\n        return {\n            valid: false\n        };\n    }\n}\n/**\r\n * Refresh access token using refresh token\r\n * @returns New authentication tokens\r\n */ async function refreshTokens() {\n    const refreshToken = getRefreshToken();\n    if (!refreshToken) {\n        throw new Error(\"No refresh token available\");\n    }\n    const response = await fetch(`${API_BASE}/auth/refresh`, {\n        method: \"POST\",\n        headers: {\n            \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n            refreshToken\n        })\n    });\n    if (!response.ok) {\n        clearTokens();\n        throw new Error(\"Token refresh failed\");\n    }\n    const data = await response.json();\n    setTokens(data.tokens);\n    return data.tokens;\n}\n/**\r\n * Store authentication tokens in localStorage\r\n * @param tokens - Authentication tokens to store\r\n */ function setTokens(tokens) {\n    if (false) {}\n}\n/**\r\n * Get stored access token\r\n * @returns Access token or null\r\n */ function getAccessToken() {\n    if (false) {}\n    return null;\n}\n/**\r\n * Get stored refresh token\r\n * @returns Refresh token or null\r\n */ function getRefreshToken() {\n    if (false) {}\n    return null;\n}\n/**\r\n * Clear all stored authentication tokens\r\n */ function clearTokens() {\n    if (false) {}\n}\n/**\r\n * Check if user is currently authenticated\r\n * @returns True if user has valid tokens\r\n */ function isAuthenticated() {\n    return !!(getAccessToken() && getRefreshToken());\n}\n/**\r\n * Decode JWT token payload without verification\r\n * @param token - JWT token to decode\r\n * @returns Decoded payload or null\r\n */ function decodeToken(token) {\n    try {\n        const payload = token.split(\".\")[1];\n        return JSON.parse(atob(payload));\n    } catch (error) {\n        return null;\n    }\n}\n/**\r\n * Check if token is expired\r\n * @param token - JWT token to check\r\n * @returns True if token is expired\r\n */ function isTokenExpired(token) {\n    const decoded = decodeToken(token);\n    if (!decoded || !decoded.exp) return true;\n    return Date.now() >= decoded.exp * 1000;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"fbdb7e167290\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZmxleGFpci1mcm9udGVuZC8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/MTc0ZCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImZiZGI3ZTE2NzI5MFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/approvals/page.tsx":
/*!************************************!*\
  !*** ./src/app/approvals/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Workspace\flexair_timekeeping_app\frontend\src\app\approvals\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/../node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(rsc)/./src/contexts/AuthContext.tsx\");\n/**\r\n * Root layout component for the Flexair Timekeeping App\r\n * Provides global styling, context providers, and common layout structure\r\n */ \n\n\n\nconst metadata = {\n    title: \"Flexair Timekeeping\",\n    description: \"Modern timekeeping application with biometric integration\",\n    keywords: [\n        \"timekeeping\",\n        \"attendance\",\n        \"biometric\",\n        \"HR\",\n        \"workforce management\"\n    ],\n    authors: [\n        {\n            name: \"Flexair Team\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\",\n    robots: \"noindex, nofollow\"\n};\n/**\r\n * Root layout component with authentication context\r\n * @param children - Child components to render\r\n */ function RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className)} h-full bg-gray-50 antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-full\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 34,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 33,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ e0),\n/* harmony export */   useAuth: () => (/* binding */ e1)\n/* harmony export */ });\n/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ \"(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js\");\n\n\nconst e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\\Users\\<USER>\\Workspace\\flexair_timekeeping_app\\frontend\\src\\contexts\\AuthContext.tsx#AuthProvider`);\n\nconst e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\\Users\\<USER>\\Workspace\\flexair_timekeeping_app\\frontend\\src\\contexts\\AuthContext.tsx#useAuth`);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/contexts/AuthContext.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapprovals%2Fpage&page=%2Fapprovals%2Fpage&appPaths=%2Fapprovals%2Fpage&pagePath=private-next-app-dir%2Fapprovals%2Fpage.tsx&appDir=C%3A%5CUsers%5Cijureidini%5CWorkspace%5Cflexair_timekeeping_app%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cijureidini%5CWorkspace%5Cflexair_timekeeping_app%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();