/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/users/page";
exports.ids = ["app/users/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fusers%2Fpage&page=%2Fusers%2Fpage&appPaths=%2Fusers%2Fpage&pagePath=private-next-app-dir%2Fusers%2Fpage.tsx&appDir=C%3A%5CUsers%5Cijureidini%5CWorkspace%5Cflexair_timekeeping_app%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cijureidini%5CWorkspace%5Cflexair_timekeeping_app%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fusers%2Fpage&page=%2Fusers%2Fpage&appPaths=%2Fusers%2Fpage&pagePath=private-next-app-dir%2Fusers%2Fpage.tsx&appDir=C%3A%5CUsers%5Cijureidini%5CWorkspace%5Cflexair_timekeeping_app%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cijureidini%5CWorkspace%5Cflexair_timekeeping_app%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?41d8\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/../node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/../node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'users',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/users/page.tsx */ \"(rsc)/./src/app/users/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\users\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/../node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\users\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/users/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/users/page\",\n        pathname: \"/users\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fusers%2Fpage&page=%2Fusers%2Fpage&appPaths=%2Fusers%2Fpage&pagePath=private-next-app-dir%2Fusers%2Fpage.tsx&appDir=C%3A%5CUsers%5Cijureidini%5CWorkspace%5Cflexair_timekeeping_app%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cijureidini%5CWorkspace%5Cflexair_timekeeping_app%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cusers%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cusers%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/users/page.tsx */ \"(ssr)/./src/app/users/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNpanVyZWlkaW5pJTVDJTVDV29ya3NwYWNlJTVDJTVDZmxleGFpcl90aW1la2VlcGluZ19hcHAlNUMlNUNmcm9udGVuZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3VzZXJzJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDRKQUFtSSIsInNvdXJjZXMiOlsid2VicGFjazovL2ZsZXhhaXItZnJvbnRlbmQvPzc4ZTMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxpanVyZWlkaW5pXFxcXFdvcmtzcGFjZVxcXFxmbGV4YWlyX3RpbWVrZWVwaW5nX2FwcFxcXFxmcm9udGVuZFxcXFxzcmNcXFxcYXBwXFxcXHVzZXJzXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cusers%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(ssr)/./src/contexts/AuthContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNpanVyZWlkaW5pJTVDJTVDV29ya3NwYWNlJTVDJTVDZmxleGFpcl90aW1la2VlcGluZ19hcHAlNUMlNUNmcm9udGVuZCU1QyU1Q3NyYyU1QyU1Q2NvbnRleHRzJTVDJTVDQXV0aENvbnRleHQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQXV0aFByb3ZpZGVyJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2lqdXJlaWRpbmklNUMlNUNXb3Jrc3BhY2UlNUMlNUNmbGV4YWlyX3RpbWVrZWVwaW5nX2FwcCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNmb250JTVDJTVDZ29vZ2xlJTVDJTVDdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJzcmMlNUMlNUMlNUMlNUNhcHAlNUMlNUMlNUMlNUNsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIySW50ZXIlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJpbnRlciU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNpanVyZWlkaW5pJTVDJTVDV29ya3NwYWNlJTVDJTVDZmxleGFpcl90aW1la2VlcGluZ19hcHAlNUMlNUNmcm9udGVuZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3S0FBMEsiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mbGV4YWlyLWZyb250ZW5kLz82NjM5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiQXV0aFByb3ZpZGVyXCJdICovIFwiQzpcXFxcVXNlcnNcXFxcaWp1cmVpZGluaVxcXFxXb3Jrc3BhY2VcXFxcZmxleGFpcl90aW1la2VlcGluZ19hcHBcXFxcZnJvbnRlbmRcXFxcc3JjXFxcXGNvbnRleHRzXFxcXEF1dGhDb250ZXh0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/app-router.js */ \"(ssr)/../node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/client-page.js */ \"(ssr)/../node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/../node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/../node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/../node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/../node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/users/page.tsx":
/*!********************************!*\
  !*** ./src/app/users/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/../node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/auth/ProtectedRoute */ \"(ssr)/./src/components/auth/ProtectedRoute.tsx\");\n/* harmony import */ var _components_users_UserList__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/users/UserList */ \"(ssr)/./src/components/users/UserList.tsx\");\n/* harmony import */ var _components_users_UserProfile__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/users/UserProfile */ \"(ssr)/./src/components/users/UserProfile.tsx\");\n/* harmony import */ var _components_users_UserForm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/users/UserForm */ \"(ssr)/./src/components/users/UserForm.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n/**\n * Users management page\n * Displays user list with options to view, edit, and create users\n */ function UsersPage() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [selectedUser, setSelectedUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingUser, setEditingUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showCreateForm, setShowCreateForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [view, setView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"list\");\n    /**\n   * Handle user selection for viewing profile\n   */ const handleUserSelect = (user)=>{\n        setSelectedUser(user);\n        setView(\"profile\");\n    };\n    /**\n   * Handle user edit\n   */ const handleUserEdit = (user)=>{\n        setEditingUser(user);\n        setView(\"edit\");\n    };\n    /**\n   * Handle create new user\n   */ const handleUserCreate = ()=>{\n        setShowCreateForm(true);\n        setView(\"create\");\n    };\n    /**\n   * Handle successful user creation/update\n   */ const handleUserSuccess = (user)=>{\n        setView(\"list\");\n        setSelectedUser(null);\n        setEditingUser(null);\n        setShowCreateForm(false);\n    // The UserList component will automatically refresh\n    };\n    /**\n   * Handle cancel/close actions\n   */ const handleCancel = ()=>{\n        setView(\"list\");\n        setSelectedUser(null);\n        setEditingUser(null);\n        setShowCreateForm(false);\n    };\n    /**\n   * Handle edit from profile view\n   */ const handleEditFromProfile = ()=>{\n        if (selectedUser) {\n            setEditingUser(selectedUser);\n            setView(\"edit\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        requiredRoles: [\n            \"admin\",\n            \"manager\"\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                    className: \"bg-white shadow\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center py-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>router.push(\"/dashboard\"),\n                                            className: \"text-gray-500 hover:text-gray-700\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-5 h-5\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: \"2\",\n                                                    d: \"M15 19l-7-7 7-7\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\users\\\\page.tsx\",\n                                                    lineNumber: 90,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\users\\\\page.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\users\\\\page.tsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: [\n                                                        view === \"list\" && \"User Management\",\n                                                        view === \"profile\" && \"User Profile\",\n                                                        view === \"edit\" && \"Edit User\",\n                                                        view === \"create\" && \"Create User\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\users\\\\page.tsx\",\n                                                    lineNumber: 94,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: [\n                                                        view === \"list\" && \"Manage user accounts and permissions\",\n                                                        view === \"profile\" && \"View user details and information\",\n                                                        view === \"edit\" && \"Update user information and settings\",\n                                                        view === \"create\" && \"Add a new user to the system\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\users\\\\page.tsx\",\n                                                    lineNumber: 100,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\users\\\\page.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\users\\\\page.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"flex items-center space-x-2 text-sm text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>router.push(\"/dashboard\"),\n                                            className: \"hover:text-gray-700\",\n                                            children: \"Dashboard\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\users\\\\page.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"/\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\users\\\\page.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleCancel,\n                                            className: `hover:text-gray-700 ${view === \"list\" ? \"text-gray-900 font-medium\" : \"\"}`,\n                                            children: \"Users\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\users\\\\page.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 17\n                                        }, this),\n                                        view !== \"list\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"/\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\users\\\\page.tsx\",\n                                                    lineNumber: 126,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-900 font-medium\",\n                                                    children: [\n                                                        view === \"profile\" && selectedUser && `${selectedUser.firstName} ${selectedUser.lastName}`,\n                                                        view === \"edit\" && \"Edit\",\n                                                        view === \"create\" && \"New User\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\users\\\\page.tsx\",\n                                                    lineNumber: 127,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\users\\\\page.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\users\\\\page.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\users\\\\page.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\users\\\\page.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\",\n                    children: [\n                        view === \"list\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_users_UserList__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            onUserSelect: handleUserSelect,\n                            onUserEdit: handleUserEdit,\n                            onUserCreate: handleUserCreate\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\users\\\\page.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 13\n                        }, this),\n                        view === \"profile\" && selectedUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_users_UserProfile__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            user: selectedUser,\n                            onEdit: handleEditFromProfile,\n                            onClose: handleCancel\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\users\\\\page.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 13\n                        }, this),\n                        view === \"edit\" && editingUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_users_UserForm__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            user: editingUser,\n                            mode: \"edit\",\n                            onSuccess: handleUserSuccess,\n                            onCancel: handleCancel\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\users\\\\page.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 13\n                        }, this),\n                        view === \"create\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_users_UserForm__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            mode: \"create\",\n                            onSuccess: handleUserSuccess,\n                            onCancel: handleCancel\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\users\\\\page.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\users\\\\page.tsx\",\n                    lineNumber: 140,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\users\\\\page.tsx\",\n            lineNumber: 79,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\users\\\\page.tsx\",\n        lineNumber: 78,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (UsersPage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/users/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/auth/ProtectedRoute.tsx":
/*!************************************************!*\
  !*** ./src/components/auth/ProtectedRoute.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProtectedRoute),\n/* harmony export */   withAuth: () => (/* binding */ withAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/../node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/**\r\n * Protected Route Component\r\n * Wraps components that require authentication\r\n */ /* __next_internal_client_entry_do_not_use__ default,withAuth auto */ \n\n\n\n/**\r\n * Protected route component that handles authentication and authorization\r\n * @param children - Components to render if user is authorized\r\n * @param requireAuth - Whether authentication is required (default: true)\r\n * @param requiredRole - Minimum role required to access the route\r\n * @param redirectTo - URL to redirect to if not authorized\r\n * @param fallback - Component to show while loading\r\n */ function ProtectedRoute({ children, requireAuth = true, requiredRole, redirectTo = \"/auth/login\", fallback }) {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user, loading, isAuthenticated } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (loading) return; // Wait for auth state to be determined\n        // If authentication is required but user is not authenticated\n        if (requireAuth && !isAuthenticated) {\n            router.push(redirectTo);\n            return;\n        }\n        // If specific role is required, check user role\n        if (requiredRole && user && !hasRequiredRole(user.role, requiredRole)) {\n            router.push(\"/unauthorized\");\n            return;\n        }\n    }, [\n        loading,\n        isAuthenticated,\n        user,\n        requireAuth,\n        requiredRole,\n        router,\n        redirectTo\n    ]);\n    /**\r\n   * Check if user has required role\r\n   * @param userRole - Current user's role\r\n   * @param requiredRole - Required role\r\n   * @returns True if user has sufficient role\r\n   */ const hasRequiredRole = (userRole, requiredRole)=>{\n        const roleHierarchy = {\n            admin: 3,\n            manager: 2,\n            employee: 1\n        };\n        return roleHierarchy[userRole] >= roleHierarchy[requiredRole];\n    };\n    // Show loading state\n    if (loading) {\n        return fallback || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoadingSpinner, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n            lineNumber: 75,\n            columnNumber: 24\n        }, this);\n    }\n    // Show unauthorized if auth is required but user is not authenticated\n    if (requireAuth && !isAuthenticated) {\n        return null; // Router will handle redirect\n    }\n    // Show unauthorized if role is required but user doesn't have it\n    if (requiredRole && user && !hasRequiredRole(user.role, requiredRole)) {\n        return null; // Router will handle redirect\n    }\n    // Render children if all checks pass\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n/**\r\n * Loading spinner component\r\n */ function LoadingSpinner() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"animate-spin -ml-1 mr-3 h-8 w-8 text-indigo-600\",\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                            className: \"opacity-25\",\n                            cx: \"12\",\n                            cy: \"12\",\n                            r: \"10\",\n                            stroke: \"currentColor\",\n                            strokeWidth: \"4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            className: \"opacity-75\",\n                            fill: \"currentColor\",\n                            d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mt-2 text-sm text-gray-600\",\n                    children: \"Loading...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                    lineNumber: 119,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n            lineNumber: 98,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n        lineNumber: 97,\n        columnNumber: 5\n    }, this);\n}\n/**\r\n * Higher-order component for protecting pages\r\n * @param Component - Component to protect\r\n * @param options - Protection options\r\n * @returns Protected component\r\n */ function withAuth(Component, options = {}) {\n    const ProtectedComponent = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProtectedRoute, {\n            ...options,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...props\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n            lineNumber: 136,\n            columnNumber: 5\n        }, this);\n    ProtectedComponent.displayName = `withAuth(${Component.displayName || Component.name})`;\n    return ProtectedComponent;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/auth/ProtectedRoute.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/users/UserForm.tsx":
/*!*******************************************!*\
  !*** ./src/components/users/UserForm.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ UserForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_users__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/users */ \"(ssr)/./src/lib/users.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n/**\n * UserForm component for creating and editing users\n * Includes validation, role selection, and proper error handling\n */ function UserForm({ user, onSuccess, onCancel, mode = \"create\" }) {\n    const { user: currentUser } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Form data state\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        email: \"\",\n        password: \"\",\n        confirmPassword: \"\",\n        firstName: \"\",\n        lastName: \"\",\n        department: \"\",\n        role: \"employee\",\n        employeeId: \"\",\n        phone: \"\",\n        isActive: true\n    });\n    // Form validation state\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [touched, setTouched] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Initialize form data when editing\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (mode === \"edit\" && user) {\n            setFormData({\n                email: user.email || \"\",\n                password: \"\",\n                confirmPassword: \"\",\n                firstName: user.firstName || \"\",\n                lastName: user.lastName || \"\",\n                department: user.department || \"\",\n                role: user.role || \"employee\",\n                employeeId: user.employeeId || \"\",\n                phone: user.phone || \"\",\n                isActive: user.isActive ?? true\n            });\n        }\n    }, [\n        user,\n        mode\n    ]);\n    /**\n   * Handle input changes\n   */ const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        // Clear error when user starts typing\n        if (errors[field]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [field]: \"\"\n                }));\n        }\n    };\n    /**\n   * Handle input blur (for validation)\n   */ const handleInputBlur = (field)=>{\n        setTouched((prev)=>({\n                ...prev,\n                [field]: true\n            }));\n        validateField(field, formData[field]);\n    };\n    /**\n   * Validate individual field\n   */ const validateField = (field, value)=>{\n        let error = \"\";\n        switch(field){\n            case \"email\":\n                if (!value) {\n                    error = \"Email is required\";\n                } else if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(value)) {\n                    error = \"Please enter a valid email address\";\n                }\n                break;\n            case \"password\":\n                if (mode === \"create\" && !value) {\n                    error = \"Password is required\";\n                } else if (value && value.length < 8) {\n                    error = \"Password must be at least 8 characters long\";\n                } else if (value && !/(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)/.test(value)) {\n                    error = \"Password must contain at least one uppercase letter, one lowercase letter, and one number\";\n                }\n                break;\n            case \"confirmPassword\":\n                if (formData.password && value !== formData.password) {\n                    error = \"Passwords do not match\";\n                }\n                break;\n            case \"firstName\":\n                if (!value) {\n                    error = \"First name is required\";\n                } else if (value.length < 2) {\n                    error = \"First name must be at least 2 characters long\";\n                }\n                break;\n            case \"lastName\":\n                if (!value) {\n                    error = \"Last name is required\";\n                } else if (value.length < 2) {\n                    error = \"Last name must be at least 2 characters long\";\n                }\n                break;\n            case \"employeeId\":\n                if (value && !/^[A-Za-z0-9-_]+$/.test(value)) {\n                    error = \"Employee ID can only contain letters, numbers, hyphens, and underscores\";\n                }\n                break;\n            case \"phone\":\n                if (value && !/^\\+?[\\d\\s\\-\\(\\)]+$/.test(value)) {\n                    error = \"Please enter a valid phone number\";\n                }\n                break;\n        }\n        setErrors((prev)=>({\n                ...prev,\n                [field]: error\n            }));\n        return error;\n    };\n    /**\n   * Validate entire form\n   */ const validateForm = ()=>{\n        const newErrors = {};\n        // Validate all required fields\n        Object.keys(formData).forEach((field)=>{\n            const error = validateField(field, formData[field]);\n            if (error) {\n                newErrors[field] = error;\n            }\n        });\n        setErrors(newErrors);\n        setTouched(Object.keys(formData).reduce((acc, key)=>({\n                ...acc,\n                [key]: true\n            }), {}));\n        return Object.keys(newErrors).length === 0;\n    };\n    /**\n   * Handle form submission\n   */ const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            setError(\"Please fix the errors below\");\n            return;\n        }\n        setLoading(true);\n        setError(\"\");\n        setMessage(\"\");\n        try {\n            let result;\n            if (mode === \"create\") {\n                const createData = {\n                    email: formData.email,\n                    password: formData.password,\n                    firstName: formData.firstName,\n                    lastName: formData.lastName,\n                    department: formData.department || undefined,\n                    role: formData.role,\n                    employeeId: formData.employeeId || undefined,\n                    phone: formData.phone || undefined\n                };\n                result = await (0,_lib_users__WEBPACK_IMPORTED_MODULE_3__.createUser)(createData);\n                setMessage(\"User created successfully!\");\n            } else {\n                const updateData = {\n                    email: formData.email,\n                    firstName: formData.firstName,\n                    lastName: formData.lastName,\n                    department: formData.department || undefined,\n                    role: formData.role,\n                    employeeId: formData.employeeId || undefined,\n                    phone: formData.phone || undefined,\n                    isActive: formData.isActive\n                };\n                result = await (0,_lib_users__WEBPACK_IMPORTED_MODULE_3__.updateUser)(user.id, updateData);\n                setMessage(\"User updated successfully!\");\n            }\n            if (onSuccess) {\n                onSuccess(result);\n            }\n        } catch (err) {\n            console.error(\"Form submission error:\", err);\n            if (err.message?.includes(\"email already exists\") || err.message?.includes(\"EMAIL_EXISTS\")) {\n                setErrors((prev)=>({\n                        ...prev,\n                        email: \"This email is already in use\"\n                    }));\n                setError(\"Email address is already in use\");\n            } else {\n                setError(err.message || `Failed to ${mode} user. Please try again.`);\n            }\n        } finally{\n            setLoading(false);\n        }\n    };\n    /**\n   * Check if current user can edit roles\n   */ const canEditRole = currentUser?.role === \"admin\";\n    const canEditStatus = currentUser?.role === \"admin\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-md p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-800\",\n                        children: mode === \"create\" ? \"Create New User\" : \"Edit User\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserForm.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mt-1\",\n                        children: mode === \"create\" ? \"Add a new user to the system\" : \"Update user information and settings\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserForm.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserForm.tsx\",\n                lineNumber: 243,\n                columnNumber: 7\n            }, this),\n            (message || error) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-md\",\n                        children: message\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserForm.tsx\",\n                        lineNumber: 259,\n                        columnNumber: 13\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserForm.tsx\",\n                        lineNumber: 264,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserForm.tsx\",\n                lineNumber: 257,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"firstName\",\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"First Name *\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserForm.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        id: \"firstName\",\n                                        value: formData.firstName,\n                                        onChange: (e)=>handleInputChange(\"firstName\", e.target.value),\n                                        onBlur: ()=>handleInputBlur(\"firstName\"),\n                                        className: `w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 ${errors.firstName && touched.firstName ? \"border-red-300\" : \"border-gray-300\"}`,\n                                        disabled: loading\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserForm.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.firstName && touched.firstName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-1 text-sm text-red-600\",\n                                        children: errors.firstName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserForm.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserForm.tsx\",\n                                lineNumber: 274,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"lastName\",\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"Last Name *\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserForm.tsx\",\n                                        lineNumber: 295,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        id: \"lastName\",\n                                        value: formData.lastName,\n                                        onChange: (e)=>handleInputChange(\"lastName\", e.target.value),\n                                        onBlur: ()=>handleInputBlur(\"lastName\"),\n                                        className: `w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 ${errors.lastName && touched.lastName ? \"border-red-300\" : \"border-gray-300\"}`,\n                                        disabled: loading\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserForm.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.lastName && touched.lastName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-1 text-sm text-red-600\",\n                                        children: errors.lastName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserForm.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserForm.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserForm.tsx\",\n                        lineNumber: 273,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"email\",\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"Email Address *\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserForm.tsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"email\",\n                                        id: \"email\",\n                                        value: formData.email,\n                                        onChange: (e)=>handleInputChange(\"email\", e.target.value),\n                                        onBlur: ()=>handleInputBlur(\"email\"),\n                                        className: `w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 ${errors.email && touched.email ? \"border-red-300\" : \"border-gray-300\"}`,\n                                        disabled: loading\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserForm.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.email && touched.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-1 text-sm text-red-600\",\n                                        children: errors.email\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserForm.tsx\",\n                                        lineNumber: 333,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserForm.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"phone\",\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"Phone Number\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserForm.tsx\",\n                                        lineNumber: 338,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"tel\",\n                                        id: \"phone\",\n                                        value: formData.phone,\n                                        onChange: (e)=>handleInputChange(\"phone\", e.target.value),\n                                        onBlur: ()=>handleInputBlur(\"phone\"),\n                                        className: `w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 ${errors.phone && touched.phone ? \"border-red-300\" : \"border-gray-300\"}`,\n                                        disabled: loading,\n                                        placeholder: \"+****************\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserForm.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.phone && touched.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-1 text-sm text-red-600\",\n                                        children: errors.phone\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserForm.tsx\",\n                                        lineNumber: 354,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserForm.tsx\",\n                                lineNumber: 337,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserForm.tsx\",\n                        lineNumber: 316,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"department\",\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"Department\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserForm.tsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        id: \"department\",\n                                        value: formData.department,\n                                        onChange: (e)=>handleInputChange(\"department\", e.target.value),\n                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500\",\n                                        disabled: loading,\n                                        placeholder: \"e.g., Engineering, HR, Sales\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserForm.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserForm.tsx\",\n                                lineNumber: 361,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"employeeId\",\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"Employee ID\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserForm.tsx\",\n                                        lineNumber: 377,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        id: \"employeeId\",\n                                        value: formData.employeeId,\n                                        onChange: (e)=>handleInputChange(\"employeeId\", e.target.value),\n                                        onBlur: ()=>handleInputBlur(\"employeeId\"),\n                                        className: `w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 ${errors.employeeId && touched.employeeId ? \"border-red-300\" : \"border-gray-300\"}`,\n                                        disabled: loading,\n                                        placeholder: \"e.g., EMP001\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserForm.tsx\",\n                                        lineNumber: 380,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.employeeId && touched.employeeId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-1 text-sm text-red-600\",\n                                        children: errors.employeeId\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserForm.tsx\",\n                                        lineNumber: 393,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserForm.tsx\",\n                                lineNumber: 376,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserForm.tsx\",\n                        lineNumber: 360,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"role\",\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"Role\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserForm.tsx\",\n                                        lineNumber: 401,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        id: \"role\",\n                                        value: formData.role,\n                                        onChange: (e)=>handleInputChange(\"role\", e.target.value),\n                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500\",\n                                        disabled: loading || !canEditRole,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"employee\",\n                                                children: \"Employee\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserForm.tsx\",\n                                                lineNumber: 411,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"manager\",\n                                                children: \"Manager\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserForm.tsx\",\n                                                lineNumber: 412,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"admin\",\n                                                children: \"Admin\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserForm.tsx\",\n                                                lineNumber: 413,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserForm.tsx\",\n                                        lineNumber: 404,\n                                        columnNumber: 13\n                                    }, this),\n                                    !canEditRole && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-1 text-sm text-gray-500\",\n                                        children: \"Only admins can change user roles\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserForm.tsx\",\n                                        lineNumber: 416,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserForm.tsx\",\n                                lineNumber: 400,\n                                columnNumber: 11\n                            }, this),\n                            mode === \"edit\" && canEditStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"isActive\",\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"Status\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserForm.tsx\",\n                                        lineNumber: 422,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        id: \"isActive\",\n                                        value: formData.isActive.toString(),\n                                        onChange: (e)=>handleInputChange(\"isActive\", e.target.value === \"true\"),\n                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500\",\n                                        disabled: loading,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"true\",\n                                                children: \"Active\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserForm.tsx\",\n                                                lineNumber: 432,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"false\",\n                                                children: \"Inactive\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserForm.tsx\",\n                                                lineNumber: 433,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserForm.tsx\",\n                                        lineNumber: 425,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserForm.tsx\",\n                                lineNumber: 421,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserForm.tsx\",\n                        lineNumber: 399,\n                        columnNumber: 9\n                    }, this),\n                    mode === \"create\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"password\",\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"Password *\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserForm.tsx\",\n                                        lineNumber: 443,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"password\",\n                                        id: \"password\",\n                                        value: formData.password,\n                                        onChange: (e)=>handleInputChange(\"password\", e.target.value),\n                                        onBlur: ()=>handleInputBlur(\"password\"),\n                                        className: `w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 ${errors.password && touched.password ? \"border-red-300\" : \"border-gray-300\"}`,\n                                        disabled: loading\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserForm.tsx\",\n                                        lineNumber: 446,\n                                        columnNumber: 15\n                                    }, this),\n                                    errors.password && touched.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-1 text-sm text-red-600\",\n                                        children: errors.password\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserForm.tsx\",\n                                        lineNumber: 458,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserForm.tsx\",\n                                lineNumber: 442,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"confirmPassword\",\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"Confirm Password *\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserForm.tsx\",\n                                        lineNumber: 463,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"password\",\n                                        id: \"confirmPassword\",\n                                        value: formData.confirmPassword,\n                                        onChange: (e)=>handleInputChange(\"confirmPassword\", e.target.value),\n                                        onBlur: ()=>handleInputBlur(\"confirmPassword\"),\n                                        className: `w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 ${errors.confirmPassword && touched.confirmPassword ? \"border-red-300\" : \"border-gray-300\"}`,\n                                        disabled: loading\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserForm.tsx\",\n                                        lineNumber: 466,\n                                        columnNumber: 15\n                                    }, this),\n                                    errors.confirmPassword && touched.confirmPassword && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-1 text-sm text-red-600\",\n                                        children: errors.confirmPassword\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserForm.tsx\",\n                                        lineNumber: 478,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserForm.tsx\",\n                                lineNumber: 462,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserForm.tsx\",\n                        lineNumber: 441,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row gap-4 pt-6 border-t border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                disabled: loading,\n                                className: \"flex-1 bg-indigo-600 hover:bg-indigo-700 disabled:bg-gray-400 text-white font-medium py-2 px-4 rounded-md transition duration-200\",\n                                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserForm.tsx\",\n                                            lineNumber: 493,\n                                            columnNumber: 17\n                                        }, this),\n                                        mode === \"create\" ? \"Creating...\" : \"Updating...\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserForm.tsx\",\n                                    lineNumber: 492,\n                                    columnNumber: 15\n                                }, this) : mode === \"create\" ? \"Create User\" : \"Update User\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserForm.tsx\",\n                                lineNumber: 486,\n                                columnNumber: 11\n                            }, this),\n                            onCancel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: onCancel,\n                                disabled: loading,\n                                className: \"flex-1 bg-gray-300 hover:bg-gray-400 disabled:bg-gray-200 text-gray-700 font-medium py-2 px-4 rounded-md transition duration-200\",\n                                children: \"Cancel\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserForm.tsx\",\n                                lineNumber: 502,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserForm.tsx\",\n                        lineNumber: 485,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserForm.tsx\",\n                lineNumber: 271,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserForm.tsx\",\n        lineNumber: 242,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/users/UserForm.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/users/UserList.tsx":
/*!*******************************************!*\
  !*** ./src/components/users/UserList.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ UserList)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_users__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/users */ \"(ssr)/./src/lib/users.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n/**\n * UserList component for displaying and managing users\n * Includes pagination, search, filtering, and role-based actions\n */ function UserList({ onUserSelect, onUserEdit, onUserCreate }) {\n    const { user: currentUser } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Pagination and filtering state\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalUsers, setTotalUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // UI state\n    const [selectedUsers, setSelectedUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const itemsPerPage = 20;\n    // Load users on component mount and when filters/page change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadUsers();\n    }, [\n        currentPage,\n        filters\n    ]);\n    // Debounced search\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timer = setTimeout(()=>{\n            if (searchQuery !== (filters.search || \"\")) {\n                setFilters((prev)=>({\n                        ...prev,\n                        search: searchQuery\n                    }));\n                setCurrentPage(1);\n            }\n        }, 500);\n        return ()=>clearTimeout(timer);\n    }, [\n        searchQuery\n    ]);\n    /**\n   * Load users from API\n   */ const loadUsers = async ()=>{\n        try {\n            setLoading(true);\n            setError(\"\");\n            const response = await (0,_lib_users__WEBPACK_IMPORTED_MODULE_3__.getAllUsers)(filters, currentPage, itemsPerPage);\n            console.log(\"Loaded users:\", response.users.map((u)=>({\n                    id: u.id,\n                    email: u.email,\n                    isActive: u.isActive,\n                    type: typeof u.isActive\n                })));\n            setUsers(response.users);\n            setTotalPages(response.pagination.totalPages);\n            setTotalUsers(response.pagination.total);\n        } catch (err) {\n            setError(\"Failed to load users. Please try again.\");\n            console.error(\"Error loading users:\", err);\n        } finally{\n            setLoading(false);\n        }\n    };\n    /**\n   * Handle user deletion\n   */ const handleDeleteUser = async (userId)=>{\n        if (!window.confirm(\"Are you sure you want to deactivate this user?\")) {\n            return;\n        }\n        try {\n            await (0,_lib_users__WEBPACK_IMPORTED_MODULE_3__.deleteUser)(userId);\n            setMessage(\"User deactivated successfully\");\n            await loadUsers();\n        } catch (err) {\n            setError(\"Failed to deactivate user\");\n            console.error(\"Error deleting user:\", err);\n        }\n    };\n    /**\n   * Handle user status toggle\n   */ const handleToggleStatus = async (userId, isActive)=>{\n        try {\n            console.log(`Toggling user ${userId} from ${isActive} to ${!isActive}`);\n            const updatedUser = await (0,_lib_users__WEBPACK_IMPORTED_MODULE_3__.toggleUserStatus)(userId, !isActive);\n            console.log(\"Updated user received:\", updatedUser);\n            setMessage(`User ${!isActive ? \"activated\" : \"deactivated\"} successfully`);\n            await loadUsers();\n        } catch (err) {\n            setError(\"Failed to update user status\");\n            console.error(\"Error toggling user status:\", err);\n        }\n    };\n    /**\n   * Handle filter changes\n   */ const handleFilterChange = (key, value)=>{\n        setFilters((prev)=>({\n                ...prev,\n                [key]: value === \"\" ? undefined : value\n            }));\n        setCurrentPage(1);\n    };\n    /**\n   * Clear all filters\n   */ const clearFilters = ()=>{\n        setFilters({});\n        setSearchQuery(\"\");\n        setCurrentPage(1);\n    };\n    /**\n   * Handle bulk actions\n   */ const handleBulkAction = async (action)=>{\n        if (selectedUsers.size === 0) {\n            setError(\"Please select users first\");\n            return;\n        }\n        if (!window.confirm(`Are you sure you want to ${action} ${selectedUsers.size} user(s)?`)) {\n            return;\n        }\n        try {\n            const promises = Array.from(selectedUsers).map((userId)=>(0,_lib_users__WEBPACK_IMPORTED_MODULE_3__.toggleUserStatus)(userId, action === \"activate\"));\n            await Promise.all(promises);\n            setMessage(`${selectedUsers.size} user(s) ${action}d successfully`);\n            setSelectedUsers(new Set());\n            await loadUsers();\n        } catch (err) {\n            setError(`Failed to ${action} users`);\n            console.error(`Error in bulk ${action}:`, err);\n        }\n    };\n    /**\n   * Check if current user can perform admin actions\n   */ const canPerformAdminActions = currentUser?.role === \"admin\";\n    const canPerformManagerActions = currentUser?.role === \"admin\" || currentUser?.role === \"manager\";\n    /**\n   * Format user role for display\n   */ const formatRole = (role)=>{\n        return role ? role.charAt(0).toUpperCase() + role.slice(1) : \"\";\n    };\n    /**\n   * Format date for display\n   */ const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString(\"en-US\", {\n            year: \"numeric\",\n            month: \"short\",\n            day: \"numeric\"\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"compact-card\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card-header\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold text-gray-900\",\n                                    children: \"Users\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: [\n                                        totalUsers,\n                                        \" total users\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                            lineNumber: 196,\n                            columnNumber: 11\n                        }, this),\n                        canPerformAdminActions && onUserCreate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onUserCreate,\n                            className: \"btn btn-primary\",\n                            children: \"Add User\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                    lineNumber: 195,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                lineNumber: 194,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 py-3 border-b border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"Search users by name or email...\",\n                                    value: searchQuery,\n                                    onChange: (e)=>setSearchQuery(e.target.value),\n                                    className: \"form-input\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowFilters(!showFilters),\n                                className: \"btn btn-outline\",\n                                children: [\n                                    \"Filters \",\n                                    showFilters ? \"▲\" : \"▼\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 9\n                    }, this),\n                    showFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3 grid grid-cols-1 sm:grid-cols-3 gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: filters.role || \"\",\n                                onChange: (e)=>handleFilterChange(\"role\", e.target.value),\n                                className: \"form-input\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        children: \"All Roles\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"admin\",\n                                        children: \"Admin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"manager\",\n                                        children: \"Manager\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"employee\",\n                                        children: \"Employee\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: filters.isActive === undefined ? \"\" : filters.isActive.toString(),\n                                onChange: (e)=>handleFilterChange(\"isActive\", e.target.value),\n                                className: \"form-input\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        children: \"All Status\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"true\",\n                                        children: \"Active\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"false\",\n                                        children: \"Inactive\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: clearFilters,\n                                className: \"btn btn-outline\",\n                                children: \"Clear Filters\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                lineNumber: 215,\n                columnNumber: 7\n            }, this),\n            canPerformAdminActions && selectedUsers.size > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 py-2 bg-gray-50 border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm text-gray-600\",\n                            children: [\n                                selectedUsers.size,\n                                \" user(s) selected\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                            lineNumber: 275,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>handleBulkAction(\"activate\"),\n                            className: \"btn btn-sm btn-success\",\n                            children: \"Activate\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>handleBulkAction(\"deactivate\"),\n                            className: \"btn btn-sm btn-error\",\n                            children: \"Deactivate\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                            lineNumber: 284,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                    lineNumber: 274,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                lineNumber: 273,\n                columnNumber: 9\n            }, this),\n            (message || error) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-6 py-3 border-b border-gray-200\",\n                children: [\n                    message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-green-600 text-sm\",\n                        children: message\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                        lineNumber: 298,\n                        columnNumber: 13\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-red-600 text-sm\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                        lineNumber: 301,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                lineNumber: 296,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"overflow-x-auto\",\n                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-6 py-8 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                            lineNumber: 310,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-gray-600\",\n                            children: \"Loading users...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                            lineNumber: 311,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                    lineNumber: 309,\n                    columnNumber: 11\n                }, this) : users.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-6 py-8 text-center text-gray-500\",\n                    children: \"No users found\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                    lineNumber: 314,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                    className: \"min-w-full divide-y divide-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                            className: \"table-header\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                children: [\n                                    canPerformAdminActions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"table-header-cell\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            checked: selectedUsers.size === users.length && users.length > 0,\n                                            onChange: (e)=>{\n                                                if (e.target.checked) {\n                                                    setSelectedUsers(new Set(users.map((u)=>u.id)));\n                                                } else {\n                                                    setSelectedUsers(new Set());\n                                                }\n                                            },\n                                            className: \"rounded border-gray-300 text-indigo-600 focus:ring-indigo-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"table-header-cell\",\n                                        children: \"User\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"table-header-cell\",\n                                        children: \"Role\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                        lineNumber: 340,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"table-header-cell\",\n                                        children: \"Status\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                        lineNumber: 343,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"table-header-cell\",\n                                        children: \"Last Login\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                        lineNumber: 346,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"table-header-cell\",\n                                        children: \"Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                        lineNumber: 349,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                lineNumber: 320,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                            lineNumber: 319,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                            className: \"bg-white divide-y divide-gray-200\",\n                            children: users.map((user)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    className: \"table-row\",\n                                    children: [\n                                        canPerformAdminActions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"table-cell\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: selectedUsers.has(user.id),\n                                                onChange: (e)=>{\n                                                    const newSelected = new Set(selectedUsers);\n                                                    if (e.target.checked) {\n                                                        newSelected.add(user.id);\n                                                    } else {\n                                                        newSelected.delete(user.id);\n                                                    }\n                                                    setSelectedUsers(newSelected);\n                                                },\n                                                className: \"rounded border-gray-300 text-indigo-600 focus:ring-indigo-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                                lineNumber: 359,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                            lineNumber: 358,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"table-cell\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-shrink-0 icon-container\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"icon-container bg-indigo-100\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-indigo-600\",\n                                                                children: [\n                                                                    user.firstName?.charAt(0) || \"\",\n                                                                    user.lastName?.charAt(0) || \"\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                                                lineNumber: 379,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                                            lineNumber: 378,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                                        lineNumber: 377,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"ml-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm font-medium text-gray-900\",\n                                                                children: [\n                                                                    user.firstName,\n                                                                    \" \",\n                                                                    user.lastName\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                                                lineNumber: 385,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: user.email\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                                                lineNumber: 388,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            user.department && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: user.department\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                                                lineNumber: 392,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                                        lineNumber: 384,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"table-cell\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: `badge ${user.role === \"admin\" ? \"badge-primary\" : user.role === \"manager\" ? \"badge-secondary\" : \"bg-gray-100 text-gray-800\"}`,\n                                                children: formatRole(user.role)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                                lineNumber: 400,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                            lineNumber: 399,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"table-cell\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: `badge ${user.isActive ? \"badge-success\" : \"badge-error\"}`,\n                                                children: user.isActive ? \"Active\" : \"Inactive\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                                lineNumber: 411,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                            lineNumber: 410,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"table-cell text-gray-500\",\n                                            children: user.lastLogin ? formatDate(user.lastLogin) : \"Never\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                            lineNumber: 419,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"table-cell font-medium\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    onUserSelect && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>onUserSelect(user),\n                                                        className: \"text-indigo-600 hover:text-indigo-900\",\n                                                        children: \"View\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                                        lineNumber: 425,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    canPerformManagerActions && onUserEdit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>onUserEdit(user),\n                                                        className: \"text-blue-600 hover:text-blue-900\",\n                                                        children: \"Edit\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                                        lineNumber: 434,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    canPerformAdminActions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleToggleStatus(user.id, user.isActive),\n                                                            className: `${user.isActive ? \"text-red-600 hover:text-red-900\" : \"text-green-600 hover:text-green-900\"}`,\n                                                            children: user.isActive ? \"Deactivate\" : \"Activate\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                                            lineNumber: 444,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                                lineNumber: 423,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                            lineNumber: 422,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, user.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                    lineNumber: 356,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                            lineNumber: 354,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                    lineNumber: 318,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                lineNumber: 307,\n                columnNumber: 7\n            }, this),\n            totalPages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-6 py-4 border-t border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-700\",\n                            children: [\n                                \"Showing \",\n                                (currentPage - 1) * itemsPerPage + 1,\n                                \" to \",\n                                Math.min(currentPage * itemsPerPage, totalUsers),\n                                \" of \",\n                                totalUsers,\n                                \" users\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                            lineNumber: 469,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setCurrentPage((prev)=>Math.max(prev - 1, 1)),\n                                    disabled: currentPage === 1,\n                                    className: \"px-3 py-1 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50\",\n                                    children: \"Previous\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                    lineNumber: 473,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"px-3 py-1 text-sm text-gray-700\",\n                                    children: [\n                                        \"Page \",\n                                        currentPage,\n                                        \" of \",\n                                        totalPages\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                    lineNumber: 481,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setCurrentPage((prev)=>Math.min(prev + 1, totalPages)),\n                                    disabled: currentPage === totalPages,\n                                    className: \"px-3 py-1 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50\",\n                                    children: \"Next\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                    lineNumber: 485,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                            lineNumber: 472,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                    lineNumber: 468,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                lineNumber: 467,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n        lineNumber: 192,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/users/UserList.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/users/UserProfile.tsx":
/*!**********************************************!*\
  !*** ./src/components/users/UserProfile.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ UserProfile)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_users__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/users */ \"(ssr)/./src/lib/users.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n/**\n * UserProfile component for displaying detailed user information\n * Includes profile view, password change, and action buttons\n */ function UserProfile({ user, onEdit, onClose, showActions = true }) {\n    const { user: currentUser } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [showPasswordForm, setShowPasswordForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [passwordForm, setPasswordForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        currentPassword: \"\",\n        newPassword: \"\",\n        confirmPassword: \"\"\n    });\n    const [passwordLoading, setPasswordLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [passwordError, setPasswordError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [passwordMessage, setPasswordMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    /**\n   * Check if current user can edit this profile\n   */ const canEdit = currentUser?.role === \"admin\" || currentUser?.role === \"manager\" || currentUser?.id === user.id;\n    const canChangePassword = currentUser?.id === user.id;\n    /**\n   * Handle password change\n   */ const handlePasswordChange = async (e)=>{\n        e.preventDefault();\n        if (passwordForm.newPassword !== passwordForm.confirmPassword) {\n            setPasswordError(\"Passwords do not match\");\n            return;\n        }\n        if (passwordForm.newPassword.length < 8) {\n            setPasswordError(\"Password must be at least 8 characters long\");\n            return;\n        }\n        setPasswordLoading(true);\n        setPasswordError(\"\");\n        setPasswordMessage(\"\");\n        try {\n            await (0,_lib_users__WEBPACK_IMPORTED_MODULE_3__.updateUserPassword)(user.id, passwordForm.currentPassword, passwordForm.newPassword);\n            setPasswordMessage(\"Password updated successfully!\");\n            setPasswordForm({\n                currentPassword: \"\",\n                newPassword: \"\",\n                confirmPassword: \"\"\n            });\n            setShowPasswordForm(false);\n        } catch (err) {\n            setPasswordError(err.message || \"Failed to update password\");\n        } finally{\n            setPasswordLoading(false);\n        }\n    };\n    /**\n   * Format date for display\n   */ const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString(\"en-US\", {\n            year: \"numeric\",\n            month: \"long\",\n            day: \"numeric\",\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    /**\n   * Format role for display\n   */ const formatRole = (role)=>{\n        return role ? role.charAt(0).toUpperCase() + role.slice(1) : \"\";\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-md overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-indigo-500 to-purple-600 px-6 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-20 w-20 rounded-full bg-white bg-opacity-20 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-2xl font-bold text-white\",\n                                        children: [\n                                            user.firstName?.charAt(0) || \"\",\n                                            user.lastName?.charAt(0) || \"\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-2xl font-bold text-white\",\n                                            children: [\n                                                user.firstName,\n                                                \" \",\n                                                user.lastName\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-indigo-100\",\n                                            children: user.email\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4 mt-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: `inline-flex px-2 py-1 text-xs font-semibold rounded-full ${user.role === \"admin\" ? \"bg-purple-100 text-purple-800\" : user.role === \"manager\" ? \"bg-blue-100 text-blue-800\" : \"bg-gray-100 text-gray-800\"}`,\n                                                    children: formatRole(user.role)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: `inline-flex px-2 py-1 text-xs font-semibold rounded-full ${user.isActive ? \"bg-green-100 text-green-800\" : \"bg-red-100 text-red-800\"}`,\n                                                    children: user.isActive ? \"Active\" : \"Inactive\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                    lineNumber: 122,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 11\n                        }, this),\n                        onClose && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"text-white hover:text-gray-200 transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-6 h-6\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: \"2\",\n                                    d: \"M6 18L18 6M6 6l12 12\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                    lineNumber: 100,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                lineNumber: 99,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6\",\n                children: [\n                    passwordMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-md\",\n                        children: passwordMessage\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                        children: \"Personal Information\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-500\",\n                                                        children: \"Full Name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                        lineNumber: 161,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-900\",\n                                                        children: [\n                                                            user.firstName,\n                                                            \" \",\n                                                            user.lastName\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                        lineNumber: 162,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-500\",\n                                                        children: \"Email\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-900\",\n                                                        children: user.email\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                        lineNumber: 166,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 15\n                                            }, this),\n                                            user.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-500\",\n                                                        children: \"Phone\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                        lineNumber: 170,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-900\",\n                                                        children: user.phone\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                        lineNumber: 171,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                        children: \"Work Information\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            user.employeeId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-500\",\n                                                        children: \"Employee ID\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                        lineNumber: 182,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-900\",\n                                                        children: user.employeeId\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                        lineNumber: 183,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 17\n                                            }, this),\n                                            user.department && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-500\",\n                                                        children: \"Department\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                        lineNumber: 188,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-900\",\n                                                        children: user.department\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                        lineNumber: 189,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-500\",\n                                                        children: \"Role\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                        lineNumber: 193,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-900\",\n                                                        children: formatRole(user.role)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                        lineNumber: 194,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                lineNumber: 192,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                children: \"Account Information\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-500\",\n                                                children: \"Account Status\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: `font-medium ${user.isActive ? \"text-green-600\" : \"text-red-600\"}`,\n                                                children: user.isActive ? \"Active\" : \"Inactive\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-500\",\n                                                children: \"Email Verified\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: `font-medium ${user.emailVerified ? \"text-green-600\" : \"text-yellow-600\"}`,\n                                                children: user.emailVerified ? \"Verified\" : \"Not Verified\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-500\",\n                                                children: \"Biometric Enabled\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: `font-medium ${user.biometricEnabled ? \"text-green-600\" : \"text-gray-600\"}`,\n                                                children: user.biometricEnabled ? \"Enabled\" : \"Disabled\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-500\",\n                                                children: \"Member Since\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-900\",\n                                                children: formatDate(user.createdAt)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 13\n                                    }, this),\n                                    user.lastLogin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-500\",\n                                                children: \"Last Login\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-900\",\n                                                children: formatDate(user.lastLogin)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 15\n                                    }, this),\n                                    user.updatedAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-500\",\n                                                children: \"Last Updated\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                lineNumber: 234,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-900\",\n                                                children: formatDate(user.updatedAt)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 9\n                    }, this),\n                    canChangePassword && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900\",\n                                        children: \"Security\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowPasswordForm(!showPasswordForm),\n                                        className: \"text-indigo-600 hover:text-indigo-800 text-sm font-medium\",\n                                        children: showPasswordForm ? \"Cancel\" : \"Change Password\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 13\n                            }, this),\n                            showPasswordForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handlePasswordChange,\n                                className: \"bg-gray-50 p-4 rounded-lg\",\n                                children: [\n                                    passwordError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4 bg-red-50 border border-red-200 text-red-700 px-3 py-2 rounded-md text-sm\",\n                                        children: passwordError\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"currentPassword\",\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: \"Current Password\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                        lineNumber: 264,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"password\",\n                                                        id: \"currentPassword\",\n                                                        value: passwordForm.currentPassword,\n                                                        onChange: (e)=>setPasswordForm((prev)=>({\n                                                                    ...prev,\n                                                                    currentPassword: e.target.value\n                                                                })),\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500\",\n                                                        disabled: passwordLoading,\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"newPassword\",\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: \"New Password\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"password\",\n                                                        id: \"newPassword\",\n                                                        value: passwordForm.newPassword,\n                                                        onChange: (e)=>setPasswordForm((prev)=>({\n                                                                    ...prev,\n                                                                    newPassword: e.target.value\n                                                                })),\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500\",\n                                                        disabled: passwordLoading,\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                        lineNumber: 281,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"confirmPassword\",\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: \"Confirm Password\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                        lineNumber: 292,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"password\",\n                                                        id: \"confirmPassword\",\n                                                        value: passwordForm.confirmPassword,\n                                                        onChange: (e)=>setPasswordForm((prev)=>({\n                                                                    ...prev,\n                                                                    confirmPassword: e.target.value\n                                                                })),\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500\",\n                                                        disabled: passwordLoading,\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                        lineNumber: 295,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 flex justify-end\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            disabled: passwordLoading,\n                                            className: \"bg-indigo-600 hover:bg-indigo-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md text-sm font-medium\",\n                                            children: passwordLoading ? \"Updating...\" : \"Update Password\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 11\n                    }, this),\n                    showActions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row gap-4 pt-6 border-t border-gray-200\",\n                        children: [\n                            canEdit && onEdit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onEdit,\n                                className: \"bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-2 rounded-md font-medium\",\n                                children: \"Edit Profile\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                lineNumber: 325,\n                                columnNumber: 15\n                            }, this),\n                            onClose && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onClose,\n                                className: \"bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-2 rounded-md font-medium\",\n                                children: \"Close\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                lineNumber: 334,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                        lineNumber: 323,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                lineNumber: 147,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n        lineNumber: 97,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/users/UserProfile.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(ssr)/./src/lib/auth.ts\");\n/**\r\n * Authentication Context Provider\r\n * Manages global authentication state and provides auth methods\r\n */ /* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n/**\r\n * Authentication Provider Component\r\n * Wraps the app and provides authentication state and methods\r\n * @param children - Child components to render\r\n */ function AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    /**\r\n   * Clear any authentication errors\r\n   */ const clearError = ()=>setError(null);\n    /**\r\n   * Initialize authentication state on mount\r\n   */ (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        initializeAuth();\n    }, []);\n    /**\r\n   * Initialize authentication state\r\n   * Checks for existing tokens and validates them\r\n   */ const initializeAuth = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            if (!(0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.isAuthenticated)()) {\n                setLoading(false);\n                return;\n            }\n            const token = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.getAccessToken)();\n            if (!token || (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.isTokenExpired)(token)) {\n                // Try to refresh token\n                try {\n                    await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.refreshTokens)();\n                } catch (error) {\n                    (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.clearTokens)();\n                    setLoading(false);\n                    return;\n                }\n            }\n            // Verify token and get user data\n            const { valid, user: userData } = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.verifyToken)();\n            if (valid && userData) {\n                setUser(userData);\n            } else {\n                (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.clearTokens)();\n            }\n        } catch (error) {\n            console.error(\"Auth initialization error:\", error);\n            (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.clearTokens)();\n        } finally{\n            setLoading(false);\n        }\n    };\n    /**\r\n   * Login user with credentials\r\n   * @param credentials - Login credentials\r\n   */ const login = async (credentials)=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const response = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.login)(credentials);\n            setUser(response.user);\n        } catch (error) {\n            setError(error.message || \"Login failed\");\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    /**\r\n   * Register new user\r\n   * @param userData - Registration data\r\n   */ const register = async (userData)=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const response = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.register)(userData);\n            setUser(response.user);\n        } catch (error) {\n            setError(error.message || \"Registration failed\");\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    /**\r\n   * Logout current user\r\n   */ const logout = async ()=>{\n        try {\n            setLoading(true);\n            await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.logout)();\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n        } finally{\n            setUser(null);\n            setLoading(false);\n        }\n    };\n    /**\r\n   * Refresh current user data\r\n   */ const refreshUser = async ()=>{\n        try {\n            const userData = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.getCurrentUser)();\n            setUser(userData);\n        } catch (error) {\n            console.error(\"Failed to refresh user data:\", error);\n        }\n    };\n    const value = {\n        user,\n        loading,\n        error,\n        isAuthenticated: !!user,\n        login,\n        register,\n        logout,\n        clearError,\n        refreshUser\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 186,\n        columnNumber: 5\n    }, this);\n}\n/**\r\n * Hook to use authentication context\r\n * @returns Authentication context\r\n * @throws Error if used outside AuthProvider\r\n */ function useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiRequest: () => (/* binding */ apiRequest),\n/* harmony export */   clearTokens: () => (/* binding */ clearTokens),\n/* harmony export */   decodeToken: () => (/* binding */ decodeToken),\n/* harmony export */   getAccessToken: () => (/* binding */ getAccessToken),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   getRefreshToken: () => (/* binding */ getRefreshToken),\n/* harmony export */   isAuthenticated: () => (/* binding */ isAuthenticated),\n/* harmony export */   isTokenExpired: () => (/* binding */ isTokenExpired),\n/* harmony export */   login: () => (/* binding */ login),\n/* harmony export */   logout: () => (/* binding */ logout),\n/* harmony export */   refreshTokens: () => (/* binding */ refreshTokens),\n/* harmony export */   register: () => (/* binding */ register),\n/* harmony export */   setTokens: () => (/* binding */ setTokens),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/**\r\n * Authentication utilities and API calls\r\n * Handles login, registration, token management, and user session\r\n */ const API_BASE = \"http://localhost:5002/api\" || 0;\n/**\r\n * API response wrapper for error handling\r\n */ class ApiError extends Error {\n    constructor(message, status, code){\n        super(message);\n        this.status = status;\n        this.code = code;\n        this.name = \"ApiError\";\n    }\n}\n/**\r\n * Make authenticated API request with automatic token refresh\r\n * @param url - API endpoint URL\r\n * @param options - Fetch options\r\n * @returns Response data\r\n */ async function apiRequest(url, options = {}) {\n    const token = getAccessToken();\n    const config = {\n        headers: {\n            \"Content-Type\": \"application/json\",\n            ...token && {\n                Authorization: `Bearer ${token}`\n            },\n            ...options.headers\n        },\n        ...options\n    };\n    const response = await fetch(`${API_BASE}${url}`, config);\n    if (!response.ok) {\n        const errorData = await response.json().catch(()=>({}));\n        throw new ApiError(errorData.error?.message || \"Request failed\", response.status, errorData.error?.code);\n    }\n    return response.json();\n}\n/**\r\n * Login user with email and password\r\n * @param credentials - Login credentials\r\n * @returns Authentication response with user data and tokens\r\n */ async function login(credentials) {\n    const response = await apiRequest(\"/auth/login\", {\n        method: \"POST\",\n        body: JSON.stringify(credentials)\n    });\n    // Store tokens\n    setTokens(response.tokens);\n    return response;\n}\n/**\r\n * Register new user account\r\n * @param userData - Registration data\r\n * @returns Authentication response with user data and tokens\r\n */ async function register(userData) {\n    const response = await apiRequest(\"/auth/register\", {\n        method: \"POST\",\n        body: JSON.stringify(userData)\n    });\n    // Store tokens\n    setTokens(response.tokens);\n    return response;\n}\n/**\r\n * Logout user and clear tokens\r\n */ async function logout() {\n    const refreshToken = getRefreshToken();\n    try {\n        await apiRequest(\"/auth/logout\", {\n            method: \"POST\",\n            body: JSON.stringify({\n                refreshToken\n            })\n        });\n    } catch (error) {\n        // Continue with logout even if API call fails\n        console.warn(\"Logout API call failed:\", error);\n    }\n    clearTokens();\n}\n/**\r\n * Get current user profile\r\n * @returns Current user data\r\n */ async function getCurrentUser() {\n    const response = await apiRequest(\"/auth/profile\");\n    return response.user;\n}\n/**\r\n * Verify if current token is valid\r\n * @returns Token validity and user data\r\n */ async function verifyToken() {\n    try {\n        const response = await apiRequest(\"/auth/verify\");\n        return {\n            valid: response.valid,\n            user: response.user\n        };\n    } catch (error) {\n        return {\n            valid: false\n        };\n    }\n}\n/**\r\n * Refresh access token using refresh token\r\n * @returns New authentication tokens\r\n */ async function refreshTokens() {\n    const refreshToken = getRefreshToken();\n    if (!refreshToken) {\n        throw new Error(\"No refresh token available\");\n    }\n    const response = await fetch(`${API_BASE}/auth/refresh`, {\n        method: \"POST\",\n        headers: {\n            \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n            refreshToken\n        })\n    });\n    if (!response.ok) {\n        clearTokens();\n        throw new Error(\"Token refresh failed\");\n    }\n    const data = await response.json();\n    setTokens(data.tokens);\n    return data.tokens;\n}\n/**\r\n * Store authentication tokens in localStorage\r\n * @param tokens - Authentication tokens to store\r\n */ function setTokens(tokens) {\n    if (false) {}\n}\n/**\r\n * Get stored access token\r\n * @returns Access token or null\r\n */ function getAccessToken() {\n    if (false) {}\n    return null;\n}\n/**\r\n * Get stored refresh token\r\n * @returns Refresh token or null\r\n */ function getRefreshToken() {\n    if (false) {}\n    return null;\n}\n/**\r\n * Clear all stored authentication tokens\r\n */ function clearTokens() {\n    if (false) {}\n}\n/**\r\n * Check if user is currently authenticated\r\n * @returns True if user has valid tokens\r\n */ function isAuthenticated() {\n    return !!(getAccessToken() && getRefreshToken());\n}\n/**\r\n * Decode JWT token payload without verification\r\n * @param token - JWT token to decode\r\n * @returns Decoded payload or null\r\n */ function decodeToken(token) {\n    try {\n        const payload = token.split(\".\")[1];\n        return JSON.parse(atob(payload));\n    } catch (error) {\n        return null;\n    }\n}\n/**\r\n * Check if token is expired\r\n * @param token - JWT token to check\r\n * @returns True if token is expired\r\n */ function isTokenExpired(token) {\n    const decoded = decodeToken(token);\n    if (!decoded || !decoded.exp) return true;\n    return Date.now() >= decoded.exp * 1000;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/users.ts":
/*!**************************!*\
  !*** ./src/lib/users.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createUser: () => (/* binding */ createUser),\n/* harmony export */   deleteUser: () => (/* binding */ deleteUser),\n/* harmony export */   getAllUsers: () => (/* binding */ getAllUsers),\n/* harmony export */   getCurrentUserProfile: () => (/* binding */ getCurrentUserProfile),\n/* harmony export */   getUserById: () => (/* binding */ getUserById),\n/* harmony export */   getUserStats: () => (/* binding */ getUserStats),\n/* harmony export */   getUsersByRole: () => (/* binding */ getUsersByRole),\n/* harmony export */   searchUsers: () => (/* binding */ searchUsers),\n/* harmony export */   toggleUserStatus: () => (/* binding */ toggleUserStatus),\n/* harmony export */   updateCurrentUserProfile: () => (/* binding */ updateCurrentUserProfile),\n/* harmony export */   updateUser: () => (/* binding */ updateUser),\n/* harmony export */   updateUserPassword: () => (/* binding */ updateUserPassword)\n/* harmony export */ });\n/* harmony import */ var _auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./auth */ \"(ssr)/./src/lib/auth.ts\");\n/**\n * User management API service\n * Handles user CRUD operations, role management, and user statistics\n */ \n/**\n * Get all users with pagination and filtering\n */ async function getAllUsers(filters = {}, page = 1, limit = 20) {\n    const params = new URLSearchParams({\n        page: page.toString(),\n        limit: limit.toString(),\n        ...Object.fromEntries(Object.entries(filters).filter(([_, value])=>value !== undefined && value !== \"\"))\n    });\n    const response = await (0,_auth__WEBPACK_IMPORTED_MODULE_0__.apiRequest)(`/users?${params.toString()}`);\n    return response;\n}\n/**\n * Get user by ID\n */ async function getUserById(id) {\n    const response = await (0,_auth__WEBPACK_IMPORTED_MODULE_0__.apiRequest)(`/users/${id}`);\n    return response.user;\n}\n/**\n * Create new user\n */ async function createUser(userData) {\n    const response = await (0,_auth__WEBPACK_IMPORTED_MODULE_0__.apiRequest)(\"/users\", {\n        method: \"POST\",\n        body: JSON.stringify(userData)\n    });\n    return response.user;\n}\n/**\n * Update user\n */ async function updateUser(id, userData) {\n    const response = await (0,_auth__WEBPACK_IMPORTED_MODULE_0__.apiRequest)(`/users/${id}`, {\n        method: \"PUT\",\n        body: JSON.stringify(userData)\n    });\n    return response.user;\n}\n/**\n * Delete user (soft delete - deactivate)\n */ async function deleteUser(id) {\n    await (0,_auth__WEBPACK_IMPORTED_MODULE_0__.apiRequest)(`/users/${id}`, {\n        method: \"DELETE\"\n    });\n}\n/**\n * Update user password\n */ async function updateUserPassword(id, currentPassword, newPassword) {\n    await (0,_auth__WEBPACK_IMPORTED_MODULE_0__.apiRequest)(`/users/${id}/password`, {\n        method: \"PUT\",\n        body: JSON.stringify({\n            currentPassword,\n            newPassword\n        })\n    });\n}\n/**\n * Get user statistics (admin only)\n */ async function getUserStats() {\n    const response = await (0,_auth__WEBPACK_IMPORTED_MODULE_0__.apiRequest)(\"/users/stats\");\n    return response.stats;\n}\n/**\n * Get current user profile\n */ async function getCurrentUserProfile() {\n    const response = await (0,_auth__WEBPACK_IMPORTED_MODULE_0__.apiRequest)(\"/users/me\");\n    return response.user;\n}\n/**\n * Update current user profile\n */ async function updateCurrentUserProfile(userData) {\n    const response = await (0,_auth__WEBPACK_IMPORTED_MODULE_0__.apiRequest)(\"/users/me\", {\n        method: \"PUT\",\n        body: JSON.stringify(userData)\n    });\n    return response.user;\n}\n/**\n * Activate/Deactivate user\n */ async function toggleUserStatus(id, isActive) {\n    return updateUser(id, {\n        isActive\n    });\n}\n/**\n * Get users by role\n */ async function getUsersByRole(role) {\n    const response = await getAllUsers({\n        role\n    });\n    return response.users;\n}\n/**\n * Search users by name or email\n */ async function searchUsers(query) {\n    const response = await getAllUsers({\n        search: query\n    });\n    return response.users;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/users.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"4036a722cdb7\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZmxleGFpci1mcm9udGVuZC8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/MTc0ZCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjQwMzZhNzIyY2RiN1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/../node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(rsc)/./src/contexts/AuthContext.tsx\");\n/**\r\n * Root layout component for the Flexair Timekeeping App\r\n * Provides global styling, context providers, and common layout structure\r\n */ \n\n\n\nconst metadata = {\n    title: \"Flexair Timekeeping\",\n    description: \"Modern timekeeping application with biometric integration\",\n    keywords: [\n        \"timekeeping\",\n        \"attendance\",\n        \"biometric\",\n        \"HR\",\n        \"workforce management\"\n    ],\n    authors: [\n        {\n            name: \"Flexair Team\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\",\n    robots: \"noindex, nofollow\"\n};\n/**\r\n * Root layout component with authentication context\r\n * @param children - Child components to render\r\n */ function RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className)} h-full bg-gray-50 antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-full\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 34,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 33,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/users/page.tsx":
/*!********************************!*\
  !*** ./src/app/users/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Workspace\flexair_timekeeping_app\frontend\src\app\users\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ e0),\n/* harmony export */   useAuth: () => (/* binding */ e1)\n/* harmony export */ });\n/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ \"(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js\");\n\n\nconst e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\\Users\\<USER>\\Workspace\\flexair_timekeeping_app\\frontend\\src\\contexts\\AuthContext.tsx#AuthProvider`);\n\nconst e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\\Users\\<USER>\\Workspace\\flexair_timekeeping_app\\frontend\\src\\contexts\\AuthContext.tsx#useAuth`);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/contexts/AuthContext.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fusers%2Fpage&page=%2Fusers%2Fpage&appPaths=%2Fusers%2Fpage&pagePath=private-next-app-dir%2Fusers%2Fpage.tsx&appDir=C%3A%5CUsers%5Cijureidini%5CWorkspace%5Cflexair_timekeeping_app%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cijureidini%5CWorkspace%5Cflexair_timekeeping_app%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();