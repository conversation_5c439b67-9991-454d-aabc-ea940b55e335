require('dotenv').config({ path: './backend/.env' });
const mysql = require('mysql2/promise');

(async () => {
  const conn = await mysql.createConnection({
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME
  });

  const [users] = await conn.execute('SELECT id, email, first_name, last_name, is_active FROM users ORDER BY id LIMIT 10');
  
  console.log('Current user status in database:');
  users.forEach(u => {
    console.log(`${u.id}: ${u.first_name} ${u.last_name} (${u.email}) - ${u.is_active ? 'ACTIVE' : 'INACTIVE'}`);
  });
  
  await conn.end();
})();
