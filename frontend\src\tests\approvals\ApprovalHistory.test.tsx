/**
 * Tests for ApprovalHistory component
 */

import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import ApprovalHistory from '@/components/approvals/ApprovalHistory';
import { useAuth } from '@/contexts/AuthContext';
import * as approvalsLib from '@/lib/approvals';

// Mock the auth context
jest.mock('@/contexts/AuthContext', () => ({
  useAuth: jest.fn(),
}));

// Mock the approvals library
jest.mock('@/lib/approvals', () => ({
  getApprovals: jest.fn(),
  getMyApprovalRequests: jest.fn(),
  getStatusBadgeColor: jest.fn(() => 'bg-green-100 text-green-800'),
  getRequestTypeDisplayName: jest.fn((type) => type),
  formatRequestData: jest.fn(() => 'Mock request data'),
}));

const mockUseAuth = useAuth as jest.MockedFunction<typeof useAuth>;
const mockGetApprovals = approvalsLib.getApprovals as jest.MockedFunction<typeof approvalsLib.getApprovals>;
const mockGetMyApprovalRequests = approvalsLib.getMyApprovalRequests as jest.MockedFunction<typeof approvalsLib.getMyApprovalRequests>;

describe('ApprovalHistory', () => {
  const mockApprovals = [
    {
      id: 1,
      timeLogId: 1,
      requestedBy: 1,
      assignedTo: 2,
      requestType: 'correction' as const,
      requestData: { clockIn: '09:00', clockOut: '17:00' },
      reason: 'Need to correct time',
      status: 'approved' as const,
      createdAt: '2024-01-15T10:00:00Z',
      approvedAt: '2024-01-15T12:00:00Z',
      requesterName: 'John Doe',
      requesterEmail: '<EMAIL>',
      approverName: 'Manager Smith',
      comments: 'Approved - valid correction',
    },
    {
      id: 2,
      timeLogId: 2,
      requestedBy: 1,
      assignedTo: 2,
      requestType: 'overtime' as const,
      requestData: { hours: 2, date: '2024-01-15' },
      reason: 'Project deadline',
      status: 'rejected' as const,
      createdAt: '2024-01-14T10:00:00Z',
      approvedAt: '2024-01-14T14:00:00Z',
      requesterName: 'John Doe',
      requesterEmail: '<EMAIL>',
      approverName: 'Manager Smith',
      comments: 'Not pre-approved',
    },
  ];

  const mockUser = {
    id: 1,
    email: '<EMAIL>',
    firstName: 'John',
    lastName: 'Doe',
    role: 'employee' as const,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseAuth.mockReturnValue({ user: mockUser } as any);
    mockGetMyApprovalRequests.mockResolvedValue({
      requests: mockApprovals,
      pagination: { page: 1, limit: 20, total: 2, totalPages: 1 },
    });
    mockGetApprovals.mockResolvedValue({
      requests: mockApprovals,
      pagination: { page: 1, limit: 20, total: 2, totalPages: 1 },
    });
  });

  describe('Rendering', () => {
    it('renders my requests view by default', async () => {
      render(<ApprovalHistory />);

      await waitFor(() => {
        expect(screen.getByText('My Approval Requests')).toBeInTheDocument();
      });

      expect(mockGetMyApprovalRequests).toHaveBeenCalled();
      expect(mockGetApprovals).not.toHaveBeenCalled();
    });

    it('renders all approvals view when specified', async () => {
      render(<ApprovalHistory viewMode="all-approvals" />);

      await waitFor(() => {
        expect(screen.getByText('All Approval Requests')).toBeInTheDocument();
      });

      expect(mockGetApprovals).toHaveBeenCalled();
      expect(mockGetMyApprovalRequests).not.toHaveBeenCalled();
    });

    it('shows loading state initially', () => {
      render(<ApprovalHistory />);
      expect(screen.getByRole('status')).toBeInTheDocument(); // Loading spinner
    });

    it('shows empty state when no approvals', async () => {
      mockGetMyApprovalRequests.mockResolvedValue({
        requests: [],
        pagination: { page: 1, limit: 20, total: 0, totalPages: 0 },
      });

      render(<ApprovalHistory />);

      await waitFor(() => {
        expect(screen.getByText('No approval requests found.')).toBeInTheDocument();
      });
    });

    it('shows error message when loading fails', async () => {
      mockGetMyApprovalRequests.mockRejectedValue(new Error('Network error'));

      render(<ApprovalHistory />);

      await waitFor(() => {
        expect(screen.getByText('Network error')).toBeInTheDocument();
      });
    });
  });

  describe('Filters', () => {
    it('renders filters when showFilters is true', () => {
      render(<ApprovalHistory showFilters={true} />);

      expect(screen.getByDisplayValue('All Status')).toBeInTheDocument();
      expect(screen.getByDisplayValue('All Types')).toBeInTheDocument();
    });

    it('does not render filters when showFilters is false', () => {
      render(<ApprovalHistory showFilters={false} />);

      expect(screen.queryByDisplayValue('All Status')).not.toBeInTheDocument();
      expect(screen.queryByDisplayValue('All Types')).not.toBeInTheDocument();
    });

    it('applies status filter', async () => {
      const user = userEvent.setup();
      render(<ApprovalHistory showFilters={true} />);

      await waitFor(() => {
        expect(screen.getByText('My Approval Requests')).toBeInTheDocument();
      });

      const statusFilter = screen.getByDisplayValue('All Status');
      await user.selectOptions(statusFilter, 'approved');

      await waitFor(() => {
        expect(mockGetMyApprovalRequests).toHaveBeenCalledWith(
          expect.objectContaining({ status: 'approved' })
        );
      });
    });

    it('applies request type filter', async () => {
      const user = userEvent.setup();
      render(<ApprovalHistory showFilters={true} />);

      await waitFor(() => {
        expect(screen.getByText('My Approval Requests')).toBeInTheDocument();
      });

      const typeFilter = screen.getByDisplayValue('All Types');
      await user.selectOptions(typeFilter, 'correction');

      await waitFor(() => {
        expect(mockGetMyApprovalRequests).toHaveBeenCalledWith(
          expect.objectContaining({ requestType: 'correction' })
        );
      });
    });

    it('applies date range filters', async () => {
      const user = userEvent.setup();
      render(<ApprovalHistory showFilters={true} />);

      await waitFor(() => {
        expect(screen.getByText('My Approval Requests')).toBeInTheDocument();
      });

      const startDateInputs = screen.getAllByPlaceholderText('Start Date');
      const startDateInput = startDateInputs[0];
      await user.type(startDateInput, '2024-01-01');

      await waitFor(() => {
        expect(mockGetMyApprovalRequests).toHaveBeenCalledWith(
          expect.objectContaining({ startDate: '2024-01-01' })
        );
      });
    });
  });

  describe('Data Display', () => {
    it('displays approval data correctly', async () => {
      render(<ApprovalHistory />);

      await waitFor(() => {
        expect(screen.getByText('My Approval Requests')).toBeInTheDocument();
      });

      // Check status badges
      expect(screen.getByText('Approved')).toBeInTheDocument();
      expect(screen.getByText('Rejected')).toBeInTheDocument();

      // Check request details
      expect(screen.getAllByText('Mock request data')).toHaveLength(2);

      // Check reasons
      expect(screen.getByText('Need to correct time')).toBeInTheDocument();
      expect(screen.getByText('Project deadline')).toBeInTheDocument();

      // Check comments
      expect(screen.getByText('Approved - valid correction')).toBeInTheDocument();
      expect(screen.getByText('Not pre-approved')).toBeInTheDocument();
    });

    it('shows requester column in all-approvals view', async () => {
      render(<ApprovalHistory viewMode="all-approvals" />);

      await waitFor(() => {
        expect(screen.getByText('All Approval Requests')).toBeInTheDocument();
      });

      expect(screen.getByText('Requester')).toBeInTheDocument();
      expect(screen.getAllByText('John Doe')).toHaveLength(2);
      expect(screen.getAllByText('<EMAIL>')).toHaveLength(2);
    });

    it('does not show requester column in my-requests view', async () => {
      render(<ApprovalHistory viewMode="my-requests" />);

      await waitFor(() => {
        expect(screen.getByText('My Approval Requests')).toBeInTheDocument();
      });

      expect(screen.queryByText('Requester')).not.toBeInTheDocument();
    });

    it('displays status icons correctly', async () => {
      render(<ApprovalHistory />);

      await waitFor(() => {
        expect(screen.getByText('My Approval Requests')).toBeInTheDocument();
      });

      // Check for SVG icons (approved and rejected)
      const svgElements = screen.getAllByRole('img', { hidden: true });
      expect(svgElements.length).toBeGreaterThan(0);
    });

    it('shows processed date and approver info', async () => {
      render(<ApprovalHistory />);

      await waitFor(() => {
        expect(screen.getByText('My Approval Requests')).toBeInTheDocument();
      });

      expect(screen.getAllByText('by Manager Smith')).toHaveLength(2);
    });

    it('shows dash for unprocessed approvals', async () => {
      const pendingApproval = {
        ...mockApprovals[0],
        status: 'pending' as const,
        approvedAt: undefined,
        approverName: undefined,
      };

      mockGetMyApprovalRequests.mockResolvedValue({
        requests: [pendingApproval],
        pagination: { page: 1, limit: 20, total: 1, totalPages: 1 },
      });

      render(<ApprovalHistory />);

      await waitFor(() => {
        expect(screen.getByText('My Approval Requests')).toBeInTheDocument();
      });

      // Check for dash in processed column
      const dashElements = screen.getAllByText('-');
      expect(dashElements.length).toBeGreaterThan(0);
    });
  });

  describe('Pagination', () => {
    it('shows pagination when multiple pages exist', async () => {
      mockGetMyApprovalRequests.mockResolvedValue({
        requests: mockApprovals,
        pagination: { page: 1, limit: 20, total: 50, totalPages: 3 },
      });

      render(<ApprovalHistory />);

      await waitFor(() => {
        expect(screen.getByText('My Approval Requests')).toBeInTheDocument();
      });

      expect(screen.getByText('Showing 1 to 20 of 50 results')).toBeInTheDocument();
      expect(screen.getByText('Page 1 of 3')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'Previous' })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'Next' })).toBeInTheDocument();
    });

    it('does not show pagination for single page', async () => {
      render(<ApprovalHistory />);

      await waitFor(() => {
        expect(screen.getByText('My Approval Requests')).toBeInTheDocument();
      });

      expect(screen.queryByText('Previous')).not.toBeInTheDocument();
      expect(screen.queryByText('Next')).not.toBeInTheDocument();
    });

    it('handles page navigation', async () => {
      const user = userEvent.setup();
      mockGetMyApprovalRequests.mockResolvedValue({
        requests: mockApprovals,
        pagination: { page: 1, limit: 20, total: 50, totalPages: 3 },
      });

      render(<ApprovalHistory />);

      await waitFor(() => {
        expect(screen.getByText('My Approval Requests')).toBeInTheDocument();
      });

      const nextButton = screen.getByRole('button', { name: 'Next' });
      await user.click(nextButton);

      await waitFor(() => {
        expect(mockGetMyApprovalRequests).toHaveBeenCalledWith(
          expect.objectContaining({ page: 2 })
        );
      });
    });
  });

  describe('Responsive Design', () => {
    it('truncates long text with title attribute', async () => {
      const longReasonApproval = {
        ...mockApprovals[0],
        reason: 'This is a very long reason that should be truncated in the display but still accessible via tooltip',
      };

      mockGetMyApprovalRequests.mockResolvedValue({
        requests: [longReasonApproval],
        pagination: { page: 1, limit: 20, total: 1, totalPages: 1 },
      });

      render(<ApprovalHistory />);

      await waitFor(() => {
        expect(screen.getByText('My Approval Requests')).toBeInTheDocument();
      });

      const reasonElement = screen.getByTitle(longReasonApproval.reason);
      expect(reasonElement).toBeInTheDocument();
    });
  });
});
