/**
 * Rate limiting middleware using rate-limiter-flexible
 * Provides protection against brute force and DoS attacks
 */

const { RateLimiterMemory } = require('rate-limiter-flexible');
const { logger, logSecurityEvent } = require('../logger');

/**
 * General API rate limiter
 * Limits requests per IP address
 */
const rateLimiter = new RateLimiterMemory({
  keyPrefix: 'middleware',
  points: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100, // Number of requests
  duration: parseInt(process.env.RATE_LIMIT_WINDOW) * 60 || 900, // Per 15 minutes (900 seconds)
  blockDuration: 60 * 15 // Block for 15 minutes
});

/**
 * Strict rate limiter for authentication endpoints
 * More restrictive limits for login/registration
 */
const authRateLimiter = new RateLimiterMemory({
  keyPrefix: 'auth',
  points: 50, // Number of attempts (increased for testing)
  duration: 60 * 15, // Per 15 minutes
  blockDuration: 60 * 15 // Block for 15 minutes
});

/**
 * Password reset rate limiter
 * Prevents abuse of password reset functionality
 */
const passwordResetLimiter = new RateLimiterMemory({
  keyPrefix: 'password_reset',
  points: 3, // Number of attempts
  duration: 60 * 60, // Per hour
  blockDuration: 60 * 60 // Block for 1 hour
});

/**
 * General rate limiting middleware
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Next middleware function
 */
async function rateLimiterMiddleware(req, res, next) {
  try {
    const key = req.ip;
    await rateLimiter.consume(key);
    next();
  } catch (rejRes) {
    // Log rate limit exceeded
    logSecurityEvent('RATE_LIMIT_EXCEEDED', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      path: req.path,
      message: 'General rate limit exceeded'
    });

    const secs = Math.round(rejRes.msBeforeNext / 1000) || 1;
    res.set('Retry-After', String(secs));
    res.status(429).json({
      error: 'Too many requests',
      message: 'Rate limit exceeded. Please try again later.',
      retryAfter: secs
    });
  }
}

/**
 * Authentication rate limiting middleware
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Next middleware function
 */
async function authRateLimiterMiddleware(req, res, next) {
  try {
    const key = req.ip;
    await authRateLimiter.consume(key);
    next();
  } catch (rejRes) {
    // Log authentication rate limit exceeded
    logSecurityEvent('AUTH_RATE_LIMIT_EXCEEDED', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      path: req.path,
      username: req.body.username || req.body.email,
      message: 'Authentication rate limit exceeded'
    });

    const secs = Math.round(rejRes.msBeforeNext / 1000) || 1;
    res.set('Retry-After', String(secs));
    res.status(429).json({
      error: 'Too many authentication attempts',
      message: 'Please wait before attempting to login again.',
      retryAfter: secs
    });
  }
}

/**
 * Password reset rate limiting middleware
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Next middleware function
 */
async function passwordResetLimiterMiddleware(req, res, next) {
  try {
    const key = req.ip;
    await passwordResetLimiter.consume(key);
    next();
  } catch (rejRes) {
    // Log password reset rate limit exceeded
    logSecurityEvent('PASSWORD_RESET_RATE_LIMIT_EXCEEDED', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      email: req.body.email,
      message: 'Password reset rate limit exceeded'
    });

    const secs = Math.round(rejRes.msBeforeNext / 1000) || 1;
    res.set('Retry-After', String(secs));
    res.status(429).json({
      error: 'Too many password reset attempts',
      message: 'Please wait before requesting another password reset.',
      retryAfter: secs
    });
  }
}

/**
 * Reset rate limits for a specific key (for testing or admin purposes)
 * @param {string} key - The key to reset
 * @param {string} limiterType - Type of limiter ('general', 'auth', 'password_reset')
 */
async function resetRateLimit(key, limiterType = 'general') {
  try {
    switch (limiterType) {
      case 'auth':
        await authRateLimiter.delete(key);
        break;
      case 'password_reset':
        await passwordResetLimiter.delete(key);
        break;
      default:
        await rateLimiter.delete(key);
    }
    logger.info(`Rate limit reset for key: ${key}, type: ${limiterType}`);
  } catch (error) {
    logger.error('Failed to reset rate limit:', error);
  }
}

module.exports = {
  rateLimiter: rateLimiterMiddleware,
  authRateLimiter: authRateLimiterMiddleware,
  passwordResetLimiter: passwordResetLimiterMiddleware,
  resetRateLimit
};