/**
 * Winston logger configuration
 * Provides structured logging with different levels and formats
 */

const winston = require('winston');
const path = require('path');

/**
 * Custom log format that redacts sensitive information
 * @param {Object} info - Log information object
 * @returns {string} Formatted log string
 */
const customFormat = winston.format.printf(({ level, message, timestamp, ...meta }) => {
  // Redact sensitive fields
  const redactedMeta = JSON.stringify(meta, (key, value) => {
    const sensitiveFields = ['password', 'token', 'secret', 'authorization', 'cookie'];
    if (sensitiveFields.some(field => key.toLowerCase().includes(field))) {
      return '[REDACTED]';
    }
    return value;
  });

  return `${timestamp} [${level.toUpperCase()}]: ${message} ${redactedMeta !== '{}' ? redactedMeta : ''}`;
});

/**
 * Create logger instance with appropriate transports
 */
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    customFormat
  ),
  defaultMeta: { service: 'flexair-api' },
  transports: [
    // Console transport for development
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.timestamp(),
        customFormat
      )
    })
  ]
});

// Add file transport for production
if (process.env.NODE_ENV === 'production') {
  // Ensure logs directory exists
  const logDir = path.dirname(process.env.LOG_FILE || 'logs/app.log');
  require('fs').mkdirSync(logDir, { recursive: true });

  logger.add(new winston.transports.File({
    filename: process.env.LOG_FILE || 'logs/app.log',
    maxsize: 5242880, // 5MB
    maxFiles: 5
  }));

  // Separate error log file
  logger.add(new winston.transports.File({
    filename: 'logs/error.log',
    level: 'error',
    maxsize: 5242880, // 5MB
    maxFiles: 5
  }));
}

/**
 * Log security events with specific format
 * @param {string} event - Security event type
 * @param {Object} details - Event details
 * @param {string} userId - User ID if applicable
 */
function logSecurityEvent(event, details, userId = null) {
  logger.warn('SECURITY_EVENT', {
    event,
    userId,
    timestamp: new Date().toISOString(),
    ip: details.ip,
    userAgent: details.userAgent,
    details: details.message || details
  });
}

/**
 * Log API request with timing
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {number} duration - Request duration in ms
 */
function logApiRequest(req, res, duration) {
  const logData = {
    method: req.method,
    url: req.originalUrl,
    statusCode: res.statusCode,
    duration: `${duration}ms`,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.user?.id || null
  };

  if (res.statusCode >= 400) {
    logger.error('API_REQUEST_ERROR', logData);
  } else if (res.statusCode >= 300) {
    logger.warn('API_REQUEST_REDIRECT', logData);
  } else {
    logger.info('API_REQUEST', logData);
  }
}

/**
 * Log database operations
 * @param {string} operation - Database operation type
 * @param {string} table - Table name
 * @param {Object} details - Operation details
 */
function logDatabaseOperation(operation, table, details = {}) {
  logger.info('DATABASE_OPERATION', {
    operation,
    table,
    duration: details.duration,
    rowsAffected: details.rowsAffected,
    userId: details.userId
  });
}

module.exports = {
  logger,
  logSecurityEvent,
  logApiRequest,
  logDatabaseOperation
};