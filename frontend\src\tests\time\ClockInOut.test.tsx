/**
 * Unit tests for ClockInOut component
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import ClockInOut from '@/components/time/ClockInOut';
import { AuthProvider } from '@/contexts/AuthContext';

// Mock the auth API
jest.mock('@/lib/auth', () => ({
  apiRequest: jest.fn(),
}));

// Mock the auth context
const mockUser = {
  id: 1,
  firstName: 'John',
  lastName: 'Doe',
  email: '<EMAIL>',
  role: 'employee',
  isActive: true,
};

const MockAuthProvider = ({ children }: { children: React.ReactNode }) => {
  return (
    <AuthProvider>
      {children}
    </AuthProvider>
  );
};

// Mock useAuth hook
jest.mock('@/contexts/AuthContext', () => ({
  ...jest.requireActual('@/contexts/AuthContext'),
  useAuth: () => ({
    user: mockUser,
    isAuthenticated: true,
    login: jest.fn(),
    logout: jest.fn(),
    refreshToken: jest.fn(),
  }),
}));

const { apiRequest } = require('@/lib/auth');

describe('ClockInOut Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Mock successful API responses by default
    apiRequest.mockResolvedValue({
      success: true,
      data: null,
    });
  });

  /**
   * Test: Component renders without crashing
   */
  it('renders without crashing', async () => {
    render(
      <MockAuthProvider>
        <ClockInOut />
      </MockAuthProvider>
    );

    expect(screen.getByText('Time Clock')).toBeInTheDocument();
    expect(screen.getByText(/Welcome back, John!/)).toBeInTheDocument();
  });

  /**
   * Test: Clock In functionality - successful case
   */
  it('handles successful clock in', async () => {
    render(
      <MockAuthProvider>
        <ClockInOut />
      </MockAuthProvider>
    );

    // Wait for component to load
    await waitFor(() => {
      expect(screen.getByLabelText('Location')).toBeInTheDocument();
    });

    // Fill in location
    const locationInput = screen.getByLabelText('Location');
    fireEvent.change(locationInput, { target: { value: 'Office' } });

    // Click clock in button
    const clockInButton = screen.getByText('Clock In');
    fireEvent.click(clockInButton);

    // Verify API was called with correct data
    await waitFor(() => {
      expect(apiRequest).toHaveBeenCalledWith('/api/time/clock-in', {
        method: 'POST',
        body: JSON.stringify({ location: 'Office' })
      });
    });
  });

  /**
   * Test: Clock In validation - missing location
   */
  it('validates location input before clock in', async () => {
    render(
      <MockAuthProvider>
        <ClockInOut />
      </MockAuthProvider>
    );

    // Wait for component to load
    await waitFor(() => {
      expect(screen.getByText('Clock In')).toBeInTheDocument();
    });

    // Try to clock in without location
    const clockInButton = screen.getByText('Clock In');
    fireEvent.click(clockInButton);

    // Should show validation message
    await waitFor(() => {
      expect(screen.getByText('Please enter your location')).toBeInTheDocument();
    });

    // API should not be called
    expect(apiRequest).not.toHaveBeenCalled();
  });

  /**
   * Test: Active session display
   */
  it('displays active session when user is clocked in', async () => {
    // Mock active session response
    const mockActiveSession = {
      id: 1,
      clock_in_time: new Date().toISOString(),
      clock_in_location: 'Office',
      break_minutes: 0,
      overtime_minutes: 0,
    };

    apiRequest.mockImplementation((url) => {
      if (url === '/api/time/active-session') {
        return Promise.resolve({
          success: true,
          data: mockActiveSession,
        });
      }
      return Promise.resolve({ success: true, data: null });
    });

    render(
      <MockAuthProvider>
        <ClockInOut />
      </MockAuthProvider>
    );

    // Wait for active session to load
    await waitFor(() => {
      expect(screen.getByText('Currently Clocked In')).toBeInTheDocument();
      expect(screen.getByText('Active')).toBeInTheDocument();
      expect(screen.getByText('Office')).toBeInTheDocument();
    });

    // Should show clock out button instead of clock in
    expect(screen.getByText('Clock Out')).toBeInTheDocument();
    expect(screen.queryByText('Clock In')).not.toBeInTheDocument();
  });

  /**
   * Test: Clock Out functionality
   */
  it('handles successful clock out', async () => {
    // Mock active session
    const mockActiveSession = {
      id: 1,
      clock_in_time: new Date().toISOString(),
      clock_in_location: 'Office',
    };

    apiRequest.mockImplementation((url, options) => {
      if (url === '/api/time/active-session') {
        return Promise.resolve({
          success: true,
          data: mockActiveSession,
        });
      }
      if (url === '/api/time/clock-out' && options?.method === 'POST') {
        return Promise.resolve({
          success: true,
          message: 'Successfully clocked out!',
        });
      }
      return Promise.resolve({ success: true, data: null });
    });

    render(
      <MockAuthProvider>
        <ClockInOut />
      </MockAuthProvider>
    );

    // Wait for active session to load
    await waitFor(() => {
      expect(screen.getByText('Clock Out')).toBeInTheDocument();
    });

    // Click clock out button
    const clockOutButton = screen.getByText('Clock Out');
    fireEvent.click(clockOutButton);

    // Verify API was called
    await waitFor(() => {
      expect(apiRequest).toHaveBeenCalledWith('/api/time/clock-out', {
        method: 'POST',
        body: JSON.stringify({})
      });
    });
  });

  /**
   * Test: Time statistics display
   */
  it('displays time statistics', async () => {
    const mockTimeStats = {
      today_hours: '8.5',
      week_hours: '40.0',
      month_hours: '160.0',
      overtime_week: '5.0',
    };

    apiRequest.mockImplementation((url) => {
      if (url === '/api/time/stats') {
        return Promise.resolve({
          success: true,
          data: mockTimeStats,
        });
      }
      return Promise.resolve({ success: true, data: null });
    });

    render(
      <MockAuthProvider>
        <ClockInOut />
      </MockAuthProvider>
    );

    // Wait for stats to load
    await waitFor(() => {
      expect(screen.getByText('Time Summary')).toBeInTheDocument();
      expect(screen.getByText('8.5')).toBeInTheDocument();
      expect(screen.getByText('40.0')).toBeInTheDocument();
      expect(screen.getByText('160.0')).toBeInTheDocument();
      expect(screen.getByText('5.0')).toBeInTheDocument();
    });
  });

  /**
   * Test: Error handling for API failures
   */
  it('handles API errors gracefully', async () => {
    // Mock API failure
    apiRequest.mockRejectedValue(new Error('Network error'));

    render(
      <MockAuthProvider>
        <ClockInOut />
      </MockAuthProvider>
    );

    // Wait for component to load
    await waitFor(() => {
      expect(screen.getByLabelText('Location')).toBeInTheDocument();
    });

    // Fill in location and try to clock in
    const locationInput = screen.getByLabelText('Location');
    fireEvent.change(locationInput, { target: { value: 'Office' } });

    const clockInButton = screen.getByText('Clock In');
    fireEvent.click(clockInButton);

    // Should show error message
    await waitFor(() => {
      expect(screen.getByText('Error clocking in. Please try again.')).toBeInTheDocument();
    });
  });

  /**
   * Test: Loading states
   */
  it('shows loading states during API calls', async () => {
    // Mock delayed API response
    apiRequest.mockImplementation(() => 
      new Promise(resolve => 
        setTimeout(() => resolve({ success: true, data: null }), 100)
      )
    );

    render(
      <MockAuthProvider>
        <ClockInOut />
      </MockAuthProvider>
    );

    // Wait for component to load
    await waitFor(() => {
      expect(screen.getByLabelText('Location')).toBeInTheDocument();
    });

    // Fill in location and click clock in
    const locationInput = screen.getByLabelText('Location');
    fireEvent.change(locationInput, { target: { value: 'Office' } });

    const clockInButton = screen.getByText('Clock In');
    fireEvent.click(clockInButton);

    // Should show loading state
    expect(screen.getByText('Clocking In...')).toBeInTheDocument();

    // Wait for loading to complete
    await waitFor(() => {
      expect(screen.queryByText('Clocking In...')).not.toBeInTheDocument();
    });
  });
});