'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import UserList from '@/components/users/UserList';
import UserProfile from '@/components/users/UserProfile';
import UserForm from '@/components/users/UserForm';
import { User } from '@/lib/users';

/**
 * Users management page
 * Displays user list with options to view, edit, and create users
 */
function UsersPage() {
  const router = useRouter();
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [view, setView] = useState<'list' | 'profile' | 'edit' | 'create'>('list');

  /**
   * Handle user selection for viewing profile
   */
  const handleUserSelect = (user: User) => {
    setSelectedUser(user);
    setView('profile');
  };

  /**
   * Handle user edit
   */
  const handleUserEdit = (user: User) => {
    setEditingUser(user);
    setView('edit');
  };

  /**
   * Handle create new user
   */
  const handleUserCreate = () => {
    setShowCreateForm(true);
    setView('create');
  };

  /**
   * Handle successful user creation/update
   */
  const handleUserSuccess = (user: User) => {
    setView('list');
    setSelectedUser(null);
    setEditingUser(null);
    setShowCreateForm(false);
    // The UserList component will automatically refresh
  };

  /**
   * Handle cancel/close actions
   */
  const handleCancel = () => {
    setView('list');
    setSelectedUser(null);
    setEditingUser(null);
    setShowCreateForm(false);
  };

  /**
   * Handle edit from profile view
   */
  const handleEditFromProfile = () => {
    if (selectedUser) {
      setEditingUser(selectedUser);
      setView('edit');
    }
  };

  return (
    <ProtectedRoute requiredRoles={['admin', 'manager']}>
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <header className="bg-white shadow">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-6">
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => router.push('/dashboard')}
                  className="text-gray-500 hover:text-gray-700"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7"></path>
                  </svg>
                </button>
                <div>
                  <h1 className="text-3xl font-bold text-gray-900">
                    {view === 'list' && 'User Management'}
                    {view === 'profile' && 'User Profile'}
                    {view === 'edit' && 'Edit User'}
                    {view === 'create' && 'Create User'}
                  </h1>
                  <p className="text-sm text-gray-600">
                    {view === 'list' && 'Manage user accounts and permissions'}
                    {view === 'profile' && 'View user details and information'}
                    {view === 'edit' && 'Update user information and settings'}
                    {view === 'create' && 'Add a new user to the system'}
                  </p>
                </div>
              </div>

              {/* Navigation breadcrumbs */}
              <nav className="flex items-center space-x-2 text-sm text-gray-500">
                <button
                  onClick={() => router.push('/dashboard')}
                  className="hover:text-gray-700"
                >
                  Dashboard
                </button>
                <span>/</span>
                <button
                  onClick={handleCancel}
                  className={`hover:text-gray-700 ${view === 'list' ? 'text-gray-900 font-medium' : ''}`}
                >
                  Users
                </button>
                {view !== 'list' && (
                  <>
                    <span>/</span>
                    <span className="text-gray-900 font-medium">
                      {view === 'profile' && selectedUser && `${selectedUser.firstName} ${selectedUser.lastName}`}
                      {view === 'edit' && 'Edit'}
                      {view === 'create' && 'New User'}
                    </span>
                  </>
                )}
              </nav>
            </div>
          </div>
        </header>

        {/* Main Content */}
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {view === 'list' && (
            <UserList
              onUserSelect={handleUserSelect}
              onUserEdit={handleUserEdit}
              onUserCreate={handleUserCreate}
            />
          )}

          {view === 'profile' && selectedUser && (
            <UserProfile
              user={selectedUser}
              onEdit={handleEditFromProfile}
              onClose={handleCancel}
            />
          )}

          {view === 'edit' && editingUser && (
            <UserForm
              user={editingUser}
              mode="edit"
              onSuccess={handleUserSuccess}
              onCancel={handleCancel}
            />
          )}

          {view === 'create' && (
            <UserForm
              mode="create"
              onSuccess={handleUserSuccess}
              onCancel={handleCancel}
            />
          )}
        </main>
      </div>
    </ProtectedRoute>
  );
}

export default UsersPage;
