/**
 * User management API service
 * Handles user CRUD operations, role management, and user statistics
 */

import { apiRequest } from './auth';

// User interfaces
export interface User {
  id: number;
  email: string;
  firstName: string;
  lastName: string;
  department?: string;
  role: 'admin' | 'manager' | 'employee';
  employeeId?: string;
  phone?: string;
  isActive: boolean;
  emailVerified: boolean;
  biometricEnabled: boolean;
  createdAt: string;
  updatedAt?: string;
  lastLogin?: string;
}

export interface CreateUserData {
  email: string;
  password?: string;
  firstName: string;
  lastName: string;
  department?: string;
  role?: 'admin' | 'manager' | 'employee';
  employeeId?: string;
  phone?: string;
}

export interface UpdateUserData {
  email?: string;
  firstName?: string;
  lastName?: string;
  department?: string;
  role?: 'admin' | 'manager' | 'employee';
  employeeId?: string;
  phone?: string;
  isActive?: boolean;
}

export interface UserFilters {
  role?: string;
  isActive?: boolean;
  search?: string;
  department?: string;
}

export interface UserListResponse {
  users: User[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface UserStats {
  total: number;
  active: number;
  inactive: number;
  byRole: {
    admin: number;
    manager: number;
    employee: number;
  };
  recentRegistrations: number;
}

/**
 * Get all users with pagination and filtering
 */
export async function getAllUsers(
  filters: UserFilters = {},
  page: number = 1,
  limit: number = 20
): Promise<UserListResponse> {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString(),
    ...Object.fromEntries(
      Object.entries(filters).filter(([_, value]) => value !== undefined && value !== '')
    )
  });

  const response = await apiRequest(`/users?${params.toString()}`);
  return response;
}

/**
 * Get user by ID
 */
export async function getUserById(id: number): Promise<User> {
  const response = await apiRequest(`/users/${id}`);
  return response.user;
}

/**
 * Create new user
 */
export async function createUser(userData: CreateUserData): Promise<User> {
  const response = await apiRequest('/users', {
    method: 'POST',
    body: JSON.stringify(userData),
  });
  return response.user;
}

/**
 * Update user
 */
export async function updateUser(id: number, userData: UpdateUserData): Promise<User> {
  const response = await apiRequest(`/users/${id}`, {
    method: 'PUT',
    body: JSON.stringify(userData),
  });
  return response.user;
}

/**
 * Delete user (soft delete - deactivate)
 */
export async function deleteUser(id: number): Promise<void> {
  await apiRequest(`/users/${id}`, {
    method: 'DELETE',
  });
}

/**
 * Update user password
 */
export async function updateUserPassword(
  id: number,
  currentPassword: string,
  newPassword: string
): Promise<void> {
  await apiRequest(`/users/${id}/password`, {
    method: 'PUT',
    body: JSON.stringify({
      currentPassword,
      newPassword,
    }),
  });
}

/**
 * Get user statistics (admin only)
 */
export async function getUserStats(): Promise<UserStats> {
  const response = await apiRequest('/users/stats');
  return response.stats;
}

/**
 * Get current user profile
 */
export async function getCurrentUserProfile(): Promise<User> {
  const response = await apiRequest('/users/me');
  return response.user;
}

/**
 * Update current user profile
 */
export async function updateCurrentUserProfile(userData: UpdateUserData): Promise<User> {
  const response = await apiRequest('/users/me', {
    method: 'PUT',
    body: JSON.stringify(userData),
  });
  return response.user;
}

/**
 * Activate/Deactivate user
 */
export async function toggleUserStatus(id: number, isActive: boolean): Promise<User> {
  return updateUser(id, { isActive });
}

/**
 * Get users by role
 */
export async function getUsersByRole(role: string): Promise<User[]> {
  const response = await getAllUsers({ role });
  return response.users;
}

/**
 * Search users by name or email
 */
export async function searchUsers(query: string): Promise<User[]> {
  const response = await getAllUsers({ search: query });
  return response.users;
}
