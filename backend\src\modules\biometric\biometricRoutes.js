/**
 * Biometric integration routes
 */

const express = require('express');
const router = express.Router();
const biometricController = require('./biometricController');
const { authenticateToken, requireRole } = require('../../shared/middleware/authMiddleware');
const { biometricValidation } = require('../../shared/middleware/validation');

// All routes require authentication
router.use(authenticateToken);

// Enroll biometric template
router.post('/enroll', 
  biometricValidation.enroll,
  biometricController.enrollBiometricTemplate
);

// Verify biometric template
router.post('/verify', 
  biometricValidation.verify,
  biometricController.verifyBiometricTemplate
);

// Get current user's biometric templates
router.get('/my-templates', 
  biometricController.getUserBiometricTemplates
);

// Get current user's biometric verification history
router.get('/my-history', 
  biometricController.getBiometricHistory
);

// Delete current user's biometric template
router.delete('/my-templates/:templateType', 
  biometricController.deleteBiometricTemplate
);

// Get biometric statistics (admin/manager only)
router.get('/stats', 
  requireRole(['admin', 'manager']),
  biometricController.getBiometricStats
);

// Admin/Manager routes for managing other users' biometrics

// Get biometric templates for specific user (admin/manager only)
router.get('/users/:userId/templates', 
  requireRole(['admin', 'manager']),
  biometricController.getBiometricTemplatesForUser
);

// Get biometric history for specific user (admin/manager only)
router.get('/users/:userId/history', 
  requireRole(['admin', 'manager']),
  biometricController.getBiometricHistoryForUser
);

// Verify biometric for specific user (admin/manager only)
router.post('/users/:userId/verify', 
  requireRole(['admin', 'manager']),
  biometricValidation.verify,
  biometricController.verifyBiometricForUser
);

// Delete biometric template for specific user (admin only)
router.delete('/users/:userId/templates/:templateType', 
  requireRole('admin'),
  biometricController.deleteBiometricTemplateForUser
);

module.exports = router;