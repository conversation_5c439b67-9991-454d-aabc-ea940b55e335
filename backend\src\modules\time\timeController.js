/**
 * Time tracking controller
 * Handles HTTP requests for time tracking operations
 */

const timeService = require('./timeService');
const { logger } = require('../../shared/logger');

/**
 * Clock in user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const clockIn = async (req, res) => {
  try {
    const userId = req.user.id;
    const clockData = req.body;

    const timeLog = await timeService.clockIn(userId, clockData);

    res.status(201).json({
      success: true,
      message: 'Clocked in successfully',
      timeLog
    });
  } catch (error) {
    logger.error('Clock in controller error:', error);
    
    if (error.message === 'User already has an active session. Please clock out first.') {
      return res.status(400).json({
        error: error.message,
        code: 'ACTIVE_SESSION_EXISTS'
      });
    }

    res.status(500).json({
      error: 'Failed to clock in',
      code: 'CLOCK_IN_ERROR'
    });
  }
};

/**
 * Clock out user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const clockOut = async (req, res) => {
  try {
    const userId = req.user.id;
    const clockData = req.body;

    const timeLog = await timeService.clockOut(userId, clockData);

    res.json({
      success: true,
      message: 'Clocked out successfully',
      timeLog
    });
  } catch (error) {
    logger.error('Clock out controller error:', error);
    
    if (error.message === 'No active session found. Please clock in first.') {
      return res.status(400).json({
        error: error.message,
        code: 'NO_ACTIVE_SESSION'
      });
    }

    res.status(500).json({
      error: 'Failed to clock out',
      code: 'CLOCK_OUT_ERROR'
    });
  }
};

/**
 * Get user's active session
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getActiveSession = async (req, res) => {
  try {
    const userId = req.user.id;
    const activeSession = await timeService.getActiveSession(userId);

    res.json({
      success: true,
      data: activeSession
    });
  } catch (error) {
    logger.error('Get active session error:', error);
    res.status(500).json({
      error: 'Failed to retrieve active session',
      code: 'ACTIVE_SESSION_ERROR'
    });
  }
};

/**
 * Get time logs with filtering and pagination
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getTimeLogs = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      startDate,
      endDate,
      userId,
      status,
      approvalStatus
    } = req.query;

    const filters = {};
    
    // Non-admin users can only see their own logs unless they're managers
    if (req.user.role === 'employee') {
      filters.userId = req.user.id;
    } else if (userId) {
      filters.userId = parseInt(userId);
    }

    if (startDate) filters.startDate = startDate;
    if (endDate) filters.endDate = endDate;
    if (status) filters.status = status;
    if (approvalStatus) filters.approvalStatus = approvalStatus;

    const result = await timeService.getTimeLogs(
      filters,
      parseInt(page),
      parseInt(limit)
    );

    res.json({
      success: true,
      data: {
        logs: result.timeLogs,
        totalCount: result.pagination.total,
        totalPages: result.pagination.pages,
        currentPage: result.pagination.page
      }
    });
  } catch (error) {
    logger.error('Get time logs error:', error);
    res.status(500).json({
      error: 'Failed to retrieve time logs',
      code: 'TIME_LOGS_ERROR'
    });
  }
};

/**
 * Get time log by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getTimeLogById = async (req, res) => {
  try {
    const { id } = req.params;
    const timeLog = await timeService.getTimeLogById(parseInt(id));

    if (!timeLog) {
      return res.status(404).json({
        error: 'Time log not found',
        code: 'TIME_LOG_NOT_FOUND'
      });
    }

    // Check if user can access this time log
    if (req.user.role === 'employee' && timeLog.user_id !== req.user.id) {
      return res.status(403).json({
        error: 'Access denied',
        code: 'ACCESS_DENIED'
      });
    }

    res.json({
      success: true,
      timeLog
    });
  } catch (error) {
    logger.error('Get time log by ID error:', error);
    res.status(500).json({
      error: 'Failed to retrieve time log',
      code: 'TIME_LOG_ERROR'
    });
  }
};

/**
 * Update time log (admin/manager only)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const updateTimeLog = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;
    const updatedBy = req.user.id;

    const timeLog = await timeService.updateTimeLog(
      parseInt(id),
      updateData,
      updatedBy
    );

    logger.info(`Time log ${id} updated by ${req.user.email}`);

    res.json({
      success: true,
      message: 'Time log updated successfully',
      timeLog
    });
  } catch (error) {
    logger.error('Update time log error:', error);
    
    if (error.message === 'Time log not found') {
      return res.status(404).json({
        error: error.message,
        code: 'TIME_LOG_NOT_FOUND'
      });
    }

    res.status(500).json({
      error: 'Failed to update time log',
      code: 'TIME_LOG_UPDATE_ERROR'
    });
  }
};

/**
 * Get time tracking statistics
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getTimeStats = async (req, res) => {
  try {
    const { startDate, endDate, userId } = req.query;
    const filters = {};

    // Non-admin users can only see their own stats unless they're managers
    if (req.user.role === 'employee') {
      filters.userId = req.user.id;
    } else if (userId) {
      filters.userId = parseInt(userId);
    }

    if (startDate) filters.startDate = startDate;
    if (endDate) filters.endDate = endDate;

    const stats = await timeService.getTimeStats(filters);

    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    logger.error('Get time stats error:', error);
    res.status(500).json({
      error: 'Failed to retrieve time statistics',
      code: 'TIME_STATS_ERROR'
    });
  }
};

/**
 * Get current user's time summary
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getMyTimeSummary = async (req, res) => {
  try {
    const userId = req.user.id;
    
    // Get active session
    const activeSession = await timeService.getActiveSession(userId);
    
    // Get today's logs
    const today = new Date().toISOString().split('T')[0];
    const todayLogs = await timeService.getTimeLogs({
      userId,
      startDate: today,
      endDate: today
    }, 1, 10);

    // Get this week's stats
    const weekStart = new Date();
    weekStart.setDate(weekStart.getDate() - weekStart.getDay());
    const weekStats = await timeService.getTimeStats({
      userId,
      startDate: weekStart.toISOString().split('T')[0]
    });

    res.json({
      success: true,
      data: {
        activeSession,
        todayLogs: todayLogs.timeLogs,
        weekStats
      }
    });
  } catch (error) {
    logger.error('Get my time summary error:', error);
    res.status(500).json({
      error: 'Failed to retrieve time summary',
      code: 'TIME_SUMMARY_ERROR'
    });
  }
};

module.exports = {
  clockIn,
  clockOut,
  getActiveSession,
  getTimeLogs,
  getTimeLogById,
  updateTimeLog,
  getTimeStats,
  getMyTimeSummary
};