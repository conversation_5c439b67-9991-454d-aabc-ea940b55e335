/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/page"],{

/***/ "(app-pages-browser)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(app-pages-browser)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2lqdXJlaWRpbmklNUMlNUNXb3Jrc3BhY2UlNUMlNUNmbGV4YWlyX3RpbWVrZWVwaW5nX2FwcCU1QyU1Q2Zyb250ZW5kJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPWZhbHNlISIsIm1hcHBpbmdzIjoiQUFBQSw4SkFBNEgiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLz8zY2FiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcaWp1cmVpZGluaVxcXFxXb3Jrc3BhY2VcXFxcZmxleGFpcl90aW1la2VlcGluZ19hcHBcXFxcZnJvbnRlbmRcXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/next/dist/api/link.js":
/*!*********************************************!*\
  !*** ../node_modules/next/dist/api/link.js ***!
  \*********************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* reexport default from dynamic */ _client_link__WEBPACK_IMPORTED_MODULE_0___default.a; }\n/* harmony export */ });\n/* harmony import */ var _client_link__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../client/link */ \"(app-pages-browser)/../node_modules/next/dist/client/link.js\");\n/* harmony import */ var _client_link__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_client_link__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _client_link__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = function(key) { return _client_link__WEBPACK_IMPORTED_MODULE_0__[key]; }.bind(0, __WEBPACK_IMPORT_KEY__)\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n\n//# sourceMappingURL=link.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2FwaS9saW5rLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUF5QztBQUNWOztBQUUvQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9hcGkvbGluay5qcz80ZWJiIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB7IGRlZmF1bHQgfSBmcm9tIFwiLi4vY2xpZW50L2xpbmtcIjtcbmV4cG9ydCAqIGZyb20gXCIuLi9jbGllbnQvbGlua1wiO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1saW5rLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/next/dist/api/link.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/next/dist/api/navigation.js":
/*!***************************************************!*\
  !*** ../node_modules/next/dist/api/navigation.js ***!
  \***************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../client/components/navigation */ \"(app-pages-browser)/../node_modules/next/dist/client/components/navigation.js\");\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_client_components_navigation__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = function(key) { return _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__[key]; }.bind(0, __WEBPACK_IMPORT_KEY__)\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n//# sourceMappingURL=navigation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2FwaS9uYXZpZ2F0aW9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFnRDs7QUFFaEQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYXBpL25hdmlnYXRpb24uanM/Y2QzMCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tIFwiLi4vY2xpZW50L2NvbXBvbmVudHMvbmF2aWdhdGlvblwiO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1uYXZpZ2F0aW9uLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/next/dist/api/navigation.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/next/dist/lib/constants.js":
/*!**************************************************!*\
  !*** ../node_modules/next/dist/lib/constants.js ***!
  \**************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    ACTION_SUFFIX: function() {\n        return ACTION_SUFFIX;\n    },\n    APP_DIR_ALIAS: function() {\n        return APP_DIR_ALIAS;\n    },\n    CACHE_ONE_YEAR: function() {\n        return CACHE_ONE_YEAR;\n    },\n    DOT_NEXT_ALIAS: function() {\n        return DOT_NEXT_ALIAS;\n    },\n    ESLINT_DEFAULT_DIRS: function() {\n        return ESLINT_DEFAULT_DIRS;\n    },\n    GSP_NO_RETURNED_VALUE: function() {\n        return GSP_NO_RETURNED_VALUE;\n    },\n    GSSP_COMPONENT_MEMBER_ERROR: function() {\n        return GSSP_COMPONENT_MEMBER_ERROR;\n    },\n    GSSP_NO_RETURNED_VALUE: function() {\n        return GSSP_NO_RETURNED_VALUE;\n    },\n    INSTRUMENTATION_HOOK_FILENAME: function() {\n        return INSTRUMENTATION_HOOK_FILENAME;\n    },\n    MIDDLEWARE_FILENAME: function() {\n        return MIDDLEWARE_FILENAME;\n    },\n    MIDDLEWARE_LOCATION_REGEXP: function() {\n        return MIDDLEWARE_LOCATION_REGEXP;\n    },\n    NEXT_BODY_SUFFIX: function() {\n        return NEXT_BODY_SUFFIX;\n    },\n    NEXT_CACHE_IMPLICIT_TAG_ID: function() {\n        return NEXT_CACHE_IMPLICIT_TAG_ID;\n    },\n    NEXT_CACHE_REVALIDATED_TAGS_HEADER: function() {\n        return NEXT_CACHE_REVALIDATED_TAGS_HEADER;\n    },\n    NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER: function() {\n        return NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER;\n    },\n    NEXT_CACHE_SOFT_TAGS_HEADER: function() {\n        return NEXT_CACHE_SOFT_TAGS_HEADER;\n    },\n    NEXT_CACHE_SOFT_TAG_MAX_LENGTH: function() {\n        return NEXT_CACHE_SOFT_TAG_MAX_LENGTH;\n    },\n    NEXT_CACHE_TAGS_HEADER: function() {\n        return NEXT_CACHE_TAGS_HEADER;\n    },\n    NEXT_CACHE_TAG_MAX_ITEMS: function() {\n        return NEXT_CACHE_TAG_MAX_ITEMS;\n    },\n    NEXT_CACHE_TAG_MAX_LENGTH: function() {\n        return NEXT_CACHE_TAG_MAX_LENGTH;\n    },\n    NEXT_DATA_SUFFIX: function() {\n        return NEXT_DATA_SUFFIX;\n    },\n    NEXT_INTERCEPTION_MARKER_PREFIX: function() {\n        return NEXT_INTERCEPTION_MARKER_PREFIX;\n    },\n    NEXT_META_SUFFIX: function() {\n        return NEXT_META_SUFFIX;\n    },\n    NEXT_QUERY_PARAM_PREFIX: function() {\n        return NEXT_QUERY_PARAM_PREFIX;\n    },\n    NON_STANDARD_NODE_ENV: function() {\n        return NON_STANDARD_NODE_ENV;\n    },\n    PAGES_DIR_ALIAS: function() {\n        return PAGES_DIR_ALIAS;\n    },\n    PRERENDER_REVALIDATE_HEADER: function() {\n        return PRERENDER_REVALIDATE_HEADER;\n    },\n    PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER: function() {\n        return PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER;\n    },\n    PUBLIC_DIR_MIDDLEWARE_CONFLICT: function() {\n        return PUBLIC_DIR_MIDDLEWARE_CONFLICT;\n    },\n    ROOT_DIR_ALIAS: function() {\n        return ROOT_DIR_ALIAS;\n    },\n    RSC_ACTION_CLIENT_WRAPPER_ALIAS: function() {\n        return RSC_ACTION_CLIENT_WRAPPER_ALIAS;\n    },\n    RSC_ACTION_ENCRYPTION_ALIAS: function() {\n        return RSC_ACTION_ENCRYPTION_ALIAS;\n    },\n    RSC_ACTION_PROXY_ALIAS: function() {\n        return RSC_ACTION_PROXY_ALIAS;\n    },\n    RSC_ACTION_VALIDATE_ALIAS: function() {\n        return RSC_ACTION_VALIDATE_ALIAS;\n    },\n    RSC_MOD_REF_PROXY_ALIAS: function() {\n        return RSC_MOD_REF_PROXY_ALIAS;\n    },\n    RSC_PREFETCH_SUFFIX: function() {\n        return RSC_PREFETCH_SUFFIX;\n    },\n    RSC_SUFFIX: function() {\n        return RSC_SUFFIX;\n    },\n    SERVER_PROPS_EXPORT_ERROR: function() {\n        return SERVER_PROPS_EXPORT_ERROR;\n    },\n    SERVER_PROPS_GET_INIT_PROPS_CONFLICT: function() {\n        return SERVER_PROPS_GET_INIT_PROPS_CONFLICT;\n    },\n    SERVER_PROPS_SSG_CONFLICT: function() {\n        return SERVER_PROPS_SSG_CONFLICT;\n    },\n    SERVER_RUNTIME: function() {\n        return SERVER_RUNTIME;\n    },\n    SSG_FALLBACK_EXPORT_ERROR: function() {\n        return SSG_FALLBACK_EXPORT_ERROR;\n    },\n    SSG_GET_INITIAL_PROPS_CONFLICT: function() {\n        return SSG_GET_INITIAL_PROPS_CONFLICT;\n    },\n    STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR: function() {\n        return STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR;\n    },\n    UNSTABLE_REVALIDATE_RENAME_ERROR: function() {\n        return UNSTABLE_REVALIDATE_RENAME_ERROR;\n    },\n    WEBPACK_LAYERS: function() {\n        return WEBPACK_LAYERS;\n    },\n    WEBPACK_RESOURCE_QUERIES: function() {\n        return WEBPACK_RESOURCE_QUERIES;\n    }\n});\nconst NEXT_QUERY_PARAM_PREFIX = \"nxtP\";\nconst NEXT_INTERCEPTION_MARKER_PREFIX = \"nxtI\";\nconst PRERENDER_REVALIDATE_HEADER = \"x-prerender-revalidate\";\nconst PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER = \"x-prerender-revalidate-if-generated\";\nconst RSC_PREFETCH_SUFFIX = \".prefetch.rsc\";\nconst RSC_SUFFIX = \".rsc\";\nconst ACTION_SUFFIX = \".action\";\nconst NEXT_DATA_SUFFIX = \".json\";\nconst NEXT_META_SUFFIX = \".meta\";\nconst NEXT_BODY_SUFFIX = \".body\";\nconst NEXT_CACHE_TAGS_HEADER = \"x-next-cache-tags\";\nconst NEXT_CACHE_SOFT_TAGS_HEADER = \"x-next-cache-soft-tags\";\nconst NEXT_CACHE_REVALIDATED_TAGS_HEADER = \"x-next-revalidated-tags\";\nconst NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER = \"x-next-revalidate-tag-token\";\nconst NEXT_CACHE_TAG_MAX_ITEMS = 128;\nconst NEXT_CACHE_TAG_MAX_LENGTH = 256;\nconst NEXT_CACHE_SOFT_TAG_MAX_LENGTH = 1024;\nconst NEXT_CACHE_IMPLICIT_TAG_ID = \"_N_T_\";\nconst CACHE_ONE_YEAR = 31536000;\nconst MIDDLEWARE_FILENAME = \"middleware\";\nconst MIDDLEWARE_LOCATION_REGEXP = `(?:src/)?${MIDDLEWARE_FILENAME}`;\nconst INSTRUMENTATION_HOOK_FILENAME = \"instrumentation\";\nconst PAGES_DIR_ALIAS = \"private-next-pages\";\nconst DOT_NEXT_ALIAS = \"private-dot-next\";\nconst ROOT_DIR_ALIAS = \"private-next-root-dir\";\nconst APP_DIR_ALIAS = \"private-next-app-dir\";\nconst RSC_MOD_REF_PROXY_ALIAS = \"private-next-rsc-mod-ref-proxy\";\nconst RSC_ACTION_VALIDATE_ALIAS = \"private-next-rsc-action-validate\";\nconst RSC_ACTION_PROXY_ALIAS = \"private-next-rsc-server-reference\";\nconst RSC_ACTION_ENCRYPTION_ALIAS = \"private-next-rsc-action-encryption\";\nconst RSC_ACTION_CLIENT_WRAPPER_ALIAS = \"private-next-rsc-action-client-wrapper\";\nconst PUBLIC_DIR_MIDDLEWARE_CONFLICT = `You can not have a '_next' folder inside of your public folder. This conflicts with the internal '/_next' route. https://nextjs.org/docs/messages/public-next-folder-conflict`;\nconst SSG_GET_INITIAL_PROPS_CONFLICT = `You can not use getInitialProps with getStaticProps. To use SSG, please remove your getInitialProps`;\nconst SERVER_PROPS_GET_INIT_PROPS_CONFLICT = `You can not use getInitialProps with getServerSideProps. Please remove getInitialProps.`;\nconst SERVER_PROPS_SSG_CONFLICT = `You can not use getStaticProps or getStaticPaths with getServerSideProps. To use SSG, please remove getServerSideProps`;\nconst STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR = `can not have getInitialProps/getServerSideProps, https://nextjs.org/docs/messages/404-get-initial-props`;\nconst SERVER_PROPS_EXPORT_ERROR = `pages with \\`getServerSideProps\\` can not be exported. See more info here: https://nextjs.org/docs/messages/gssp-export`;\nconst GSP_NO_RETURNED_VALUE = \"Your `getStaticProps` function did not return an object. Did you forget to add a `return`?\";\nconst GSSP_NO_RETURNED_VALUE = \"Your `getServerSideProps` function did not return an object. Did you forget to add a `return`?\";\nconst UNSTABLE_REVALIDATE_RENAME_ERROR = \"The `unstable_revalidate` property is available for general use.\\n\" + \"Please use `revalidate` instead.\";\nconst GSSP_COMPONENT_MEMBER_ERROR = `can not be attached to a page's component and must be exported from the page. See more info here: https://nextjs.org/docs/messages/gssp-component-member`;\nconst NON_STANDARD_NODE_ENV = `You are using a non-standard \"NODE_ENV\" value in your environment. This creates inconsistencies in the project and is strongly advised against. Read more: https://nextjs.org/docs/messages/non-standard-node-env`;\nconst SSG_FALLBACK_EXPORT_ERROR = `Pages with \\`fallback\\` enabled in \\`getStaticPaths\\` can not be exported. See more info here: https://nextjs.org/docs/messages/ssg-fallback-true-export`;\nconst ESLINT_DEFAULT_DIRS = [\n    \"app\",\n    \"pages\",\n    \"components\",\n    \"lib\",\n    \"src\"\n];\nconst SERVER_RUNTIME = {\n    edge: \"edge\",\n    experimentalEdge: \"experimental-edge\",\n    nodejs: \"nodejs\"\n};\n/**\n * The names of the webpack layers. These layers are the primitives for the\n * webpack chunks.\n */ const WEBPACK_LAYERS_NAMES = {\n    /**\n   * The layer for the shared code between the client and server bundles.\n   */ shared: \"shared\",\n    /**\n   * React Server Components layer (rsc).\n   */ reactServerComponents: \"rsc\",\n    /**\n   * Server Side Rendering layer for app (ssr).\n   */ serverSideRendering: \"ssr\",\n    /**\n   * The browser client bundle layer for actions.\n   */ actionBrowser: \"action-browser\",\n    /**\n   * The layer for the API routes.\n   */ api: \"api\",\n    /**\n   * The layer for the middleware code.\n   */ middleware: \"middleware\",\n    /**\n   * The layer for the instrumentation hooks.\n   */ instrument: \"instrument\",\n    /**\n   * The layer for assets on the edge.\n   */ edgeAsset: \"edge-asset\",\n    /**\n   * The browser client bundle layer for App directory.\n   */ appPagesBrowser: \"app-pages-browser\",\n    /**\n   * The server bundle layer for metadata routes.\n   */ appMetadataRoute: \"app-metadata-route\",\n    /**\n   * The layer for the server bundle for App Route handlers.\n   */ appRouteHandler: \"app-route-handler\"\n};\nconst WEBPACK_LAYERS = {\n    ...WEBPACK_LAYERS_NAMES,\n    GROUP: {\n        serverOnly: [\n            WEBPACK_LAYERS_NAMES.reactServerComponents,\n            WEBPACK_LAYERS_NAMES.actionBrowser,\n            WEBPACK_LAYERS_NAMES.appMetadataRoute,\n            WEBPACK_LAYERS_NAMES.appRouteHandler,\n            WEBPACK_LAYERS_NAMES.instrument\n        ],\n        clientOnly: [\n            WEBPACK_LAYERS_NAMES.serverSideRendering,\n            WEBPACK_LAYERS_NAMES.appPagesBrowser\n        ],\n        nonClientServerTarget: [\n            // middleware and pages api\n            WEBPACK_LAYERS_NAMES.middleware,\n            WEBPACK_LAYERS_NAMES.api\n        ],\n        app: [\n            WEBPACK_LAYERS_NAMES.reactServerComponents,\n            WEBPACK_LAYERS_NAMES.actionBrowser,\n            WEBPACK_LAYERS_NAMES.appMetadataRoute,\n            WEBPACK_LAYERS_NAMES.appRouteHandler,\n            WEBPACK_LAYERS_NAMES.serverSideRendering,\n            WEBPACK_LAYERS_NAMES.appPagesBrowser,\n            WEBPACK_LAYERS_NAMES.shared,\n            WEBPACK_LAYERS_NAMES.instrument\n        ]\n    }\n};\nconst WEBPACK_RESOURCE_QUERIES = {\n    edgeSSREntry: \"__next_edge_ssr_entry__\",\n    metadata: \"__next_metadata__\",\n    metadataRoute: \"__next_metadata_route__\",\n    metadataImageMeta: \"__next_metadata_image_meta__\"\n};\n\n//# sourceMappingURL=constants.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/next/dist/lib/constants.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ HomePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/../node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/../node_modules/next/dist/api/link.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/**\r\n * Home page component - Landing page for Flexair Timekeeping\r\n */ /* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction HomePage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { isAuthenticated, loading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Redirect authenticated users to dashboard\n        if (!loading && isAuthenticated) {\n            router.push(\"/dashboard\");\n        }\n    }, [\n        isAuthenticated,\n        loading,\n        router\n    ]);\n    // Show loading while checking auth state\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-2 text-gray-600\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 27,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 26,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-16\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-5xl font-bold text-gray-900 mb-4\",\n                            children: \"Flexair Timekeeping\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n                            children: \"Modern timekeeping application with biometric integration, comprehensive analytics, and seamless workforce management.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureCard, {\n                            title: \"Biometric Integration\",\n                            description: \"Secure fingerprint and facial recognition for accurate time tracking\",\n                            icon: \"\\uD83D\\uDD10\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureCard, {\n                            title: \"Real-time Analytics\",\n                            description: \"Comprehensive dashboard with insights and reporting capabilities\",\n                            icon: \"\\uD83D\\uDCCA\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureCard, {\n                            title: \"Approval Workflow\",\n                            description: \"Streamlined approval process for time corrections and requests\",\n                            icon: \"✅\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureCard, {\n                            title: \"Role-based Access\",\n                            description: \"Admin, manager, and employee roles with appropriate permissions\",\n                            icon: \"\\uD83D\\uDC65\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureCard, {\n                            title: \"Audit Logging\",\n                            description: \"Complete audit trail for all system activities and changes\",\n                            icon: \"\\uD83D\\uDCDD\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureCard, {\n                            title: \"REST API\",\n                            description: \"Modern Express.js backend with comprehensive API endpoints\",\n                            icon: \"\\uD83D\\uDE80\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-12\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                href: \"/auth/login\",\n                                className: \"inline-block bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-3 px-8 rounded-lg transition-colors\",\n                                children: \"Sign In\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                href: \"/auth/register\",\n                                className: \"inline-block bg-white hover:bg-gray-50 text-indigo-600 font-semibold py-3 px-8 rounded-lg border-2 border-indigo-600 transition-colors\",\n                                children: \"Create Account\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-lg p-8 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl font-bold text-gray-900 mb-4\",\n                            children: \"Development Status\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-2 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-green-600 mb-2\",\n                                            children: \"✅ Backend Complete\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"text-left text-gray-600 space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Authentication with JWT\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 111,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• User Management\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 112,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Time Tracking\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Approval Workflow\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 114,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Biometric Integration\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 115,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Dashboard Analytics\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 116,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-green-600 mb-2\",\n                                            children: \"✅ Frontend Authentication\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"text-left text-gray-600 space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Login & Registration Forms\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 124,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Protected Routes\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 125,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• JWT Token Management\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 126,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Authentication Context\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 127,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Auto Token Refresh\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 128,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Role-based Access Control\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 129,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-8 p-4 bg-gray-50 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-500\",\n                                    children: [\n                                        \"API Status: \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-green-600 font-semibold\",\n                                            children: \"Running on http://localhost:5002\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 27\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-500 mt-1\",\n                                    children: [\n                                        \"Frontend: \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-green-600 font-semibold\",\n                                            children: \"Authentication System Ready\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 25\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 36,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePage, \"gWJ89pc545Cl+2Ljpu5b9kbtsR0=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth\n    ];\n});\n_c = HomePage;\n/**\r\n * Feature card component\r\n */ function FeatureCard(param) {\n    let { title, description, icon } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-md p-6 text-center hover:shadow-lg transition-shadow\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-4xl mb-4\",\n                children: icon\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 158,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-xl font-semibold text-gray-900 mb-2\",\n                children: title\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 159,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-gray-600\",\n                children: description\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 160,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 157,\n        columnNumber: 5\n    }, this);\n}\n_c1 = FeatureCard;\nvar _c, _c1;\n$RefreshReg$(_c, \"HomePage\");\n$RefreshReg$(_c1, \"FeatureCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUFBOztDQUVDOztBQUlpQztBQUNVO0FBQ2Y7QUFDb0I7QUFFbEMsU0FBU0k7O0lBQ3RCLE1BQU1DLFNBQVNKLDBEQUFTQTtJQUN4QixNQUFNLEVBQUVLLGVBQWUsRUFBRUMsT0FBTyxFQUFFLEdBQUdKLDhEQUFPQTtJQUU1Q0gsZ0RBQVNBLENBQUM7UUFDUiw0Q0FBNEM7UUFDNUMsSUFBSSxDQUFDTyxXQUFXRCxpQkFBaUI7WUFDL0JELE9BQU9HLElBQUksQ0FBQztRQUNkO0lBQ0YsR0FBRztRQUFDRjtRQUFpQkM7UUFBU0Y7S0FBTztJQUVyQyx5Q0FBeUM7SUFDekMsSUFBSUUsU0FBUztRQUNYLHFCQUNFLDhEQUFDRTtZQUFJQyxXQUFVO3NCQUNiLDRFQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNEO3dCQUFJQyxXQUFVOzs7Ozs7a0NBQ2YsOERBQUNDO3dCQUFFRCxXQUFVO2tDQUFxQjs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFJMUM7SUFDQSxxQkFDRSw4REFBQ0Q7UUFBSUMsV0FBVTtrQkFDYiw0RUFBQ0Q7WUFBSUMsV0FBVTs7OEJBRWIsOERBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0U7NEJBQUdGLFdBQVU7c0NBQXdDOzs7Ozs7c0NBR3RELDhEQUFDQzs0QkFBRUQsV0FBVTtzQ0FBMEM7Ozs7Ozs7Ozs7Ozs4QkFPekQsOERBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0c7NEJBQ0NDLE9BQU07NEJBQ05DLGFBQVk7NEJBQ1pDLE1BQUs7Ozs7OztzQ0FFUCw4REFBQ0g7NEJBQ0NDLE9BQU07NEJBQ05DLGFBQVk7NEJBQ1pDLE1BQUs7Ozs7OztzQ0FFUCw4REFBQ0g7NEJBQ0NDLE9BQU07NEJBQ05DLGFBQVk7NEJBQ1pDLE1BQUs7Ozs7OztzQ0FFUCw4REFBQ0g7NEJBQ0NDLE9BQU07NEJBQ05DLGFBQVk7NEJBQ1pDLE1BQUs7Ozs7OztzQ0FFUCw4REFBQ0g7NEJBQ0NDLE9BQU07NEJBQ05DLGFBQVk7NEJBQ1pDLE1BQUs7Ozs7OztzQ0FFUCw4REFBQ0g7NEJBQ0NDLE9BQU07NEJBQ05DLGFBQVk7NEJBQ1pDLE1BQUs7Ozs7Ozs7Ozs7Ozs4QkFLVCw4REFBQ1A7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ1IsaURBQUlBO2dDQUNIZSxNQUFLO2dDQUNMUCxXQUFVOzBDQUNYOzs7Ozs7MENBR0QsOERBQUNSLGlEQUFJQTtnQ0FDSGUsTUFBSztnQ0FDTFAsV0FBVTswQ0FDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBT0wsOERBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ1E7NEJBQUdSLFdBQVU7c0NBQXdDOzs7Ozs7c0NBR3RELDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNEOztzREFDQyw4REFBQ1U7NENBQUdULFdBQVU7c0RBQTRDOzs7Ozs7c0RBRzFELDhEQUFDVTs0Q0FBR1YsV0FBVTs7OERBQ1osOERBQUNXOzhEQUFHOzs7Ozs7OERBQ0osOERBQUNBOzhEQUFHOzs7Ozs7OERBQ0osOERBQUNBOzhEQUFHOzs7Ozs7OERBQ0osOERBQUNBOzhEQUFHOzs7Ozs7OERBQ0osOERBQUNBOzhEQUFHOzs7Ozs7OERBQ0osOERBQUNBOzhEQUFHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBR1IsOERBQUNaOztzREFDQyw4REFBQ1U7NENBQUdULFdBQVU7c0RBQTRDOzs7Ozs7c0RBRzFELDhEQUFDVTs0Q0FBR1YsV0FBVTs7OERBQ1osOERBQUNXOzhEQUFHOzs7Ozs7OERBQ0osOERBQUNBOzhEQUFHOzs7Ozs7OERBQ0osOERBQUNBOzhEQUFHOzs7Ozs7OERBQ0osOERBQUNBOzhEQUFHOzs7Ozs7OERBQ0osOERBQUNBOzhEQUFHOzs7Ozs7OERBQ0osOERBQUNBOzhEQUFHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBS1YsOERBQUNaOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0M7b0NBQUVELFdBQVU7O3dDQUF3QjtzREFDdkIsOERBQUNZOzRDQUFLWixXQUFVO3NEQUErQjs7Ozs7Ozs7Ozs7OzhDQUU3RCw4REFBQ0M7b0NBQUVELFdBQVU7O3dDQUE2QjtzREFDOUIsOERBQUNZOzRDQUFLWixXQUFVO3NEQUErQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFPdkU7R0F0SXdCTjs7UUFDUEgsc0RBQVNBO1FBQ2FFLDBEQUFPQTs7O0tBRnRCQztBQXdJeEI7O0NBRUMsR0FDRCxTQUFTUyxZQUFZLEtBSXBCO1FBSm9CLEVBQUVDLEtBQUssRUFBRUMsV0FBVyxFQUFFQyxJQUFJLEVBSTlDLEdBSm9CO0lBS25CLHFCQUNFLDhEQUFDUDtRQUFJQyxXQUFVOzswQkFDYiw4REFBQ0Q7Z0JBQUlDLFdBQVU7MEJBQWlCTTs7Ozs7OzBCQUNoQyw4REFBQ0c7Z0JBQUdULFdBQVU7MEJBQTRDSTs7Ozs7OzBCQUMxRCw4REFBQ0g7Z0JBQUVELFdBQVU7MEJBQWlCSzs7Ozs7Ozs7Ozs7O0FBR3BDO01BWlNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvcGFnZS50c3g/ZjY4YSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcclxuICogSG9tZSBwYWdlIGNvbXBvbmVudCAtIExhbmRpbmcgcGFnZSBmb3IgRmxleGFpciBUaW1la2VlcGluZ1xyXG4gKi9cclxuXHJcbid1c2UgY2xpZW50JztcclxuXHJcbmltcG9ydCB7IHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcclxuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJztcclxuaW1wb3J0IExpbmsgZnJvbSAnbmV4dC9saW5rJztcclxuaW1wb3J0IHsgdXNlQXV0aCB9IGZyb20gJ0AvY29udGV4dHMvQXV0aENvbnRleHQnO1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gSG9tZVBhZ2UoKSB7XHJcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKCk7XHJcbiAgY29uc3QgeyBpc0F1dGhlbnRpY2F0ZWQsIGxvYWRpbmcgfSA9IHVzZUF1dGgoKTtcclxuXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIC8vIFJlZGlyZWN0IGF1dGhlbnRpY2F0ZWQgdXNlcnMgdG8gZGFzaGJvYXJkXHJcbiAgICBpZiAoIWxvYWRpbmcgJiYgaXNBdXRoZW50aWNhdGVkKSB7XHJcbiAgICAgIHJvdXRlci5wdXNoKCcvZGFzaGJvYXJkJyk7XHJcbiAgICB9XHJcbiAgfSwgW2lzQXV0aGVudGljYXRlZCwgbG9hZGluZywgcm91dGVyXSk7XHJcblxyXG4gIC8vIFNob3cgbG9hZGluZyB3aGlsZSBjaGVja2luZyBhdXRoIHN0YXRlXHJcbiAgaWYgKGxvYWRpbmcpIHtcclxuICAgIHJldHVybiAoXHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGgtOCB3LTggYm9yZGVyLWItMiBib3JkZXItaW5kaWdvLTYwMCBteC1hdXRvXCI+PC9kaXY+XHJcbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJtdC0yIHRleHQtZ3JheS02MDBcIj5Mb2FkaW5nLi4uPC9wPlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L2Rpdj5cclxuICAgICk7XHJcbiAgfVxyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1ncmFkaWVudC10by1iciBmcm9tLWJsdWUtNTAgdG8taW5kaWdvLTEwMFwiPlxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lciBteC1hdXRvIHB4LTQgcHktMTZcIj5cclxuICAgICAgICB7LyogSGVhZGVyICovfVxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgbWItMTZcIj5cclxuICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTV4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMCBtYi00XCI+XHJcbiAgICAgICAgICAgIEZsZXhhaXIgVGltZWtlZXBpbmdcclxuICAgICAgICAgIDwvaDE+XHJcbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhsIHRleHQtZ3JheS02MDAgbWF4LXctMnhsIG14LWF1dG9cIj5cclxuICAgICAgICAgICAgTW9kZXJuIHRpbWVrZWVwaW5nIGFwcGxpY2F0aW9uIHdpdGggYmlvbWV0cmljIGludGVncmF0aW9uLCBcclxuICAgICAgICAgICAgY29tcHJlaGVuc2l2ZSBhbmFseXRpY3MsIGFuZCBzZWFtbGVzcyB3b3JrZm9yY2UgbWFuYWdlbWVudC5cclxuICAgICAgICAgIDwvcD5cclxuICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgey8qIEZlYXR1cmUgR3JpZCAqL31cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgbWQ6Z3JpZC1jb2xzLTIgbGc6Z3JpZC1jb2xzLTMgZ2FwLTggbWItMTZcIj5cclxuICAgICAgICAgIDxGZWF0dXJlQ2FyZFxyXG4gICAgICAgICAgICB0aXRsZT1cIkJpb21ldHJpYyBJbnRlZ3JhdGlvblwiXHJcbiAgICAgICAgICAgIGRlc2NyaXB0aW9uPVwiU2VjdXJlIGZpbmdlcnByaW50IGFuZCBmYWNpYWwgcmVjb2duaXRpb24gZm9yIGFjY3VyYXRlIHRpbWUgdHJhY2tpbmdcIlxyXG4gICAgICAgICAgICBpY29uPVwi8J+UkFwiXHJcbiAgICAgICAgICAvPlxyXG4gICAgICAgICAgPEZlYXR1cmVDYXJkXHJcbiAgICAgICAgICAgIHRpdGxlPVwiUmVhbC10aW1lIEFuYWx5dGljc1wiXHJcbiAgICAgICAgICAgIGRlc2NyaXB0aW9uPVwiQ29tcHJlaGVuc2l2ZSBkYXNoYm9hcmQgd2l0aCBpbnNpZ2h0cyBhbmQgcmVwb3J0aW5nIGNhcGFiaWxpdGllc1wiXHJcbiAgICAgICAgICAgIGljb249XCLwn5OKXCJcclxuICAgICAgICAgIC8+XHJcbiAgICAgICAgICA8RmVhdHVyZUNhcmRcclxuICAgICAgICAgICAgdGl0bGU9XCJBcHByb3ZhbCBXb3JrZmxvd1wiXHJcbiAgICAgICAgICAgIGRlc2NyaXB0aW9uPVwiU3RyZWFtbGluZWQgYXBwcm92YWwgcHJvY2VzcyBmb3IgdGltZSBjb3JyZWN0aW9ucyBhbmQgcmVxdWVzdHNcIlxyXG4gICAgICAgICAgICBpY29uPVwi4pyFXCJcclxuICAgICAgICAgIC8+XHJcbiAgICAgICAgICA8RmVhdHVyZUNhcmRcclxuICAgICAgICAgICAgdGl0bGU9XCJSb2xlLWJhc2VkIEFjY2Vzc1wiXHJcbiAgICAgICAgICAgIGRlc2NyaXB0aW9uPVwiQWRtaW4sIG1hbmFnZXIsIGFuZCBlbXBsb3llZSByb2xlcyB3aXRoIGFwcHJvcHJpYXRlIHBlcm1pc3Npb25zXCJcclxuICAgICAgICAgICAgaWNvbj1cIvCfkaVcIlxyXG4gICAgICAgICAgLz5cclxuICAgICAgICAgIDxGZWF0dXJlQ2FyZFxyXG4gICAgICAgICAgICB0aXRsZT1cIkF1ZGl0IExvZ2dpbmdcIlxyXG4gICAgICAgICAgICBkZXNjcmlwdGlvbj1cIkNvbXBsZXRlIGF1ZGl0IHRyYWlsIGZvciBhbGwgc3lzdGVtIGFjdGl2aXRpZXMgYW5kIGNoYW5nZXNcIlxyXG4gICAgICAgICAgICBpY29uPVwi8J+TnVwiXHJcbiAgICAgICAgICAvPlxyXG4gICAgICAgICAgPEZlYXR1cmVDYXJkXHJcbiAgICAgICAgICAgIHRpdGxlPVwiUkVTVCBBUElcIlxyXG4gICAgICAgICAgICBkZXNjcmlwdGlvbj1cIk1vZGVybiBFeHByZXNzLmpzIGJhY2tlbmQgd2l0aCBjb21wcmVoZW5zaXZlIEFQSSBlbmRwb2ludHNcIlxyXG4gICAgICAgICAgICBpY29uPVwi8J+agFwiXHJcbiAgICAgICAgICAvPlxyXG4gICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICB7LyogQXV0aGVudGljYXRpb24gQWN0aW9ucyAqL31cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIG1iLTEyXCI+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNCBzbTpzcGFjZS15LTAgc206c3BhY2UteC00IHNtOmZsZXggc206anVzdGlmeS1jZW50ZXJcIj5cclxuICAgICAgICAgICAgPExpbmtcclxuICAgICAgICAgICAgICBocmVmPVwiL2F1dGgvbG9naW5cIlxyXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImlubGluZS1ibG9jayBiZy1pbmRpZ28tNjAwIGhvdmVyOmJnLWluZGlnby03MDAgdGV4dC13aGl0ZSBmb250LXNlbWlib2xkIHB5LTMgcHgtOCByb3VuZGVkLWxnIHRyYW5zaXRpb24tY29sb3JzXCJcclxuICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgIFNpZ24gSW5cclxuICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgICA8TGlua1xyXG4gICAgICAgICAgICAgIGhyZWY9XCIvYXV0aC9yZWdpc3RlclwiXHJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaW5saW5lLWJsb2NrIGJnLXdoaXRlIGhvdmVyOmJnLWdyYXktNTAgdGV4dC1pbmRpZ28tNjAwIGZvbnQtc2VtaWJvbGQgcHktMyBweC04IHJvdW5kZWQtbGcgYm9yZGVyLTIgYm9yZGVyLWluZGlnby02MDAgdHJhbnNpdGlvbi1jb2xvcnNcIlxyXG4gICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgQ3JlYXRlIEFjY291bnRcclxuICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgIHsvKiBTdGF0dXMgU2VjdGlvbiAqL31cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtbGcgc2hhZG93LWxnIHAtOCB0ZXh0LWNlbnRlclwiPlxyXG4gICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwIG1iLTRcIj5cclxuICAgICAgICAgICAgRGV2ZWxvcG1lbnQgU3RhdHVzXHJcbiAgICAgICAgICA8L2gyPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIG1kOmdyaWQtY29scy0yIGdhcC04XCI+XHJcbiAgICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZW1pYm9sZCB0ZXh0LWdyZWVuLTYwMCBtYi0yXCI+XHJcbiAgICAgICAgICAgICAgICDinIUgQmFja2VuZCBDb21wbGV0ZVxyXG4gICAgICAgICAgICAgIDwvaDM+XHJcbiAgICAgICAgICAgICAgPHVsIGNsYXNzTmFtZT1cInRleHQtbGVmdCB0ZXh0LWdyYXktNjAwIHNwYWNlLXktMVwiPlxyXG4gICAgICAgICAgICAgICAgPGxpPuKAoiBBdXRoZW50aWNhdGlvbiB3aXRoIEpXVDwvbGk+XHJcbiAgICAgICAgICAgICAgICA8bGk+4oCiIFVzZXIgTWFuYWdlbWVudDwvbGk+XHJcbiAgICAgICAgICAgICAgICA8bGk+4oCiIFRpbWUgVHJhY2tpbmc8L2xpPlxyXG4gICAgICAgICAgICAgICAgPGxpPuKAoiBBcHByb3ZhbCBXb3JrZmxvdzwvbGk+XHJcbiAgICAgICAgICAgICAgICA8bGk+4oCiIEJpb21ldHJpYyBJbnRlZ3JhdGlvbjwvbGk+XHJcbiAgICAgICAgICAgICAgICA8bGk+4oCiIERhc2hib2FyZCBBbmFseXRpY3M8L2xpPlxyXG4gICAgICAgICAgICAgIDwvdWw+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmVlbi02MDAgbWItMlwiPlxyXG4gICAgICAgICAgICAgICAg4pyFIEZyb250ZW5kIEF1dGhlbnRpY2F0aW9uXHJcbiAgICAgICAgICAgICAgPC9oMz5cclxuICAgICAgICAgICAgICA8dWwgY2xhc3NOYW1lPVwidGV4dC1sZWZ0IHRleHQtZ3JheS02MDAgc3BhY2UteS0xXCI+XHJcbiAgICAgICAgICAgICAgICA8bGk+4oCiIExvZ2luICYgUmVnaXN0cmF0aW9uIEZvcm1zPC9saT5cclxuICAgICAgICAgICAgICAgIDxsaT7igKIgUHJvdGVjdGVkIFJvdXRlczwvbGk+XHJcbiAgICAgICAgICAgICAgICA8bGk+4oCiIEpXVCBUb2tlbiBNYW5hZ2VtZW50PC9saT5cclxuICAgICAgICAgICAgICAgIDxsaT7igKIgQXV0aGVudGljYXRpb24gQ29udGV4dDwvbGk+XHJcbiAgICAgICAgICAgICAgICA8bGk+4oCiIEF1dG8gVG9rZW4gUmVmcmVzaDwvbGk+XHJcbiAgICAgICAgICAgICAgICA8bGk+4oCiIFJvbGUtYmFzZWQgQWNjZXNzIENvbnRyb2w8L2xpPlxyXG4gICAgICAgICAgICAgIDwvdWw+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICBcclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtOCBwLTQgYmctZ3JheS01MCByb3VuZGVkLWxnXCI+XHJcbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTUwMFwiPlxyXG4gICAgICAgICAgICAgIEFQSSBTdGF0dXM6IDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tNjAwIGZvbnQtc2VtaWJvbGRcIj5SdW5uaW5nIG9uIGh0dHA6Ly9sb2NhbGhvc3Q6NTAwMjwvc3Bhbj5cclxuICAgICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDAgbXQtMVwiPlxyXG4gICAgICAgICAgICAgIEZyb250ZW5kOiA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyZWVuLTYwMCBmb250LXNlbWlib2xkXCI+QXV0aGVudGljYXRpb24gU3lzdGVtIFJlYWR5PC9zcGFuPlxyXG4gICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9kaXY+XHJcbiAgICA8L2Rpdj5cclxuICApO1xyXG59XHJcblxyXG4vKipcclxuICogRmVhdHVyZSBjYXJkIGNvbXBvbmVudFxyXG4gKi9cclxuZnVuY3Rpb24gRmVhdHVyZUNhcmQoeyB0aXRsZSwgZGVzY3JpcHRpb24sIGljb24gfToge1xyXG4gIHRpdGxlOiBzdHJpbmc7XHJcbiAgZGVzY3JpcHRpb246IHN0cmluZztcclxuICBpY29uOiBzdHJpbmc7XHJcbn0pIHtcclxuICByZXR1cm4gKFxyXG4gICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLWxnIHNoYWRvdy1tZCBwLTYgdGV4dC1jZW50ZXIgaG92ZXI6c2hhZG93LWxnIHRyYW5zaXRpb24tc2hhZG93XCI+XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC00eGwgbWItNFwiPntpY29ufTwvZGl2PlxyXG4gICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDAgbWItMlwiPnt0aXRsZX08L2gzPlxyXG4gICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwXCI+e2Rlc2NyaXB0aW9ufTwvcD5cclxuICAgIDwvZGl2PlxyXG4gICk7XHJcbn0iXSwibmFtZXMiOlsidXNlRWZmZWN0IiwidXNlUm91dGVyIiwiTGluayIsInVzZUF1dGgiLCJIb21lUGFnZSIsInJvdXRlciIsImlzQXV0aGVudGljYXRlZCIsImxvYWRpbmciLCJwdXNoIiwiZGl2IiwiY2xhc3NOYW1lIiwicCIsImgxIiwiRmVhdHVyZUNhcmQiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiaWNvbiIsImhyZWYiLCJoMiIsImgzIiwidWwiLCJsaSIsInNwYW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: function() { return /* binding */ AuthProvider; },\n/* harmony export */   useAuth: function() { return /* binding */ useAuth; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(app-pages-browser)/./src/lib/auth.ts\");\n/**\r\n * Authentication Context Provider\r\n * Manages global authentication state and provides auth methods\r\n */ /* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n/**\r\n * Authentication Provider Component\r\n * Wraps the app and provides authentication state and methods\r\n * @param children - Child components to render\r\n */ function AuthProvider(param) {\n    let { children } = param;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    /**\r\n   * Clear any authentication errors\r\n   */ const clearError = ()=>setError(null);\n    /**\r\n   * Initialize authentication state on mount\r\n   */ (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        initializeAuth();\n    }, []);\n    /**\r\n   * Initialize authentication state\r\n   * Checks for existing tokens and validates them\r\n   */ const initializeAuth = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            if (!(0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.isAuthenticated)()) {\n                setLoading(false);\n                return;\n            }\n            const token = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.getAccessToken)();\n            if (!token || (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.isTokenExpired)(token)) {\n                // Try to refresh token\n                try {\n                    await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.refreshTokens)();\n                } catch (error) {\n                    (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.clearTokens)();\n                    setLoading(false);\n                    return;\n                }\n            }\n            // Verify token and get user data\n            const { valid, user: userData } = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.verifyToken)();\n            if (valid && userData) {\n                setUser(userData);\n            } else {\n                (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.clearTokens)();\n            }\n        } catch (error) {\n            console.error(\"Auth initialization error:\", error);\n            (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.clearTokens)();\n        } finally{\n            setLoading(false);\n        }\n    };\n    /**\r\n   * Login user with credentials\r\n   * @param credentials - Login credentials\r\n   */ const login = async (credentials)=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const response = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.login)(credentials);\n            setUser(response.user);\n        } catch (error) {\n            setError(error.message || \"Login failed\");\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    /**\r\n   * Register new user\r\n   * @param userData - Registration data\r\n   */ const register = async (userData)=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const response = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.register)(userData);\n            setUser(response.user);\n        } catch (error) {\n            setError(error.message || \"Registration failed\");\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    /**\r\n   * Logout current user\r\n   */ const logout = async ()=>{\n        try {\n            setLoading(true);\n            await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.logout)();\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n        } finally{\n            setUser(null);\n            setLoading(false);\n        }\n    };\n    /**\r\n   * Refresh current user data\r\n   */ const refreshUser = async ()=>{\n        try {\n            const userData = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.getCurrentUser)();\n            setUser(userData);\n        } catch (error) {\n            console.error(\"Failed to refresh user data:\", error);\n        }\n    };\n    const value = {\n        user,\n        loading,\n        error,\n        isAuthenticated: !!user,\n        login,\n        register,\n        logout,\n        clearError,\n        refreshUser\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 186,\n        columnNumber: 5\n    }, this);\n}\n_s(AuthProvider, \"PA9FxEY9xSNRrsSqaLtbYei52Hs=\");\n_c = AuthProvider;\n/**\r\n * Hook to use authentication context\r\n * @returns Authentication context\r\n * @throws Error if used outside AuthProvider\r\n */ function useAuth() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n_s1(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/AuthContext.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiRequest: function() { return /* binding */ apiRequest; },\n/* harmony export */   clearTokens: function() { return /* binding */ clearTokens; },\n/* harmony export */   decodeToken: function() { return /* binding */ decodeToken; },\n/* harmony export */   getAccessToken: function() { return /* binding */ getAccessToken; },\n/* harmony export */   getCurrentUser: function() { return /* binding */ getCurrentUser; },\n/* harmony export */   getRefreshToken: function() { return /* binding */ getRefreshToken; },\n/* harmony export */   isAuthenticated: function() { return /* binding */ isAuthenticated; },\n/* harmony export */   isTokenExpired: function() { return /* binding */ isTokenExpired; },\n/* harmony export */   login: function() { return /* binding */ login; },\n/* harmony export */   logout: function() { return /* binding */ logout; },\n/* harmony export */   refreshTokens: function() { return /* binding */ refreshTokens; },\n/* harmony export */   register: function() { return /* binding */ register; },\n/* harmony export */   setTokens: function() { return /* binding */ setTokens; },\n/* harmony export */   verifyToken: function() { return /* binding */ verifyToken; }\n/* harmony export */ });\n/**\r\n * Authentication utilities and API calls\r\n * Handles login, registration, token management, and user session\r\n */ const API_BASE = \"http://localhost:5002/api\" || 0;\n/**\r\n * API response wrapper for error handling\r\n */ class ApiError extends Error {\n    constructor(message, status, code){\n        super(message);\n        this.status = status;\n        this.code = code;\n        this.name = \"ApiError\";\n    }\n}\n/**\r\n * Make authenticated API request with automatic token refresh\r\n * @param url - API endpoint URL\r\n * @param options - Fetch options\r\n * @returns Response data\r\n */ async function apiRequest(url) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const token = getAccessToken();\n    const config = {\n        headers: {\n            \"Content-Type\": \"application/json\",\n            ...token && {\n                Authorization: \"Bearer \".concat(token)\n            },\n            ...options.headers\n        },\n        ...options\n    };\n    const response = await fetch(\"\".concat(API_BASE).concat(url), config);\n    if (!response.ok) {\n        var _errorData_error, _errorData_error1;\n        const errorData = await response.json().catch(()=>({}));\n        throw new ApiError(((_errorData_error = errorData.error) === null || _errorData_error === void 0 ? void 0 : _errorData_error.message) || \"Request failed\", response.status, (_errorData_error1 = errorData.error) === null || _errorData_error1 === void 0 ? void 0 : _errorData_error1.code);\n    }\n    return response.json();\n}\n/**\r\n * Login user with email and password\r\n * @param credentials - Login credentials\r\n * @returns Authentication response with user data and tokens\r\n */ async function login(credentials) {\n    const response = await apiRequest(\"/auth/login\", {\n        method: \"POST\",\n        body: JSON.stringify(credentials)\n    });\n    // Store tokens\n    setTokens(response.tokens);\n    return response;\n}\n/**\r\n * Register new user account\r\n * @param userData - Registration data\r\n * @returns Authentication response with user data and tokens\r\n */ async function register(userData) {\n    const response = await apiRequest(\"/auth/register\", {\n        method: \"POST\",\n        body: JSON.stringify(userData)\n    });\n    // Store tokens\n    setTokens(response.tokens);\n    return response;\n}\n/**\r\n * Logout user and clear tokens\r\n */ async function logout() {\n    const refreshToken = getRefreshToken();\n    try {\n        await apiRequest(\"/auth/logout\", {\n            method: \"POST\",\n            body: JSON.stringify({\n                refreshToken\n            })\n        });\n    } catch (error) {\n        // Continue with logout even if API call fails\n        console.warn(\"Logout API call failed:\", error);\n    }\n    clearTokens();\n}\n/**\r\n * Get current user profile\r\n * @returns Current user data\r\n */ async function getCurrentUser() {\n    const response = await apiRequest(\"/auth/profile\");\n    return response.user;\n}\n/**\r\n * Verify if current token is valid\r\n * @returns Token validity and user data\r\n */ async function verifyToken() {\n    try {\n        const response = await apiRequest(\"/auth/verify\");\n        return {\n            valid: response.valid,\n            user: response.user\n        };\n    } catch (error) {\n        return {\n            valid: false\n        };\n    }\n}\n/**\r\n * Refresh access token using refresh token\r\n * @returns New authentication tokens\r\n */ async function refreshTokens() {\n    const refreshToken = getRefreshToken();\n    if (!refreshToken) {\n        throw new Error(\"No refresh token available\");\n    }\n    const response = await fetch(\"\".concat(API_BASE, \"/auth/refresh\"), {\n        method: \"POST\",\n        headers: {\n            \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n            refreshToken\n        })\n    });\n    if (!response.ok) {\n        clearTokens();\n        throw new Error(\"Token refresh failed\");\n    }\n    const data = await response.json();\n    setTokens(data.tokens);\n    return data.tokens;\n}\n/**\r\n * Store authentication tokens in localStorage\r\n * @param tokens - Authentication tokens to store\r\n */ function setTokens(tokens) {\n    if (true) {\n        localStorage.setItem(\"accessToken\", tokens.accessToken);\n        localStorage.setItem(\"refreshToken\", tokens.refreshToken);\n    }\n}\n/**\r\n * Get stored access token\r\n * @returns Access token or null\r\n */ function getAccessToken() {\n    if (true) {\n        return localStorage.getItem(\"accessToken\");\n    }\n    return null;\n}\n/**\r\n * Get stored refresh token\r\n * @returns Refresh token or null\r\n */ function getRefreshToken() {\n    if (true) {\n        return localStorage.getItem(\"refreshToken\");\n    }\n    return null;\n}\n/**\r\n * Clear all stored authentication tokens\r\n */ function clearTokens() {\n    if (true) {\n        localStorage.removeItem(\"accessToken\");\n        localStorage.removeItem(\"refreshToken\");\n    }\n}\n/**\r\n * Check if user is currently authenticated\r\n * @returns True if user has valid tokens\r\n */ function isAuthenticated() {\n    return !!(getAccessToken() && getRefreshToken());\n}\n/**\r\n * Decode JWT token payload without verification\r\n * @param token - JWT token to decode\r\n * @returns Decoded payload or null\r\n */ function decodeToken(token) {\n    try {\n        const payload = token.split(\".\")[1];\n        return JSON.parse(atob(payload));\n    } catch (error) {\n        return null;\n    }\n}\n/**\r\n * Check if token is expired\r\n * @param token - JWT token to check\r\n * @returns True if token is expired\r\n */ function isTokenExpired(token) {\n    const decoded = decodeToken(token);\n    if (!decoded || !decoded.exp) return true;\n    return Date.now() >= decoded.exp * 1000;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/auth.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/next/dist/client/add-locale.js":
/*!******************************************************!*\
  !*** ../node_modules/next/dist/client/add-locale.js ***!
  \******************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"addLocale\", ({\n    enumerable: true,\n    get: function() {\n        return addLocale;\n    }\n}));\nconst _normalizetrailingslash = __webpack_require__(/*! ./normalize-trailing-slash */ \"(app-pages-browser)/../node_modules/next/dist/client/normalize-trailing-slash.js\");\nconst addLocale = function(path) {\n    for(var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){\n        args[_key - 1] = arguments[_key];\n    }\n    if (false) {}\n    return path;\n};\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=add-locale.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9hZGQtbG9jYWxlLmpzIiwibWFwcGluZ3MiOiI7Ozs7NkNBR2FBOzs7ZUFBQUE7OztvREFGOEI7QUFFcEMsTUFBTUEsWUFBdUIsU0FBQ0MsSUFBQUE7cUNBQVNDLE9BQUFBLElBQUFBLE1BQUFBLE9BQUFBLElBQUFBLE9BQUFBLElBQUFBLElBQUFBLE9BQUFBLEdBQUFBLE9BQUFBLE1BQUFBLE9BQUFBO1FBQUFBLElBQUFBLENBQUFBLE9BQUFBLEVBQUFBLEdBQUFBLFNBQUFBLENBQUFBLEtBQUFBOztJQUM1QyxJQUFJQyxLQUErQixFQUFFLEVBSXJDO0lBQ0EsT0FBT0Y7QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vc3JjL2NsaWVudC9hZGQtbG9jYWxlLnRzP2ZhYWUiXSwibmFtZXMiOlsiYWRkTG9jYWxlIiwicGF0aCIsImFyZ3MiLCJwcm9jZXNzIiwiZW52IiwiX19ORVhUX0kxOE5fU1VQUE9SVCIsIm5vcm1hbGl6ZVBhdGhUcmFpbGluZ1NsYXNoIiwicmVxdWlyZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/next/dist/client/add-locale.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/next/dist/client/get-domain-locale.js":
/*!*************************************************************!*\
  !*** ../node_modules/next/dist/client/get-domain-locale.js ***!
  \*************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"getDomainLocale\", ({\n    enumerable: true,\n    get: function() {\n        return getDomainLocale;\n    }\n}));\nconst _normalizetrailingslash = __webpack_require__(/*! ./normalize-trailing-slash */ \"(app-pages-browser)/../node_modules/next/dist/client/normalize-trailing-slash.js\");\nconst basePath =  false || \"\";\nfunction getDomainLocale(path, locale, locales, domainLocales) {\n    if (false) {} else {\n        return false;\n    }\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=get-domain-locale.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9nZXQtZG9tYWluLWxvY2FsZS5qcyIsIm1hcHBpbmdzIjoiOzs7O21EQU9nQkE7OztlQUFBQTs7O29EQUoyQjtBQUUzQyxNQUFNQyxXQUFXQyxNQUFtQyxJQUFlO0FBRTVELFNBQVNGLGdCQUNkSyxJQUFZLEVBQ1pDLE1BQXVCLEVBQ3ZCQyxPQUFrQixFQUNsQkMsYUFBOEI7SUFFOUIsSUFBSU4sS0FBK0IsRUFBRSxFQWdCckMsTUFBTztRQUNMLE9BQU87SUFDVDtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9zcmMvY2xpZW50L2dldC1kb21haW4tbG9jYWxlLnRzPzFkNGUiXSwibmFtZXMiOlsiZ2V0RG9tYWluTG9jYWxlIiwiYmFzZVBhdGgiLCJwcm9jZXNzIiwiZW52IiwiX19ORVhUX1JPVVRFUl9CQVNFUEFUSCIsInBhdGgiLCJsb2NhbGUiLCJsb2NhbGVzIiwiZG9tYWluTG9jYWxlcyIsIl9fTkVYVF9JMThOX1NVUFBPUlQiLCJub3JtYWxpemVMb2NhbGVQYXRoIiwicmVxdWlyZSIsImRldGVjdERvbWFpbkxvY2FsZSIsInRhcmdldCIsImRldGVjdGVkTG9jYWxlIiwiZG9tYWluIiwidW5kZWZpbmVkIiwicHJvdG8iLCJodHRwIiwiZmluYWxMb2NhbGUiLCJkZWZhdWx0TG9jYWxlIiwibm9ybWFsaXplUGF0aFRyYWlsaW5nU2xhc2giXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/next/dist/client/get-domain-locale.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/next/dist/client/link.js":
/*!************************************************!*\
  !*** ../node_modules/next/dist/client/link.js ***!
  \************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return _default;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/../node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/index.js\"));\nconst _resolvehref = __webpack_require__(/*! ./resolve-href */ \"(app-pages-browser)/../node_modules/next/dist/client/resolve-href.js\");\nconst _islocalurl = __webpack_require__(/*! ../shared/lib/router/utils/is-local-url */ \"(app-pages-browser)/../node_modules/next/dist/shared/lib/router/utils/is-local-url.js\");\nconst _formaturl = __webpack_require__(/*! ../shared/lib/router/utils/format-url */ \"(app-pages-browser)/../node_modules/next/dist/shared/lib/router/utils/format-url.js\");\nconst _utils = __webpack_require__(/*! ../shared/lib/utils */ \"(app-pages-browser)/../node_modules/next/dist/shared/lib/utils.js\");\nconst _addlocale = __webpack_require__(/*! ./add-locale */ \"(app-pages-browser)/../node_modules/next/dist/client/add-locale.js\");\nconst _routercontextsharedruntime = __webpack_require__(/*! ../shared/lib/router-context.shared-runtime */ \"(app-pages-browser)/../node_modules/next/dist/shared/lib/router-context.shared-runtime.js\");\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../shared/lib/app-router-context.shared-runtime */ \"(app-pages-browser)/../node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nconst _useintersection = __webpack_require__(/*! ./use-intersection */ \"(app-pages-browser)/../node_modules/next/dist/client/use-intersection.js\");\nconst _getdomainlocale = __webpack_require__(/*! ./get-domain-locale */ \"(app-pages-browser)/../node_modules/next/dist/client/get-domain-locale.js\");\nconst _addbasepath = __webpack_require__(/*! ./add-base-path */ \"(app-pages-browser)/../node_modules/next/dist/client/add-base-path.js\");\nconst _routerreducertypes = __webpack_require__(/*! ./components/router-reducer/router-reducer-types */ \"(app-pages-browser)/../node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst prefetched = new Set();\nfunction prefetch(router, href, as, options, appOptions, isAppRouter) {\n    if (typeof window === \"undefined\") {\n        return;\n    }\n    // app-router supports external urls out of the box so it shouldn't short-circuit here as support for e.g. `replace` is added in the app-router.\n    if (!isAppRouter && !(0, _islocalurl.isLocalURL)(href)) {\n        return;\n    }\n    // We should only dedupe requests when experimental.optimisticClientCache is\n    // disabled.\n    if (!options.bypassPrefetchedCheck) {\n        const locale = typeof options.locale !== \"undefined\" ? options.locale : \"locale\" in router ? router.locale : undefined;\n        const prefetchedKey = href + \"%\" + as + \"%\" + locale;\n        // If we've already fetched the key, then don't prefetch it again!\n        if (prefetched.has(prefetchedKey)) {\n            return;\n        }\n        // Mark this URL as prefetched.\n        prefetched.add(prefetchedKey);\n    }\n    const doPrefetch = async ()=>{\n        if (isAppRouter) {\n            // note that `appRouter.prefetch()` is currently sync,\n            // so we have to wrap this call in an async function to be able to catch() errors below.\n            return router.prefetch(href, appOptions);\n        } else {\n            return router.prefetch(href, as, options);\n        }\n    };\n    // Prefetch the JSON page if asked (only in the client)\n    // We need to handle a prefetch error here since we may be\n    // loading with priority which can reject but we don't\n    // want to force navigation since this is only a prefetch\n    doPrefetch().catch((err)=>{\n        if (true) {\n            // rethrow to show invalid URL errors\n            throw err;\n        }\n    });\n}\nfunction isModifiedEvent(event) {\n    const eventTarget = event.currentTarget;\n    const target = eventTarget.getAttribute(\"target\");\n    return target && target !== \"_self\" || event.metaKey || event.ctrlKey || event.shiftKey || event.altKey || // triggers resource download\n    event.nativeEvent && event.nativeEvent.which === 2;\n}\nfunction linkClicked(e, router, href, as, replace, shallow, scroll, locale, isAppRouter) {\n    const { nodeName } = e.currentTarget;\n    // anchors inside an svg have a lowercase nodeName\n    const isAnchorNodeName = nodeName.toUpperCase() === \"A\";\n    if (isAnchorNodeName && (isModifiedEvent(e) || // app-router supports external urls out of the box so it shouldn't short-circuit here as support for e.g. `replace` is added in the app-router.\n    !isAppRouter && !(0, _islocalurl.isLocalURL)(href))) {\n        // ignore click for browser’s default behavior\n        return;\n    }\n    e.preventDefault();\n    const navigate = ()=>{\n        // If the router is an NextRouter instance it will have `beforePopState`\n        const routerScroll = scroll != null ? scroll : true;\n        if (\"beforePopState\" in router) {\n            router[replace ? \"replace\" : \"push\"](href, as, {\n                shallow,\n                locale,\n                scroll: routerScroll\n            });\n        } else {\n            router[replace ? \"replace\" : \"push\"](as || href, {\n                scroll: routerScroll\n            });\n        }\n    };\n    if (isAppRouter) {\n        _react.default.startTransition(navigate);\n    } else {\n        navigate();\n    }\n}\nfunction formatStringOrUrl(urlObjOrString) {\n    if (typeof urlObjOrString === \"string\") {\n        return urlObjOrString;\n    }\n    return (0, _formaturl.formatUrl)(urlObjOrString);\n}\n/**\n * A React component that extends the HTML `<a>` element to provide [prefetching](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#2-prefetching)\n * and client-side navigation between routes.\n *\n * It is the primary way to navigate between routes in Next.js.\n *\n * Read more: [Next.js docs: `<Link>`](https://nextjs.org/docs/app/api-reference/components/link)\n */ const Link = /*#__PURE__*/ _s(_react.default.forwardRef(_c = _s(function LinkComponent(props, forwardedRef) {\n    _s();\n    let children;\n    const { href: hrefProp, as: asProp, children: childrenProp, prefetch: prefetchProp = null, passHref, replace, shallow, scroll, locale, onClick, onMouseEnter: onMouseEnterProp, onTouchStart: onTouchStartProp, legacyBehavior = false, ...restProps } = props;\n    children = childrenProp;\n    if (legacyBehavior && (typeof children === \"string\" || typeof children === \"number\")) {\n        children = /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n            children: children\n        });\n    }\n    const pagesRouter = _react.default.useContext(_routercontextsharedruntime.RouterContext);\n    const appRouter = _react.default.useContext(_approutercontextsharedruntime.AppRouterContext);\n    const router = pagesRouter != null ? pagesRouter : appRouter;\n    // We're in the app directory if there is no pages router.\n    const isAppRouter = !pagesRouter;\n    const prefetchEnabled = prefetchProp !== false;\n    /**\n     * The possible states for prefetch are:\n     * - null: this is the default \"auto\" mode, where we will prefetch partially if the link is in the viewport\n     * - true: we will prefetch if the link is visible and prefetch the full page, not just partially\n     * - false: we will not prefetch if in the viewport at all\n     */ const appPrefetchKind = prefetchProp === null ? _routerreducertypes.PrefetchKind.AUTO : _routerreducertypes.PrefetchKind.FULL;\n    if (true) {\n        function createPropError(args) {\n            return new Error(\"Failed prop type: The prop `\" + args.key + \"` expects a \" + args.expected + \" in `<Link>`, but got `\" + args.actual + \"` instead.\" + (typeof window !== \"undefined\" ? \"\\nOpen your browser's console to view the Component stack trace.\" : \"\"));\n        }\n        // TypeScript trick for type-guarding:\n        const requiredPropsGuard = {\n            href: true\n        };\n        const requiredProps = Object.keys(requiredPropsGuard);\n        requiredProps.forEach((key)=>{\n            if (key === \"href\") {\n                if (props[key] == null || typeof props[key] !== \"string\" && typeof props[key] !== \"object\") {\n                    throw createPropError({\n                        key,\n                        expected: \"`string` or `object`\",\n                        actual: props[key] === null ? \"null\" : typeof props[key]\n                    });\n                }\n            } else {\n                // TypeScript trick for type-guarding:\n                // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                const _ = key;\n            }\n        });\n        // TypeScript trick for type-guarding:\n        const optionalPropsGuard = {\n            as: true,\n            replace: true,\n            scroll: true,\n            shallow: true,\n            passHref: true,\n            prefetch: true,\n            locale: true,\n            onClick: true,\n            onMouseEnter: true,\n            onTouchStart: true,\n            legacyBehavior: true\n        };\n        const optionalProps = Object.keys(optionalPropsGuard);\n        optionalProps.forEach((key)=>{\n            const valType = typeof props[key];\n            if (key === \"as\") {\n                if (props[key] && valType !== \"string\" && valType !== \"object\") {\n                    throw createPropError({\n                        key,\n                        expected: \"`string` or `object`\",\n                        actual: valType\n                    });\n                }\n            } else if (key === \"locale\") {\n                if (props[key] && valType !== \"string\") {\n                    throw createPropError({\n                        key,\n                        expected: \"`string`\",\n                        actual: valType\n                    });\n                }\n            } else if (key === \"onClick\" || key === \"onMouseEnter\" || key === \"onTouchStart\") {\n                if (props[key] && valType !== \"function\") {\n                    throw createPropError({\n                        key,\n                        expected: \"`function`\",\n                        actual: valType\n                    });\n                }\n            } else if (key === \"replace\" || key === \"scroll\" || key === \"shallow\" || key === \"passHref\" || key === \"prefetch\" || key === \"legacyBehavior\") {\n                if (props[key] != null && valType !== \"boolean\") {\n                    throw createPropError({\n                        key,\n                        expected: \"`boolean`\",\n                        actual: valType\n                    });\n                }\n            } else {\n                // TypeScript trick for type-guarding:\n                // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                const _ = key;\n            }\n        });\n        // This hook is in a conditional but that is ok because `process.env.NODE_ENV` never changes\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        const hasWarned = _react.default.useRef(false);\n        if (props.prefetch && !hasWarned.current && !isAppRouter) {\n            hasWarned.current = true;\n            console.warn(\"Next.js auto-prefetches automatically based on viewport. The prefetch attribute is no longer needed. More: https://nextjs.org/docs/messages/prefetch-true-deprecated\");\n        }\n    }\n    if (true) {\n        if (isAppRouter && !asProp) {\n            let href;\n            if (typeof hrefProp === \"string\") {\n                href = hrefProp;\n            } else if (typeof hrefProp === \"object\" && typeof hrefProp.pathname === \"string\") {\n                href = hrefProp.pathname;\n            }\n            if (href) {\n                const hasDynamicSegment = href.split(\"/\").some((segment)=>segment.startsWith(\"[\") && segment.endsWith(\"]\"));\n                if (hasDynamicSegment) {\n                    throw new Error(\"Dynamic href `\" + href + \"` found in <Link> while using the `/app` router, this is not supported. Read more: https://nextjs.org/docs/messages/app-dir-dynamic-href\");\n                }\n            }\n        }\n    }\n    const { href, as } = _react.default.useMemo(()=>{\n        if (!pagesRouter) {\n            const resolvedHref = formatStringOrUrl(hrefProp);\n            return {\n                href: resolvedHref,\n                as: asProp ? formatStringOrUrl(asProp) : resolvedHref\n            };\n        }\n        const [resolvedHref, resolvedAs] = (0, _resolvehref.resolveHref)(pagesRouter, hrefProp, true);\n        return {\n            href: resolvedHref,\n            as: asProp ? (0, _resolvehref.resolveHref)(pagesRouter, asProp) : resolvedAs || resolvedHref\n        };\n    }, [\n        pagesRouter,\n        hrefProp,\n        asProp\n    ]);\n    const previousHref = _react.default.useRef(href);\n    const previousAs = _react.default.useRef(as);\n    // This will return the first child, if multiple are provided it will throw an error\n    let child;\n    if (legacyBehavior) {\n        if (true) {\n            if (onClick) {\n                console.warn('\"onClick\" was passed to <Link> with `href` of `' + hrefProp + '` but \"legacyBehavior\" was set. The legacy behavior requires onClick be set on the child of next/link');\n            }\n            if (onMouseEnterProp) {\n                console.warn('\"onMouseEnter\" was passed to <Link> with `href` of `' + hrefProp + '` but \"legacyBehavior\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link');\n            }\n            try {\n                child = _react.default.Children.only(children);\n            } catch (err) {\n                if (!children) {\n                    throw new Error(\"No children were passed to <Link> with `href` of `\" + hrefProp + \"` but one child is required https://nextjs.org/docs/messages/link-no-children\");\n                }\n                throw new Error(\"Multiple children were passed to <Link> with `href` of `\" + hrefProp + \"` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children\" + (typeof window !== \"undefined\" ? \" \\nOpen your browser's console to view the Component stack trace.\" : \"\"));\n            }\n        } else {}\n    } else {\n        if (true) {\n            if ((children == null ? void 0 : children.type) === \"a\") {\n                throw new Error(\"Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor\");\n            }\n        }\n    }\n    const childRef = legacyBehavior ? child && typeof child === \"object\" && child.ref : forwardedRef;\n    const [setIntersectionRef, isVisible, resetVisible] = (0, _useintersection.useIntersection)({\n        rootMargin: \"200px\"\n    });\n    const setRef = _react.default.useCallback((el)=>{\n        // Before the link getting observed, check if visible state need to be reset\n        if (previousAs.current !== as || previousHref.current !== href) {\n            resetVisible();\n            previousAs.current = as;\n            previousHref.current = href;\n        }\n        setIntersectionRef(el);\n        if (childRef) {\n            if (typeof childRef === \"function\") childRef(el);\n            else if (typeof childRef === \"object\") {\n                childRef.current = el;\n            }\n        }\n    }, [\n        as,\n        childRef,\n        href,\n        resetVisible,\n        setIntersectionRef\n    ]);\n    // Prefetch the URL if we haven't already and it's visible.\n    _react.default.useEffect(()=>{\n        // in dev, we only prefetch on hover to avoid wasting resources as the prefetch will trigger compiling the page.\n        if (true) {\n            return;\n        }\n        if (!router) {\n            return;\n        }\n        // If we don't need to prefetch the URL, don't do prefetch.\n        if (!isVisible || !prefetchEnabled) {\n            return;\n        }\n        // Prefetch the URL.\n        prefetch(router, href, as, {\n            locale\n        }, {\n            kind: appPrefetchKind\n        }, isAppRouter);\n    }, [\n        as,\n        href,\n        isVisible,\n        locale,\n        prefetchEnabled,\n        pagesRouter == null ? void 0 : pagesRouter.locale,\n        router,\n        isAppRouter,\n        appPrefetchKind\n    ]);\n    const childProps = {\n        ref: setRef,\n        onClick (e) {\n            if (true) {\n                if (!e) {\n                    throw new Error('Component rendered inside next/link has to pass click event to \"onClick\" prop.');\n                }\n            }\n            if (!legacyBehavior && typeof onClick === \"function\") {\n                onClick(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onClick === \"function\") {\n                child.props.onClick(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (e.defaultPrevented) {\n                return;\n            }\n            linkClicked(e, router, href, as, replace, shallow, scroll, locale, isAppRouter);\n        },\n        onMouseEnter (e) {\n            if (!legacyBehavior && typeof onMouseEnterProp === \"function\") {\n                onMouseEnterProp(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onMouseEnter === \"function\") {\n                child.props.onMouseEnter(e);\n            }\n            if (!router) {\n                return;\n            }\n            if ((!prefetchEnabled || \"development\" === \"development\") && isAppRouter) {\n                return;\n            }\n            prefetch(router, href, as, {\n                locale,\n                priority: true,\n                // @see {https://github.com/vercel/next.js/discussions/40268?sort=top#discussioncomment-3572642}\n                bypassPrefetchedCheck: true\n            }, {\n                kind: appPrefetchKind\n            }, isAppRouter);\n        },\n        onTouchStart:  false ? 0 : function onTouchStart(e) {\n            if (!legacyBehavior && typeof onTouchStartProp === \"function\") {\n                onTouchStartProp(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onTouchStart === \"function\") {\n                child.props.onTouchStart(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (!prefetchEnabled && isAppRouter) {\n                return;\n            }\n            prefetch(router, href, as, {\n                locale,\n                priority: true,\n                // @see {https://github.com/vercel/next.js/discussions/40268?sort=top#discussioncomment-3572642}\n                bypassPrefetchedCheck: true\n            }, {\n                kind: appPrefetchKind\n            }, isAppRouter);\n        }\n    };\n    // If child is an <a> tag and doesn't have a href attribute, or if the 'passHref' property is\n    // defined, we specify the current 'href', so that repetition is not needed by the user.\n    // If the url is absolute, we can bypass the logic to prepend the domain and locale.\n    if ((0, _utils.isAbsoluteUrl)(as)) {\n        childProps.href = as;\n    } else if (!legacyBehavior || passHref || child.type === \"a\" && !(\"href\" in child.props)) {\n        const curLocale = typeof locale !== \"undefined\" ? locale : pagesRouter == null ? void 0 : pagesRouter.locale;\n        // we only render domain locales if we are currently on a domain locale\n        // so that locale links are still visitable in development/preview envs\n        const localeDomain = (pagesRouter == null ? void 0 : pagesRouter.isLocaleDomain) && (0, _getdomainlocale.getDomainLocale)(as, curLocale, pagesRouter == null ? void 0 : pagesRouter.locales, pagesRouter == null ? void 0 : pagesRouter.domainLocales);\n        childProps.href = localeDomain || (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(as, curLocale, pagesRouter == null ? void 0 : pagesRouter.defaultLocale));\n    }\n    return legacyBehavior ? /*#__PURE__*/ _react.default.cloneElement(child, childProps) : /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n        ...restProps,\n        ...childProps,\n        children: children\n    });\n}, \"wKD5mb5mk47bkaStGb/Fvd6RWZE=\")), \"wKD5mb5mk47bkaStGb/Fvd6RWZE=\");\n_c1 = Link;\nconst _default = Link;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=link.js.map\nvar _c, _c1;\n$RefreshReg$(_c, \"Link$_react.default.forwardRef\");\n$RefreshReg$(_c1, \"Link\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/next/dist/client/link.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/next/dist/client/request-idle-callback.js":
/*!*****************************************************************!*\
  !*** ../node_modules/next/dist/client/request-idle-callback.js ***!
  \*****************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    cancelIdleCallback: function() {\n        return cancelIdleCallback;\n    },\n    requestIdleCallback: function() {\n        return requestIdleCallback;\n    }\n});\nconst requestIdleCallback = typeof self !== \"undefined\" && self.requestIdleCallback && self.requestIdleCallback.bind(window) || function(cb) {\n    let start = Date.now();\n    return self.setTimeout(function() {\n        cb({\n            didTimeout: false,\n            timeRemaining: function() {\n                return Math.max(0, 50 - (Date.now() - start));\n            }\n        });\n    }, 1);\n};\nconst cancelIdleCallback = typeof self !== \"undefined\" && self.cancelIdleCallback && self.cancelIdleCallback.bind(window) || function(id) {\n    return clearTimeout(id);\n};\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=request-idle-callback.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9yZXF1ZXN0LWlkbGUtY2FsbGJhY2suanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0lBZ0JhQSxvQkFBa0I7ZUFBbEJBOztJQWhCQUMscUJBQW1CO2VBQW5CQTs7O0FBQU4sTUFBTUEsc0JBQ1gsT0FBUUMsU0FBUyxlQUNmQSxLQUFLRCxtQkFBbUIsSUFDeEJDLEtBQUtELG1CQUFtQixDQUFDRSxJQUFJLENBQUNDLFdBQ2hDLFNBQVVDLEVBQXVCO0lBQy9CLElBQUlDLFFBQVFDLEtBQUtDLEdBQUc7SUFDcEIsT0FBT04sS0FBS08sVUFBVSxDQUFDO1FBQ3JCSixHQUFHO1lBQ0RLLFlBQVk7WUFDWkMsZUFBZTtnQkFDYixPQUFPQyxLQUFLQyxHQUFHLENBQUMsR0FBRyxLQUFNTixDQUFBQSxLQUFLQyxHQUFHLEtBQUtGLEtBQUFBO1lBQ3hDO1FBQ0Y7SUFDRixHQUFHO0FBQ0w7QUFFSyxNQUFNTixxQkFDWCxPQUFRRSxTQUFTLGVBQ2ZBLEtBQUtGLGtCQUFrQixJQUN2QkUsS0FBS0Ysa0JBQWtCLENBQUNHLElBQUksQ0FBQ0MsV0FDL0IsU0FBVVUsRUFBVTtJQUNsQixPQUFPQyxhQUFhRDtBQUN0QiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vc3JjL2NsaWVudC9yZXF1ZXN0LWlkbGUtY2FsbGJhY2sudHM/MDVmNCJdLCJuYW1lcyI6WyJjYW5jZWxJZGxlQ2FsbGJhY2siLCJyZXF1ZXN0SWRsZUNhbGxiYWNrIiwic2VsZiIsImJpbmQiLCJ3aW5kb3ciLCJjYiIsInN0YXJ0IiwiRGF0ZSIsIm5vdyIsInNldFRpbWVvdXQiLCJkaWRUaW1lb3V0IiwidGltZVJlbWFpbmluZyIsIk1hdGgiLCJtYXgiLCJpZCIsImNsZWFyVGltZW91dCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/next/dist/client/request-idle-callback.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/next/dist/client/resolve-href.js":
/*!********************************************************!*\
  !*** ../node_modules/next/dist/client/resolve-href.js ***!
  \********************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"resolveHref\", ({\n    enumerable: true,\n    get: function() {\n        return resolveHref;\n    }\n}));\nconst _querystring = __webpack_require__(/*! ../shared/lib/router/utils/querystring */ \"(app-pages-browser)/../node_modules/next/dist/shared/lib/router/utils/querystring.js\");\nconst _formaturl = __webpack_require__(/*! ../shared/lib/router/utils/format-url */ \"(app-pages-browser)/../node_modules/next/dist/shared/lib/router/utils/format-url.js\");\nconst _omit = __webpack_require__(/*! ../shared/lib/router/utils/omit */ \"(app-pages-browser)/../node_modules/next/dist/shared/lib/router/utils/omit.js\");\nconst _utils = __webpack_require__(/*! ../shared/lib/utils */ \"(app-pages-browser)/../node_modules/next/dist/shared/lib/utils.js\");\nconst _normalizetrailingslash = __webpack_require__(/*! ./normalize-trailing-slash */ \"(app-pages-browser)/../node_modules/next/dist/client/normalize-trailing-slash.js\");\nconst _islocalurl = __webpack_require__(/*! ../shared/lib/router/utils/is-local-url */ \"(app-pages-browser)/../node_modules/next/dist/shared/lib/router/utils/is-local-url.js\");\nconst _utils1 = __webpack_require__(/*! ../shared/lib/router/utils */ \"(app-pages-browser)/../node_modules/next/dist/shared/lib/router/utils/index.js\");\nconst _interpolateas = __webpack_require__(/*! ../shared/lib/router/utils/interpolate-as */ \"(app-pages-browser)/../node_modules/next/dist/shared/lib/router/utils/interpolate-as.js\");\nfunction resolveHref(router, href, resolveAs) {\n    // we use a dummy base url for relative urls\n    let base;\n    let urlAsString = typeof href === \"string\" ? href : (0, _formaturl.formatWithValidation)(href);\n    // repeated slashes and backslashes in the URL are considered\n    // invalid and will never match a Next.js page/file\n    const urlProtoMatch = urlAsString.match(/^[a-zA-Z]{1,}:\\/\\//);\n    const urlAsStringNoProto = urlProtoMatch ? urlAsString.slice(urlProtoMatch[0].length) : urlAsString;\n    const urlParts = urlAsStringNoProto.split(\"?\", 1);\n    if ((urlParts[0] || \"\").match(/(\\/\\/|\\\\)/)) {\n        console.error(\"Invalid href '\" + urlAsString + \"' passed to next/router in page: '\" + router.pathname + \"'. Repeated forward-slashes (//) or backslashes \\\\ are not valid in the href.\");\n        const normalizedUrl = (0, _utils.normalizeRepeatedSlashes)(urlAsStringNoProto);\n        urlAsString = (urlProtoMatch ? urlProtoMatch[0] : \"\") + normalizedUrl;\n    }\n    // Return because it cannot be routed by the Next.js router\n    if (!(0, _islocalurl.isLocalURL)(urlAsString)) {\n        return resolveAs ? [\n            urlAsString\n        ] : urlAsString;\n    }\n    try {\n        base = new URL(urlAsString.startsWith(\"#\") ? router.asPath : router.pathname, \"http://n\");\n    } catch (_) {\n        // fallback to / for invalid asPath values e.g. //\n        base = new URL(\"/\", \"http://n\");\n    }\n    try {\n        const finalUrl = new URL(urlAsString, base);\n        finalUrl.pathname = (0, _normalizetrailingslash.normalizePathTrailingSlash)(finalUrl.pathname);\n        let interpolatedAs = \"\";\n        if ((0, _utils1.isDynamicRoute)(finalUrl.pathname) && finalUrl.searchParams && resolveAs) {\n            const query = (0, _querystring.searchParamsToUrlQuery)(finalUrl.searchParams);\n            const { result, params } = (0, _interpolateas.interpolateAs)(finalUrl.pathname, finalUrl.pathname, query);\n            if (result) {\n                interpolatedAs = (0, _formaturl.formatWithValidation)({\n                    pathname: result,\n                    hash: finalUrl.hash,\n                    query: (0, _omit.omit)(query, params)\n                });\n            }\n        }\n        // if the origin didn't change, it means we received a relative href\n        const resolvedHref = finalUrl.origin === base.origin ? finalUrl.href.slice(finalUrl.origin.length) : finalUrl.href;\n        return resolveAs ? [\n            resolvedHref,\n            interpolatedAs || resolvedHref\n        ] : resolvedHref;\n    } catch (_) {\n        return resolveAs ? [\n            urlAsString\n        ] : urlAsString;\n    }\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=resolve-href.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/next/dist/client/resolve-href.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/next/dist/client/use-intersection.js":
/*!************************************************************!*\
  !*** ../node_modules/next/dist/client/use-intersection.js ***!
  \************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"useIntersection\", ({\n    enumerable: true,\n    get: function() {\n        return useIntersection;\n    }\n}));\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/index.js\");\nconst _requestidlecallback = __webpack_require__(/*! ./request-idle-callback */ \"(app-pages-browser)/../node_modules/next/dist/client/request-idle-callback.js\");\nconst hasIntersectionObserver = typeof IntersectionObserver === \"function\";\nconst observers = new Map();\nconst idList = [];\nfunction createObserver(options) {\n    const id = {\n        root: options.root || null,\n        margin: options.rootMargin || \"\"\n    };\n    const existing = idList.find((obj)=>obj.root === id.root && obj.margin === id.margin);\n    let instance;\n    if (existing) {\n        instance = observers.get(existing);\n        if (instance) {\n            return instance;\n        }\n    }\n    const elements = new Map();\n    const observer = new IntersectionObserver((entries)=>{\n        entries.forEach((entry)=>{\n            const callback = elements.get(entry.target);\n            const isVisible = entry.isIntersecting || entry.intersectionRatio > 0;\n            if (callback && isVisible) {\n                callback(isVisible);\n            }\n        });\n    }, options);\n    instance = {\n        id,\n        observer,\n        elements\n    };\n    idList.push(id);\n    observers.set(id, instance);\n    return instance;\n}\nfunction observe(element, callback, options) {\n    const { id, observer, elements } = createObserver(options);\n    elements.set(element, callback);\n    observer.observe(element);\n    return function unobserve() {\n        elements.delete(element);\n        observer.unobserve(element);\n        // Destroy observer when there's nothing left to watch:\n        if (elements.size === 0) {\n            observer.disconnect();\n            observers.delete(id);\n            const index = idList.findIndex((obj)=>obj.root === id.root && obj.margin === id.margin);\n            if (index > -1) {\n                idList.splice(index, 1);\n            }\n        }\n    };\n}\nfunction useIntersection(param) {\n    let { rootRef, rootMargin, disabled } = param;\n    const isDisabled = disabled || !hasIntersectionObserver;\n    const [visible, setVisible] = (0, _react.useState)(false);\n    const elementRef = (0, _react.useRef)(null);\n    const setElement = (0, _react.useCallback)((element)=>{\n        elementRef.current = element;\n    }, []);\n    (0, _react.useEffect)(()=>{\n        if (hasIntersectionObserver) {\n            if (isDisabled || visible) return;\n            const element = elementRef.current;\n            if (element && element.tagName) {\n                const unobserve = observe(element, (isVisible)=>isVisible && setVisible(isVisible), {\n                    root: rootRef == null ? void 0 : rootRef.current,\n                    rootMargin\n                });\n                return unobserve;\n            }\n        } else {\n            if (!visible) {\n                const idleCallback = (0, _requestidlecallback.requestIdleCallback)(()=>setVisible(true));\n                return ()=>(0, _requestidlecallback.cancelIdleCallback)(idleCallback);\n            }\n        }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        isDisabled,\n        rootMargin,\n        rootRef,\n        visible,\n        elementRef.current\n    ]);\n    const resetVisible = (0, _react.useCallback)(()=>{\n        setVisible(false);\n    }, []);\n    return [\n        setElement,\n        visible,\n        resetVisible\n    ];\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=use-intersection.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/next/dist/client/use-intersection.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/next/dist/shared/lib/escape-regexp.js":
/*!*************************************************************!*\
  !*** ../node_modules/next/dist/shared/lib/escape-regexp.js ***!
  \*************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("// regexp is based on https://github.com/sindresorhus/escape-string-regexp\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"escapeStringRegexp\", ({\n    enumerable: true,\n    get: function() {\n        return escapeStringRegexp;\n    }\n}));\nconst reHasRegExp = /[|\\\\{}()[\\]^$+*?.-]/;\nconst reReplaceRegExp = /[|\\\\{}()[\\]^$+*?.-]/g;\nfunction escapeStringRegexp(str) {\n    // see also: https://github.com/lodash/lodash/blob/2da024c3b4f9947a48517639de7560457cd4ec6c/escapeRegExp.js#L23\n    if (reHasRegExp.test(str)) {\n        return str.replace(reReplaceRegExp, \"\\\\$&\");\n    }\n    return str;\n} //# sourceMappingURL=escape-regexp.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NoYXJlZC9saWIvZXNjYXBlLXJlZ2V4cC5qcyIsIm1hcHBpbmdzIjoiQUFBQSwwRUFBMEU7Ozs7O3NEQUkxREE7OztlQUFBQTs7O0FBSGhCLE1BQU1DLGNBQWM7QUFDcEIsTUFBTUMsa0JBQWtCO0FBRWpCLFNBQVNGLG1CQUFtQkcsR0FBVztJQUM1QywrR0FBK0c7SUFDL0csSUFBSUYsWUFBWUcsSUFBSSxDQUFDRCxNQUFNO1FBQ3pCLE9BQU9BLElBQUlFLE9BQU8sQ0FBQ0gsaUJBQWlCO0lBQ3RDO0lBQ0EsT0FBT0M7QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vLi4vc3JjL3NoYXJlZC9saWIvZXNjYXBlLXJlZ2V4cC50cz9kY2IxIl0sIm5hbWVzIjpbImVzY2FwZVN0cmluZ1JlZ2V4cCIsInJlSGFzUmVnRXhwIiwicmVSZXBsYWNlUmVnRXhwIiwic3RyIiwidGVzdCIsInJlcGxhY2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/next/dist/shared/lib/escape-regexp.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/next/dist/shared/lib/router-context.shared-runtime.js":
/*!*****************************************************************************!*\
  !*** ../node_modules/next/dist/shared/lib/router-context.shared-runtime.js ***!
  \*****************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"RouterContext\", ({\n    enumerable: true,\n    get: function() {\n        return RouterContext;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/../node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/index.js\"));\nconst RouterContext = _react.default.createContext(null);\nif (true) {\n    RouterContext.displayName = \"RouterContext\";\n} //# sourceMappingURL=router-context.shared-runtime.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NoYXJlZC9saWIvcm91dGVyLWNvbnRleHQuc2hhcmVkLXJ1bnRpbWUuanMiLCJtYXBwaW5ncyI6Ijs7OztpREFHYUE7OztlQUFBQTs7Ozs0RUFISztBQUdYLE1BQU1BLGdCQUFnQkMsT0FBQUEsT0FBSyxDQUFDQyxhQUFhLENBQW9CO0FBRXBFLElBQUlDLElBQXlCLEVBQWM7SUFDekNILGNBQWNJLFdBQVcsR0FBRztBQUM5QiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vLi4vc3JjL3NoYXJlZC9saWIvcm91dGVyLWNvbnRleHQuc2hhcmVkLXJ1bnRpbWUudHM/NjM2YyJdLCJuYW1lcyI6WyJSb3V0ZXJDb250ZXh0IiwiUmVhY3QiLCJjcmVhdGVDb250ZXh0IiwicHJvY2VzcyIsImRpc3BsYXlOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/next/dist/shared/lib/router-context.shared-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/next/dist/shared/lib/router/utils/format-url.js":
/*!***********************************************************************!*\
  !*** ../node_modules/next/dist/shared/lib/router/utils/format-url.js ***!
  \***********************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("// Format function modified from nodejs\n// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    formatUrl: function() {\n        return formatUrl;\n    },\n    formatWithValidation: function() {\n        return formatWithValidation;\n    },\n    urlObjectKeys: function() {\n        return urlObjectKeys;\n    }\n});\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/../node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _querystring = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! ./querystring */ \"(app-pages-browser)/../node_modules/next/dist/shared/lib/router/utils/querystring.js\"));\nconst slashedProtocols = /https?|ftp|gopher|file/;\nfunction formatUrl(urlObj) {\n    let { auth, hostname } = urlObj;\n    let protocol = urlObj.protocol || \"\";\n    let pathname = urlObj.pathname || \"\";\n    let hash = urlObj.hash || \"\";\n    let query = urlObj.query || \"\";\n    let host = false;\n    auth = auth ? encodeURIComponent(auth).replace(/%3A/i, \":\") + \"@\" : \"\";\n    if (urlObj.host) {\n        host = auth + urlObj.host;\n    } else if (hostname) {\n        host = auth + (~hostname.indexOf(\":\") ? \"[\" + hostname + \"]\" : hostname);\n        if (urlObj.port) {\n            host += \":\" + urlObj.port;\n        }\n    }\n    if (query && typeof query === \"object\") {\n        query = String(_querystring.urlQueryToSearchParams(query));\n    }\n    let search = urlObj.search || query && \"?\" + query || \"\";\n    if (protocol && !protocol.endsWith(\":\")) protocol += \":\";\n    if (urlObj.slashes || (!protocol || slashedProtocols.test(protocol)) && host !== false) {\n        host = \"//\" + (host || \"\");\n        if (pathname && pathname[0] !== \"/\") pathname = \"/\" + pathname;\n    } else if (!host) {\n        host = \"\";\n    }\n    if (hash && hash[0] !== \"#\") hash = \"#\" + hash;\n    if (search && search[0] !== \"?\") search = \"?\" + search;\n    pathname = pathname.replace(/[?#]/g, encodeURIComponent);\n    search = search.replace(\"#\", \"%23\");\n    return \"\" + protocol + host + pathname + search + hash;\n}\nconst urlObjectKeys = [\n    \"auth\",\n    \"hash\",\n    \"host\",\n    \"hostname\",\n    \"href\",\n    \"path\",\n    \"pathname\",\n    \"port\",\n    \"protocol\",\n    \"query\",\n    \"search\",\n    \"slashes\"\n];\nfunction formatWithValidation(url) {\n    if (true) {\n        if (url !== null && typeof url === \"object\") {\n            Object.keys(url).forEach((key)=>{\n                if (!urlObjectKeys.includes(key)) {\n                    console.warn(\"Unknown key passed via urlObject into url.format: \" + key);\n                }\n            });\n        }\n    }\n    return formatUrl(url);\n} //# sourceMappingURL=format-url.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/next/dist/shared/lib/router/utils/format-url.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/next/dist/shared/lib/router/utils/index.js":
/*!******************************************************************!*\
  !*** ../node_modules/next/dist/shared/lib/router/utils/index.js ***!
  \******************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    getSortedRoutes: function() {\n        return _sortedroutes.getSortedRoutes;\n    },\n    isDynamicRoute: function() {\n        return _isdynamic.isDynamicRoute;\n    }\n});\nconst _sortedroutes = __webpack_require__(/*! ./sorted-routes */ \"(app-pages-browser)/../node_modules/next/dist/shared/lib/router/utils/sorted-routes.js\");\nconst _isdynamic = __webpack_require__(/*! ./is-dynamic */ \"(app-pages-browser)/../node_modules/next/dist/shared/lib/router/utils/is-dynamic.js\"); //# sourceMappingURL=index.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NoYXJlZC9saWIvcm91dGVyL3V0aWxzL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztJQUFTQSxpQkFBZTtlQUFmQSxjQUFBQSxlQUFlOztJQUNmQyxnQkFBYztlQUFkQSxXQUFBQSxjQUFjOzs7MENBRFM7dUNBQ0QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uLy4uLy4uLy4uL3NyYy9zaGFyZWQvbGliL3JvdXRlci91dGlscy9pbmRleC50cz83MmQ1Il0sIm5hbWVzIjpbImdldFNvcnRlZFJvdXRlcyIsImlzRHluYW1pY1JvdXRlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/next/dist/shared/lib/router/utils/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/next/dist/shared/lib/router/utils/interpolate-as.js":
/*!***************************************************************************!*\
  !*** ../node_modules/next/dist/shared/lib/router/utils/interpolate-as.js ***!
  \***************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"interpolateAs\", ({\n    enumerable: true,\n    get: function() {\n        return interpolateAs;\n    }\n}));\nconst _routematcher = __webpack_require__(/*! ./route-matcher */ \"(app-pages-browser)/../node_modules/next/dist/shared/lib/router/utils/route-matcher.js\");\nconst _routeregex = __webpack_require__(/*! ./route-regex */ \"(app-pages-browser)/../node_modules/next/dist/shared/lib/router/utils/route-regex.js\");\nfunction interpolateAs(route, asPathname, query) {\n    let interpolatedRoute = \"\";\n    const dynamicRegex = (0, _routeregex.getRouteRegex)(route);\n    const dynamicGroups = dynamicRegex.groups;\n    const dynamicMatches = (asPathname !== route ? (0, _routematcher.getRouteMatcher)(dynamicRegex)(asPathname) : \"\") || // Fall back to reading the values from the href\n    // TODO: should this take priority; also need to change in the router.\n    query;\n    interpolatedRoute = route;\n    const params = Object.keys(dynamicGroups);\n    if (!params.every((param)=>{\n        let value = dynamicMatches[param] || \"\";\n        const { repeat, optional } = dynamicGroups[param];\n        // support single-level catch-all\n        // TODO: more robust handling for user-error (passing `/`)\n        let replaced = \"[\" + (repeat ? \"...\" : \"\") + param + \"]\";\n        if (optional) {\n            replaced = (!value ? \"/\" : \"\") + \"[\" + replaced + \"]\";\n        }\n        if (repeat && !Array.isArray(value)) value = [\n            value\n        ];\n        return (optional || param in dynamicMatches) && // Interpolate group into data URL if present\n        (interpolatedRoute = interpolatedRoute.replace(replaced, repeat ? value.map(// path delimiter escaped since they are being inserted\n        // into the URL and we expect URL encoded segments\n        // when parsing dynamic route params\n        (segment)=>encodeURIComponent(segment)).join(\"/\") : encodeURIComponent(value)) || \"/\");\n    })) {\n        interpolatedRoute = \"\" // did not satisfy all requirements\n        ;\n    // n.b. We ignore this error because we handle warning for this case in\n    // development in the `<Link>` component directly.\n    }\n    return {\n        params,\n        result: interpolatedRoute\n    };\n} //# sourceMappingURL=interpolate-as.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/next/dist/shared/lib/router/utils/interpolate-as.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/next/dist/shared/lib/router/utils/is-dynamic.js":
/*!***********************************************************************!*\
  !*** ../node_modules/next/dist/shared/lib/router/utils/is-dynamic.js ***!
  \***********************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"isDynamicRoute\", ({\n    enumerable: true,\n    get: function() {\n        return isDynamicRoute;\n    }\n}));\nconst _interceptionroutes = __webpack_require__(/*! ../../../../server/future/helpers/interception-routes */ \"(app-pages-browser)/../node_modules/next/dist/server/future/helpers/interception-routes.js\");\n// Identify /[param]/ in route string\nconst TEST_ROUTE = /\\/\\[[^/]+?\\](?=\\/|$)/;\nfunction isDynamicRoute(route) {\n    if ((0, _interceptionroutes.isInterceptionRouteAppPath)(route)) {\n        route = (0, _interceptionroutes.extractInterceptionRouteInformation)(route).interceptedRoute;\n    }\n    return TEST_ROUTE.test(route);\n} //# sourceMappingURL=is-dynamic.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NoYXJlZC9saWIvcm91dGVyL3V0aWxzL2lzLWR5bmFtaWMuanMiLCJtYXBwaW5ncyI6Ijs7OztrREFRZ0JBOzs7ZUFBQUE7OztnREFMVDtBQUVQLHFDQUFxQztBQUNyQyxNQUFNQyxhQUFhO0FBRVosU0FBU0QsZUFBZUUsS0FBYTtJQUMxQyxJQUFJQyxDQUFBQSxHQUFBQSxvQkFBQUEsMEJBQTBCLEVBQUNELFFBQVE7UUFDckNBLFFBQVFFLENBQUFBLEdBQUFBLG9CQUFBQSxtQ0FBbUMsRUFBQ0YsT0FBT0csZ0JBQWdCO0lBQ3JFO0lBRUEsT0FBT0osV0FBV0ssSUFBSSxDQUFDSjtBQUN6QiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vLi4vLi4vLi4vc3JjL3NoYXJlZC9saWIvcm91dGVyL3V0aWxzL2lzLWR5bmFtaWMudHM/ODJkYyJdLCJuYW1lcyI6WyJpc0R5bmFtaWNSb3V0ZSIsIlRFU1RfUk9VVEUiLCJyb3V0ZSIsImlzSW50ZXJjZXB0aW9uUm91dGVBcHBQYXRoIiwiZXh0cmFjdEludGVyY2VwdGlvblJvdXRlSW5mb3JtYXRpb24iLCJpbnRlcmNlcHRlZFJvdXRlIiwidGVzdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/next/dist/shared/lib/router/utils/is-dynamic.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/next/dist/shared/lib/router/utils/is-local-url.js":
/*!*************************************************************************!*\
  !*** ../node_modules/next/dist/shared/lib/router/utils/is-local-url.js ***!
  \*************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"isLocalURL\", ({\n    enumerable: true,\n    get: function() {\n        return isLocalURL;\n    }\n}));\nconst _utils = __webpack_require__(/*! ../../utils */ \"(app-pages-browser)/../node_modules/next/dist/shared/lib/utils.js\");\nconst _hasbasepath = __webpack_require__(/*! ../../../../client/has-base-path */ \"(app-pages-browser)/../node_modules/next/dist/client/has-base-path.js\");\nfunction isLocalURL(url) {\n    // prevent a hydration mismatch on href for url with anchor refs\n    if (!(0, _utils.isAbsoluteUrl)(url)) return true;\n    try {\n        // absolute urls can be local if they are on the same origin\n        const locationOrigin = (0, _utils.getLocationOrigin)();\n        const resolved = new URL(url, locationOrigin);\n        return resolved.origin === locationOrigin && (0, _hasbasepath.hasBasePath)(resolved.pathname);\n    } catch (_) {\n        return false;\n    }\n} //# sourceMappingURL=is-local-url.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NoYXJlZC9saWIvcm91dGVyL3V0aWxzL2lzLWxvY2FsLXVybC5qcyIsIm1hcHBpbmdzIjoiOzs7OzhDQU1nQkE7OztlQUFBQTs7O21DQU5pQzt5Q0FDckI7QUFLckIsU0FBU0EsV0FBV0MsR0FBVztJQUNwQyxnRUFBZ0U7SUFDaEUsSUFBSSxDQUFDQyxDQUFBQSxHQUFBQSxPQUFBQSxhQUFhLEVBQUNELE1BQU0sT0FBTztJQUNoQyxJQUFJO1FBQ0YsNERBQTREO1FBQzVELE1BQU1FLGlCQUFpQkMsQ0FBQUEsR0FBQUEsT0FBQUEsaUJBQWlCO1FBQ3hDLE1BQU1DLFdBQVcsSUFBSUMsSUFBSUwsS0FBS0U7UUFDOUIsT0FBT0UsU0FBU0UsTUFBTSxLQUFLSixrQkFBa0JLLENBQUFBLEdBQUFBLGFBQUFBLFdBQVcsRUFBQ0gsU0FBU0ksUUFBUTtJQUM1RSxFQUFFLE9BQU9DLEdBQUc7UUFDVixPQUFPO0lBQ1Q7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vLi4vLi4vLi4vc3JjL3NoYXJlZC9saWIvcm91dGVyL3V0aWxzL2lzLWxvY2FsLXVybC50cz8wNGU1Il0sIm5hbWVzIjpbImlzTG9jYWxVUkwiLCJ1cmwiLCJpc0Fic29sdXRlVXJsIiwibG9jYXRpb25PcmlnaW4iLCJnZXRMb2NhdGlvbk9yaWdpbiIsInJlc29sdmVkIiwiVVJMIiwib3JpZ2luIiwiaGFzQmFzZVBhdGgiLCJwYXRobmFtZSIsIl8iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/next/dist/shared/lib/router/utils/is-local-url.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/next/dist/shared/lib/router/utils/omit.js":
/*!*****************************************************************!*\
  !*** ../node_modules/next/dist/shared/lib/router/utils/omit.js ***!
  \*****************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"omit\", ({\n    enumerable: true,\n    get: function() {\n        return omit;\n    }\n}));\nfunction omit(object, keys) {\n    const omitted = {};\n    Object.keys(object).forEach((key)=>{\n        if (!keys.includes(key)) {\n            omitted[key] = object[key];\n        }\n    });\n    return omitted;\n} //# sourceMappingURL=omit.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NoYXJlZC9saWIvcm91dGVyL3V0aWxzL29taXQuanMiLCJtYXBwaW5ncyI6Ijs7Ozt3Q0FBZ0JBOzs7ZUFBQUE7OztBQUFULFNBQVNBLEtBQ2RDLE1BQVMsRUFDVEMsSUFBUztJQUVULE1BQU1DLFVBQXNDLENBQUM7SUFDN0NDLE9BQU9GLElBQUksQ0FBQ0QsUUFBUUksT0FBTyxDQUFDLENBQUNDO1FBQzNCLElBQUksQ0FBQ0osS0FBS0ssUUFBUSxDQUFDRCxNQUFXO1lBQzVCSCxPQUFPLENBQUNHLElBQUksR0FBR0wsTUFBTSxDQUFDSyxJQUFJO1FBQzVCO0lBQ0Y7SUFDQSxPQUFPSDtBQUNUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi8uLi8uLi8uLi9zcmMvc2hhcmVkL2xpYi9yb3V0ZXIvdXRpbHMvb21pdC50cz9iN2E4Il0sIm5hbWVzIjpbIm9taXQiLCJvYmplY3QiLCJrZXlzIiwib21pdHRlZCIsIk9iamVjdCIsImZvckVhY2giLCJrZXkiLCJpbmNsdWRlcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/next/dist/shared/lib/router/utils/omit.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/next/dist/shared/lib/router/utils/querystring.js":
/*!************************************************************************!*\
  !*** ../node_modules/next/dist/shared/lib/router/utils/querystring.js ***!
  \************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    assign: function() {\n        return assign;\n    },\n    searchParamsToUrlQuery: function() {\n        return searchParamsToUrlQuery;\n    },\n    urlQueryToSearchParams: function() {\n        return urlQueryToSearchParams;\n    }\n});\nfunction searchParamsToUrlQuery(searchParams) {\n    const query = {};\n    searchParams.forEach((value, key)=>{\n        if (typeof query[key] === \"undefined\") {\n            query[key] = value;\n        } else if (Array.isArray(query[key])) {\n            query[key].push(value);\n        } else {\n            query[key] = [\n                query[key],\n                value\n            ];\n        }\n    });\n    return query;\n}\nfunction stringifyUrlQueryParam(param) {\n    if (typeof param === \"string\" || typeof param === \"number\" && !isNaN(param) || typeof param === \"boolean\") {\n        return String(param);\n    } else {\n        return \"\";\n    }\n}\nfunction urlQueryToSearchParams(urlQuery) {\n    const result = new URLSearchParams();\n    Object.entries(urlQuery).forEach((param)=>{\n        let [key, value] = param;\n        if (Array.isArray(value)) {\n            value.forEach((item)=>result.append(key, stringifyUrlQueryParam(item)));\n        } else {\n            result.set(key, stringifyUrlQueryParam(value));\n        }\n    });\n    return result;\n}\nfunction assign(target) {\n    for(var _len = arguments.length, searchParamsList = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){\n        searchParamsList[_key - 1] = arguments[_key];\n    }\n    searchParamsList.forEach((searchParams)=>{\n        Array.from(searchParams.keys()).forEach((key)=>target.delete(key));\n        searchParams.forEach((value, key)=>target.append(key, value));\n    });\n    return target;\n} //# sourceMappingURL=querystring.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/next/dist/shared/lib/router/utils/querystring.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/next/dist/shared/lib/router/utils/route-matcher.js":
/*!**************************************************************************!*\
  !*** ../node_modules/next/dist/shared/lib/router/utils/route-matcher.js ***!
  \**************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"getRouteMatcher\", ({\n    enumerable: true,\n    get: function() {\n        return getRouteMatcher;\n    }\n}));\nconst _utils = __webpack_require__(/*! ../../utils */ \"(app-pages-browser)/../node_modules/next/dist/shared/lib/utils.js\");\nfunction getRouteMatcher(param) {\n    let { re, groups } = param;\n    return (pathname)=>{\n        const routeMatch = re.exec(pathname);\n        if (!routeMatch) {\n            return false;\n        }\n        const decode = (param)=>{\n            try {\n                return decodeURIComponent(param);\n            } catch (_) {\n                throw new _utils.DecodeError(\"failed to decode param\");\n            }\n        };\n        const params = {};\n        Object.keys(groups).forEach((slugName)=>{\n            const g = groups[slugName];\n            const m = routeMatch[g.pos];\n            if (m !== undefined) {\n                params[slugName] = ~m.indexOf(\"/\") ? m.split(\"/\").map((entry)=>decode(entry)) : g.repeat ? [\n                    decode(m)\n                ] : decode(m);\n            }\n        });\n        return params;\n    };\n} //# sourceMappingURL=route-matcher.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NoYXJlZC9saWIvcm91dGVyL3V0aWxzL3JvdXRlLW1hdGNoZXIuanMiLCJtYXBwaW5ncyI6Ijs7OzttREFXZ0JBOzs7ZUFBQUE7OzttQ0FWWTtBQVVyQixTQUFTQSxnQkFBZ0JDLEtBQTBCO0lBQTFCLE1BQUVDLEVBQUUsRUFBRUMsTUFBTSxFQUFjLEdBQTFCRjtJQUM5QixPQUFPLENBQUNHO1FBQ04sTUFBTUMsYUFBYUgsR0FBR0ksSUFBSSxDQUFDRjtRQUMzQixJQUFJLENBQUNDLFlBQVk7WUFDZixPQUFPO1FBQ1Q7UUFFQSxNQUFNRSxTQUFTLENBQUNOO1lBQ2QsSUFBSTtnQkFDRixPQUFPTyxtQkFBbUJQO1lBQzVCLEVBQUUsT0FBT1EsR0FBRztnQkFDVixNQUFNLElBQUlDLE9BQUFBLFdBQVcsQ0FBQztZQUN4QjtRQUNGO1FBQ0EsTUFBTUMsU0FBcUQsQ0FBQztRQUU1REMsT0FBT0MsSUFBSSxDQUFDVixRQUFRVyxPQUFPLENBQUMsQ0FBQ0M7WUFDM0IsTUFBTUMsSUFBSWIsTUFBTSxDQUFDWSxTQUFTO1lBQzFCLE1BQU1FLElBQUlaLFVBQVUsQ0FBQ1csRUFBRUUsR0FBRyxDQUFDO1lBQzNCLElBQUlELE1BQU1FLFdBQVc7Z0JBQ25CUixNQUFNLENBQUNJLFNBQVMsR0FBRyxDQUFDRSxFQUFFRyxPQUFPLENBQUMsT0FDMUJILEVBQUVJLEtBQUssQ0FBQyxLQUFLQyxHQUFHLENBQUMsQ0FBQ0MsUUFBVWhCLE9BQU9nQixVQUNuQ1AsRUFBRVEsTUFBTSxHQUNSO29CQUFDakIsT0FBT1U7aUJBQUcsR0FDWFYsT0FBT1U7WUFDYjtRQUNGO1FBQ0EsT0FBT047SUFDVDtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi8uLi8uLi8uLi9zcmMvc2hhcmVkL2xpYi9yb3V0ZXIvdXRpbHMvcm91dGUtbWF0Y2hlci50cz8zMTc5Il0sIm5hbWVzIjpbImdldFJvdXRlTWF0Y2hlciIsInBhcmFtIiwicmUiLCJncm91cHMiLCJwYXRobmFtZSIsInJvdXRlTWF0Y2giLCJleGVjIiwiZGVjb2RlIiwiZGVjb2RlVVJJQ29tcG9uZW50IiwiXyIsIkRlY29kZUVycm9yIiwicGFyYW1zIiwiT2JqZWN0Iiwia2V5cyIsImZvckVhY2giLCJzbHVnTmFtZSIsImciLCJtIiwicG9zIiwidW5kZWZpbmVkIiwiaW5kZXhPZiIsInNwbGl0IiwibWFwIiwiZW50cnkiLCJyZXBlYXQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/next/dist/shared/lib/router/utils/route-matcher.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/next/dist/shared/lib/router/utils/route-regex.js":
/*!************************************************************************!*\
  !*** ../node_modules/next/dist/shared/lib/router/utils/route-regex.js ***!
  \************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    getNamedMiddlewareRegex: function() {\n        return getNamedMiddlewareRegex;\n    },\n    getNamedRouteRegex: function() {\n        return getNamedRouteRegex;\n    },\n    getRouteRegex: function() {\n        return getRouteRegex;\n    },\n    parseParameter: function() {\n        return parseParameter;\n    }\n});\nconst _constants = __webpack_require__(/*! ../../../../lib/constants */ \"(app-pages-browser)/../node_modules/next/dist/lib/constants.js\");\nconst _interceptionroutes = __webpack_require__(/*! ../../../../server/future/helpers/interception-routes */ \"(app-pages-browser)/../node_modules/next/dist/server/future/helpers/interception-routes.js\");\nconst _escaperegexp = __webpack_require__(/*! ../../escape-regexp */ \"(app-pages-browser)/../node_modules/next/dist/shared/lib/escape-regexp.js\");\nconst _removetrailingslash = __webpack_require__(/*! ./remove-trailing-slash */ \"(app-pages-browser)/../node_modules/next/dist/shared/lib/router/utils/remove-trailing-slash.js\");\nfunction parseParameter(param) {\n    const optional = param.startsWith(\"[\") && param.endsWith(\"]\");\n    if (optional) {\n        param = param.slice(1, -1);\n    }\n    const repeat = param.startsWith(\"...\");\n    if (repeat) {\n        param = param.slice(3);\n    }\n    return {\n        key: param,\n        repeat,\n        optional\n    };\n}\nfunction getParametrizedRoute(route) {\n    const segments = (0, _removetrailingslash.removeTrailingSlash)(route).slice(1).split(\"/\");\n    const groups = {};\n    let groupIndex = 1;\n    return {\n        parameterizedRoute: segments.map((segment)=>{\n            const markerMatch = _interceptionroutes.INTERCEPTION_ROUTE_MARKERS.find((m)=>segment.startsWith(m));\n            const paramMatches = segment.match(/\\[((?:\\[.*\\])|.+)\\]/) // Check for parameters\n            ;\n            if (markerMatch && paramMatches) {\n                const { key, optional, repeat } = parseParameter(paramMatches[1]);\n                groups[key] = {\n                    pos: groupIndex++,\n                    repeat,\n                    optional\n                };\n                return \"/\" + (0, _escaperegexp.escapeStringRegexp)(markerMatch) + \"([^/]+?)\";\n            } else if (paramMatches) {\n                const { key, repeat, optional } = parseParameter(paramMatches[1]);\n                groups[key] = {\n                    pos: groupIndex++,\n                    repeat,\n                    optional\n                };\n                return repeat ? optional ? \"(?:/(.+?))?\" : \"/(.+?)\" : \"/([^/]+?)\";\n            } else {\n                return \"/\" + (0, _escaperegexp.escapeStringRegexp)(segment);\n            }\n        }).join(\"\"),\n        groups\n    };\n}\nfunction getRouteRegex(normalizedRoute) {\n    const { parameterizedRoute, groups } = getParametrizedRoute(normalizedRoute);\n    return {\n        re: new RegExp(\"^\" + parameterizedRoute + \"(?:/)?$\"),\n        groups: groups\n    };\n}\n/**\n * Builds a function to generate a minimal routeKey using only a-z and minimal\n * number of characters.\n */ function buildGetSafeRouteKey() {\n    let i = 0;\n    return ()=>{\n        let routeKey = \"\";\n        let j = ++i;\n        while(j > 0){\n            routeKey += String.fromCharCode(97 + (j - 1) % 26);\n            j = Math.floor((j - 1) / 26);\n        }\n        return routeKey;\n    };\n}\nfunction getSafeKeyFromSegment(param) {\n    let { interceptionMarker, getSafeRouteKey, segment, routeKeys, keyPrefix } = param;\n    const { key, optional, repeat } = parseParameter(segment);\n    // replace any non-word characters since they can break\n    // the named regex\n    let cleanedKey = key.replace(/\\W/g, \"\");\n    if (keyPrefix) {\n        cleanedKey = \"\" + keyPrefix + cleanedKey;\n    }\n    let invalidKey = false;\n    // check if the key is still invalid and fallback to using a known\n    // safe key\n    if (cleanedKey.length === 0 || cleanedKey.length > 30) {\n        invalidKey = true;\n    }\n    if (!isNaN(parseInt(cleanedKey.slice(0, 1)))) {\n        invalidKey = true;\n    }\n    if (invalidKey) {\n        cleanedKey = getSafeRouteKey();\n    }\n    if (keyPrefix) {\n        routeKeys[cleanedKey] = \"\" + keyPrefix + key;\n    } else {\n        routeKeys[cleanedKey] = key;\n    }\n    // if the segment has an interception marker, make sure that's part of the regex pattern\n    // this is to ensure that the route with the interception marker doesn't incorrectly match\n    // the non-intercepted route (ie /app/(.)[username] should not match /app/[username])\n    const interceptionPrefix = interceptionMarker ? (0, _escaperegexp.escapeStringRegexp)(interceptionMarker) : \"\";\n    return repeat ? optional ? \"(?:/\" + interceptionPrefix + \"(?<\" + cleanedKey + \">.+?))?\" : \"/\" + interceptionPrefix + \"(?<\" + cleanedKey + \">.+?)\" : \"/\" + interceptionPrefix + \"(?<\" + cleanedKey + \">[^/]+?)\";\n}\nfunction getNamedParametrizedRoute(route, prefixRouteKeys) {\n    const segments = (0, _removetrailingslash.removeTrailingSlash)(route).slice(1).split(\"/\");\n    const getSafeRouteKey = buildGetSafeRouteKey();\n    const routeKeys = {};\n    return {\n        namedParameterizedRoute: segments.map((segment)=>{\n            const hasInterceptionMarker = _interceptionroutes.INTERCEPTION_ROUTE_MARKERS.some((m)=>segment.startsWith(m));\n            const paramMatches = segment.match(/\\[((?:\\[.*\\])|.+)\\]/) // Check for parameters\n            ;\n            if (hasInterceptionMarker && paramMatches) {\n                const [usedMarker] = segment.split(paramMatches[0]);\n                return getSafeKeyFromSegment({\n                    getSafeRouteKey,\n                    interceptionMarker: usedMarker,\n                    segment: paramMatches[1],\n                    routeKeys,\n                    keyPrefix: prefixRouteKeys ? _constants.NEXT_INTERCEPTION_MARKER_PREFIX : undefined\n                });\n            } else if (paramMatches) {\n                return getSafeKeyFromSegment({\n                    getSafeRouteKey,\n                    segment: paramMatches[1],\n                    routeKeys,\n                    keyPrefix: prefixRouteKeys ? _constants.NEXT_QUERY_PARAM_PREFIX : undefined\n                });\n            } else {\n                return \"/\" + (0, _escaperegexp.escapeStringRegexp)(segment);\n            }\n        }).join(\"\"),\n        routeKeys\n    };\n}\nfunction getNamedRouteRegex(normalizedRoute, prefixRouteKey) {\n    const result = getNamedParametrizedRoute(normalizedRoute, prefixRouteKey);\n    return {\n        ...getRouteRegex(normalizedRoute),\n        namedRegex: \"^\" + result.namedParameterizedRoute + \"(?:/)?$\",\n        routeKeys: result.routeKeys\n    };\n}\nfunction getNamedMiddlewareRegex(normalizedRoute, options) {\n    const { parameterizedRoute } = getParametrizedRoute(normalizedRoute);\n    const { catchAll = true } = options;\n    if (parameterizedRoute === \"/\") {\n        let catchAllRegex = catchAll ? \".*\" : \"\";\n        return {\n            namedRegex: \"^/\" + catchAllRegex + \"$\"\n        };\n    }\n    const { namedParameterizedRoute } = getNamedParametrizedRoute(normalizedRoute, false);\n    let catchAllGroupedRegex = catchAll ? \"(?:(/.*)?)\" : \"\";\n    return {\n        namedRegex: \"^\" + namedParameterizedRoute + catchAllGroupedRegex + \"$\"\n    };\n} //# sourceMappingURL=route-regex.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/next/dist/shared/lib/router/utils/route-regex.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/next/dist/shared/lib/router/utils/sorted-routes.js":
/*!**************************************************************************!*\
  !*** ../node_modules/next/dist/shared/lib/router/utils/sorted-routes.js ***!
  \**************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"getSortedRoutes\", ({\n    enumerable: true,\n    get: function() {\n        return getSortedRoutes;\n    }\n}));\nclass UrlNode {\n    insert(urlPath) {\n        this._insert(urlPath.split(\"/\").filter(Boolean), [], false);\n    }\n    smoosh() {\n        return this._smoosh();\n    }\n    _smoosh(prefix) {\n        if (prefix === void 0) prefix = \"/\";\n        const childrenPaths = [\n            ...this.children.keys()\n        ].sort();\n        if (this.slugName !== null) {\n            childrenPaths.splice(childrenPaths.indexOf(\"[]\"), 1);\n        }\n        if (this.restSlugName !== null) {\n            childrenPaths.splice(childrenPaths.indexOf(\"[...]\"), 1);\n        }\n        if (this.optionalRestSlugName !== null) {\n            childrenPaths.splice(childrenPaths.indexOf(\"[[...]]\"), 1);\n        }\n        const routes = childrenPaths.map((c)=>this.children.get(c)._smoosh(\"\" + prefix + c + \"/\")).reduce((prev, curr)=>[\n                ...prev,\n                ...curr\n            ], []);\n        if (this.slugName !== null) {\n            routes.push(...this.children.get(\"[]\")._smoosh(prefix + \"[\" + this.slugName + \"]/\"));\n        }\n        if (!this.placeholder) {\n            const r = prefix === \"/\" ? \"/\" : prefix.slice(0, -1);\n            if (this.optionalRestSlugName != null) {\n                throw new Error('You cannot define a route with the same specificity as a optional catch-all route (\"' + r + '\" and \"' + r + \"[[...\" + this.optionalRestSlugName + ']]\").');\n            }\n            routes.unshift(r);\n        }\n        if (this.restSlugName !== null) {\n            routes.push(...this.children.get(\"[...]\")._smoosh(prefix + \"[...\" + this.restSlugName + \"]/\"));\n        }\n        if (this.optionalRestSlugName !== null) {\n            routes.push(...this.children.get(\"[[...]]\")._smoosh(prefix + \"[[...\" + this.optionalRestSlugName + \"]]/\"));\n        }\n        return routes;\n    }\n    _insert(urlPaths, slugNames, isCatchAll) {\n        if (urlPaths.length === 0) {\n            this.placeholder = false;\n            return;\n        }\n        if (isCatchAll) {\n            throw new Error(\"Catch-all must be the last part of the URL.\");\n        }\n        // The next segment in the urlPaths list\n        let nextSegment = urlPaths[0];\n        // Check if the segment matches `[something]`\n        if (nextSegment.startsWith(\"[\") && nextSegment.endsWith(\"]\")) {\n            // Strip `[` and `]`, leaving only `something`\n            let segmentName = nextSegment.slice(1, -1);\n            let isOptional = false;\n            if (segmentName.startsWith(\"[\") && segmentName.endsWith(\"]\")) {\n                // Strip optional `[` and `]`, leaving only `something`\n                segmentName = segmentName.slice(1, -1);\n                isOptional = true;\n            }\n            if (segmentName.startsWith(\"...\")) {\n                // Strip `...`, leaving only `something`\n                segmentName = segmentName.substring(3);\n                isCatchAll = true;\n            }\n            if (segmentName.startsWith(\"[\") || segmentName.endsWith(\"]\")) {\n                throw new Error(\"Segment names may not start or end with extra brackets ('\" + segmentName + \"').\");\n            }\n            if (segmentName.startsWith(\".\")) {\n                throw new Error(\"Segment names may not start with erroneous periods ('\" + segmentName + \"').\");\n            }\n            function handleSlug(previousSlug, nextSlug) {\n                if (previousSlug !== null) {\n                    // If the specific segment already has a slug but the slug is not `something`\n                    // This prevents collisions like:\n                    // pages/[post]/index.js\n                    // pages/[id]/index.js\n                    // Because currently multiple dynamic params on the same segment level are not supported\n                    if (previousSlug !== nextSlug) {\n                        // TODO: This error seems to be confusing for users, needs an error link, the description can be based on above comment.\n                        throw new Error(\"You cannot use different slug names for the same dynamic path ('\" + previousSlug + \"' !== '\" + nextSlug + \"').\");\n                    }\n                }\n                slugNames.forEach((slug)=>{\n                    if (slug === nextSlug) {\n                        throw new Error('You cannot have the same slug name \"' + nextSlug + '\" repeat within a single dynamic path');\n                    }\n                    if (slug.replace(/\\W/g, \"\") === nextSegment.replace(/\\W/g, \"\")) {\n                        throw new Error('You cannot have the slug names \"' + slug + '\" and \"' + nextSlug + '\" differ only by non-word symbols within a single dynamic path');\n                    }\n                });\n                slugNames.push(nextSlug);\n            }\n            if (isCatchAll) {\n                if (isOptional) {\n                    if (this.restSlugName != null) {\n                        throw new Error('You cannot use both an required and optional catch-all route at the same level (\"[...' + this.restSlugName + ']\" and \"' + urlPaths[0] + '\" ).');\n                    }\n                    handleSlug(this.optionalRestSlugName, segmentName);\n                    // slugName is kept as it can only be one particular slugName\n                    this.optionalRestSlugName = segmentName;\n                    // nextSegment is overwritten to [[...]] so that it can later be sorted specifically\n                    nextSegment = \"[[...]]\";\n                } else {\n                    if (this.optionalRestSlugName != null) {\n                        throw new Error('You cannot use both an optional and required catch-all route at the same level (\"[[...' + this.optionalRestSlugName + ']]\" and \"' + urlPaths[0] + '\").');\n                    }\n                    handleSlug(this.restSlugName, segmentName);\n                    // slugName is kept as it can only be one particular slugName\n                    this.restSlugName = segmentName;\n                    // nextSegment is overwritten to [...] so that it can later be sorted specifically\n                    nextSegment = \"[...]\";\n                }\n            } else {\n                if (isOptional) {\n                    throw new Error('Optional route parameters are not yet supported (\"' + urlPaths[0] + '\").');\n                }\n                handleSlug(this.slugName, segmentName);\n                // slugName is kept as it can only be one particular slugName\n                this.slugName = segmentName;\n                // nextSegment is overwritten to [] so that it can later be sorted specifically\n                nextSegment = \"[]\";\n            }\n        }\n        // If this UrlNode doesn't have the nextSegment yet we create a new child UrlNode\n        if (!this.children.has(nextSegment)) {\n            this.children.set(nextSegment, new UrlNode());\n        }\n        this.children.get(nextSegment)._insert(urlPaths.slice(1), slugNames, isCatchAll);\n    }\n    constructor(){\n        this.placeholder = true;\n        this.children = new Map();\n        this.slugName = null;\n        this.restSlugName = null;\n        this.optionalRestSlugName = null;\n    }\n}\nfunction getSortedRoutes(normalizedPages) {\n    // First the UrlNode is created, and every UrlNode can have only 1 dynamic segment\n    // Eg you can't have pages/[post]/abc.js and pages/[hello]/something-else.js\n    // Only 1 dynamic segment per nesting level\n    // So in the case that is test/integration/dynamic-routing it'll be this:\n    // pages/[post]/comments.js\n    // pages/blog/[post]/comment/[id].js\n    // Both are fine because `pages/[post]` and `pages/blog` are on the same level\n    // So in this case `UrlNode` created here has `this.slugName === 'post'`\n    // And since your PR passed through `slugName` as an array basically it'd including it in too many possibilities\n    // Instead what has to be passed through is the upwards path's dynamic names\n    const root = new UrlNode();\n    // Here the `root` gets injected multiple paths, and insert will break them up into sublevels\n    normalizedPages.forEach((pagePath)=>root.insert(pagePath));\n    // Smoosh will then sort those sublevels up to the point where you get the correct route definition priority\n    return root.smoosh();\n} //# sourceMappingURL=sorted-routes.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/next/dist/shared/lib/router/utils/sorted-routes.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/next/dist/shared/lib/utils.js":
/*!*****************************************************!*\
  !*** ../node_modules/next/dist/shared/lib/utils.js ***!
  \*****************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    DecodeError: function() {\n        return DecodeError;\n    },\n    MiddlewareNotFoundError: function() {\n        return MiddlewareNotFoundError;\n    },\n    MissingStaticPage: function() {\n        return MissingStaticPage;\n    },\n    NormalizeError: function() {\n        return NormalizeError;\n    },\n    PageNotFoundError: function() {\n        return PageNotFoundError;\n    },\n    SP: function() {\n        return SP;\n    },\n    ST: function() {\n        return ST;\n    },\n    WEB_VITALS: function() {\n        return WEB_VITALS;\n    },\n    execOnce: function() {\n        return execOnce;\n    },\n    getDisplayName: function() {\n        return getDisplayName;\n    },\n    getLocationOrigin: function() {\n        return getLocationOrigin;\n    },\n    getURL: function() {\n        return getURL;\n    },\n    isAbsoluteUrl: function() {\n        return isAbsoluteUrl;\n    },\n    isResSent: function() {\n        return isResSent;\n    },\n    loadGetInitialProps: function() {\n        return loadGetInitialProps;\n    },\n    normalizeRepeatedSlashes: function() {\n        return normalizeRepeatedSlashes;\n    },\n    stringifyError: function() {\n        return stringifyError;\n    }\n});\nconst WEB_VITALS = [\n    \"CLS\",\n    \"FCP\",\n    \"FID\",\n    \"INP\",\n    \"LCP\",\n    \"TTFB\"\n];\nfunction execOnce(fn) {\n    let used = false;\n    let result;\n    return function() {\n        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n            args[_key] = arguments[_key];\n        }\n        if (!used) {\n            used = true;\n            result = fn(...args);\n        }\n        return result;\n    };\n}\n// Scheme: https://tools.ietf.org/html/rfc3986#section-3.1\n// Absolute URL: https://tools.ietf.org/html/rfc3986#section-4.3\nconst ABSOLUTE_URL_REGEX = /^[a-zA-Z][a-zA-Z\\d+\\-.]*?:/;\nconst isAbsoluteUrl = (url)=>ABSOLUTE_URL_REGEX.test(url);\nfunction getLocationOrigin() {\n    const { protocol, hostname, port } = window.location;\n    return protocol + \"//\" + hostname + (port ? \":\" + port : \"\");\n}\nfunction getURL() {\n    const { href } = window.location;\n    const origin = getLocationOrigin();\n    return href.substring(origin.length);\n}\nfunction getDisplayName(Component) {\n    return typeof Component === \"string\" ? Component : Component.displayName || Component.name || \"Unknown\";\n}\nfunction isResSent(res) {\n    return res.finished || res.headersSent;\n}\nfunction normalizeRepeatedSlashes(url) {\n    const urlParts = url.split(\"?\");\n    const urlNoQuery = urlParts[0];\n    return urlNoQuery // first we replace any non-encoded backslashes with forward\n    // then normalize repeated forward slashes\n    .replace(/\\\\/g, \"/\").replace(/\\/\\/+/g, \"/\") + (urlParts[1] ? \"?\" + urlParts.slice(1).join(\"?\") : \"\");\n}\nasync function loadGetInitialProps(App, ctx) {\n    if (true) {\n        var _App_prototype;\n        if ((_App_prototype = App.prototype) == null ? void 0 : _App_prototype.getInitialProps) {\n            const message = '\"' + getDisplayName(App) + '.getInitialProps()\" is defined as an instance method - visit https://nextjs.org/docs/messages/get-initial-props-as-an-instance-method for more information.';\n            throw new Error(message);\n        }\n    }\n    // when called from _app `ctx` is nested in `ctx`\n    const res = ctx.res || ctx.ctx && ctx.ctx.res;\n    if (!App.getInitialProps) {\n        if (ctx.ctx && ctx.Component) {\n            // @ts-ignore pageProps default\n            return {\n                pageProps: await loadGetInitialProps(ctx.Component, ctx.ctx)\n            };\n        }\n        return {};\n    }\n    const props = await App.getInitialProps(ctx);\n    if (res && isResSent(res)) {\n        return props;\n    }\n    if (!props) {\n        const message = '\"' + getDisplayName(App) + '.getInitialProps()\" should resolve to an object. But found \"' + props + '\" instead.';\n        throw new Error(message);\n    }\n    if (true) {\n        if (Object.keys(props).length === 0 && !ctx.ctx) {\n            console.warn(\"\" + getDisplayName(App) + \" returned an empty object from `getInitialProps`. This de-optimizes and prevents automatic static optimization. https://nextjs.org/docs/messages/empty-object-getInitialProps\");\n        }\n    }\n    return props;\n}\nconst SP = typeof performance !== \"undefined\";\nconst ST = SP && [\n    \"mark\",\n    \"measure\",\n    \"getEntriesByName\"\n].every((method)=>typeof performance[method] === \"function\");\nclass DecodeError extends Error {\n}\nclass NormalizeError extends Error {\n}\nclass PageNotFoundError extends Error {\n    constructor(page){\n        super();\n        this.code = \"ENOENT\";\n        this.name = \"PageNotFoundError\";\n        this.message = \"Cannot find module for page: \" + page;\n    }\n}\nclass MissingStaticPage extends Error {\n    constructor(page, message){\n        super();\n        this.message = \"Failed to load static file for page: \" + page + \" \" + message;\n    }\n}\nclass MiddlewareNotFoundError extends Error {\n    constructor(){\n        super();\n        this.code = \"ENOENT\";\n        this.message = \"Cannot find the middleware module\";\n    }\n}\nfunction stringifyError(error) {\n    return JSON.stringify({\n        message: error.message,\n        stack: error.stack\n    });\n} //# sourceMappingURL=utils.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/next/dist/shared/lib/utils.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!*****************************************************************************************!*\
  !*** ../node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \*****************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n\nif (true) {\n  (function() {\n'use strict';\n\nvar React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/index.js\");\n\n// ATTENTION\n// When adding new symbols to this file,\n// Please consider also adding to 'react-devtools-shared/src/backend/ReactSymbols'\n// The Symbol used to tag the ReactElement-like types.\nvar REACT_ELEMENT_TYPE = Symbol.for('react.element');\nvar REACT_PORTAL_TYPE = Symbol.for('react.portal');\nvar REACT_FRAGMENT_TYPE = Symbol.for('react.fragment');\nvar REACT_STRICT_MODE_TYPE = Symbol.for('react.strict_mode');\nvar REACT_PROFILER_TYPE = Symbol.for('react.profiler');\nvar REACT_PROVIDER_TYPE = Symbol.for('react.provider'); // TODO: Delete with enableRenderableContext\n\nvar REACT_CONSUMER_TYPE = Symbol.for('react.consumer');\nvar REACT_CONTEXT_TYPE = Symbol.for('react.context');\nvar REACT_FORWARD_REF_TYPE = Symbol.for('react.forward_ref');\nvar REACT_SUSPENSE_TYPE = Symbol.for('react.suspense');\nvar REACT_SUSPENSE_LIST_TYPE = Symbol.for('react.suspense_list');\nvar REACT_MEMO_TYPE = Symbol.for('react.memo');\nvar REACT_LAZY_TYPE = Symbol.for('react.lazy');\nvar REACT_OFFSCREEN_TYPE = Symbol.for('react.offscreen');\nvar REACT_CACHE_TYPE = Symbol.for('react.cache');\nvar MAYBE_ITERATOR_SYMBOL = Symbol.iterator;\nvar FAUX_ITERATOR_SYMBOL = '@@iterator';\nfunction getIteratorFn(maybeIterable) {\n  if (maybeIterable === null || typeof maybeIterable !== 'object') {\n    return null;\n  }\n\n  var maybeIterator = MAYBE_ITERATOR_SYMBOL && maybeIterable[MAYBE_ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL];\n\n  if (typeof maybeIterator === 'function') {\n    return maybeIterator;\n  }\n\n  return null;\n}\n\nvar ReactSharedInternals = React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n\nfunction error(format) {\n  {\n    {\n      for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n        args[_key2 - 1] = arguments[_key2];\n      }\n\n      printWarning('error', format, args);\n    }\n  }\n}\n\nfunction printWarning(level, format, args) {\n  // When changing this logic, you might want to also\n  // update consoleWithStackDev.www.js as well.\n  {\n    var ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n    var stack = ReactDebugCurrentFrame.getStackAddendum();\n\n    if (stack !== '') {\n      format += '%s';\n      args = args.concat([stack]);\n    } // eslint-disable-next-line react-internal/safe-string-coercion\n\n\n    var argsWithFormat = args.map(function (item) {\n      return String(item);\n    }); // Careful: RN currently depends on this prefix\n\n    argsWithFormat.unshift('Warning: ' + format); // We intentionally don't use spread (or .apply) directly because it\n    // breaks IE9: https://github.com/facebook/react/issues/13610\n    // eslint-disable-next-line react-internal/no-production-logging\n\n    Function.prototype.apply.call(console[level], console, argsWithFormat);\n  }\n}\n\n// -----------------------------------------------------------------------------\n\nvar enableScopeAPI = false; // Experimental Create Event Handle API.\nvar enableCacheElement = false;\nvar enableTransitionTracing = false; // No known bugs, but needs performance testing\n\nvar enableLegacyHidden = false; // Enables unstable_avoidThisFallback feature in Fiber\nvar enableRenderableContext = false;\n// stuff. Intended to enable React core members to more easily debug scheduling\n// issues in DEV builds.\n\nvar enableDebugTracing = false;\n\nfunction getWrappedName(outerType, innerType, wrapperName) {\n  var displayName = outerType.displayName;\n\n  if (displayName) {\n    return displayName;\n  }\n\n  var functionName = innerType.displayName || innerType.name || '';\n  return functionName !== '' ? wrapperName + \"(\" + functionName + \")\" : wrapperName;\n} // Keep in sync with react-reconciler/getComponentNameFromFiber\n\n\nfunction getContextName(type) {\n  return type.displayName || 'Context';\n}\n\nvar REACT_CLIENT_REFERENCE$2 = Symbol.for('react.client.reference'); // Note that the reconciler package should generally prefer to use getComponentNameFromFiber() instead.\n\nfunction getComponentNameFromType(type) {\n  if (type == null) {\n    // Host root, text node or just invalid type.\n    return null;\n  }\n\n  if (typeof type === 'function') {\n    if (type.$$typeof === REACT_CLIENT_REFERENCE$2) {\n      // TODO: Create a convention for naming client references with debug info.\n      return null;\n    }\n\n    return type.displayName || type.name || null;\n  }\n\n  if (typeof type === 'string') {\n    return type;\n  }\n\n  switch (type) {\n    case REACT_FRAGMENT_TYPE:\n      return 'Fragment';\n\n    case REACT_PORTAL_TYPE:\n      return 'Portal';\n\n    case REACT_PROFILER_TYPE:\n      return 'Profiler';\n\n    case REACT_STRICT_MODE_TYPE:\n      return 'StrictMode';\n\n    case REACT_SUSPENSE_TYPE:\n      return 'Suspense';\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return 'SuspenseList';\n\n    case REACT_CACHE_TYPE:\n      {\n        return 'Cache';\n      }\n\n  }\n\n  if (typeof type === 'object') {\n    {\n      if (typeof type.tag === 'number') {\n        error('Received an unexpected object in getComponentNameFromType(). ' + 'This is likely a bug in React. Please file an issue.');\n      }\n    }\n\n    switch (type.$$typeof) {\n      case REACT_PROVIDER_TYPE:\n        {\n          var provider = type;\n          return getContextName(provider._context) + '.Provider';\n        }\n\n      case REACT_CONTEXT_TYPE:\n        var context = type;\n\n        {\n          return getContextName(context) + '.Consumer';\n        }\n\n      case REACT_CONSUMER_TYPE:\n        {\n          return null;\n        }\n\n      case REACT_FORWARD_REF_TYPE:\n        return getWrappedName(type, type.render, 'ForwardRef');\n\n      case REACT_MEMO_TYPE:\n        var outerName = type.displayName || null;\n\n        if (outerName !== null) {\n          return outerName;\n        }\n\n        return getComponentNameFromType(type.type) || 'Memo';\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            return getComponentNameFromType(init(payload));\n          } catch (x) {\n            return null;\n          }\n        }\n    }\n  }\n\n  return null;\n}\n\n// $FlowFixMe[method-unbinding]\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\n\nvar assign = Object.assign;\n\n/*\n * The `'' + value` pattern (used in perf-sensitive code) throws for Symbol\n * and Temporal.* types. See https://github.com/facebook/react/pull/22064.\n *\n * The functions in this module will throw an easier-to-understand,\n * easier-to-debug exception with a clear errors message message explaining the\n * problem. (Instead of a confusing exception thrown inside the implementation\n * of the `value` object).\n */\n// $FlowFixMe[incompatible-return] only called in DEV, so void return is not possible.\nfunction typeName(value) {\n  {\n    // toStringTag is needed for namespaced types like Temporal.Instant\n    var hasToStringTag = typeof Symbol === 'function' && Symbol.toStringTag;\n    var type = hasToStringTag && value[Symbol.toStringTag] || value.constructor.name || 'Object'; // $FlowFixMe[incompatible-return]\n\n    return type;\n  }\n} // $FlowFixMe[incompatible-return] only called in DEV, so void return is not possible.\n\n\nfunction willCoercionThrow(value) {\n  {\n    try {\n      testStringCoercion(value);\n      return false;\n    } catch (e) {\n      return true;\n    }\n  }\n}\n\nfunction testStringCoercion(value) {\n  // If you ended up here by following an exception call stack, here's what's\n  // happened: you supplied an object or symbol value to React (as a prop, key,\n  // DOM attribute, CSS property, string ref, etc.) and when React tried to\n  // coerce it to a string using `'' + value`, an exception was thrown.\n  //\n  // The most common types that will cause this exception are `Symbol` instances\n  // and Temporal objects like `Temporal.Instant`. But any object that has a\n  // `valueOf` or `[Symbol.toPrimitive]` method that throws will also cause this\n  // exception. (Library authors do this to prevent users from using built-in\n  // numeric operators like `+` or comparison operators like `>=` because custom\n  // methods are needed to perform accurate arithmetic or comparison.)\n  //\n  // To fix the problem, coerce this object or symbol value to a string before\n  // passing it to React. The most reliable way is usually `String(value)`.\n  //\n  // To find which value is throwing, check the browser or debugger console.\n  // Before this exception was thrown, there should be `console.error` output\n  // that shows the type (Symbol, Temporal.PlainDate, etc.) that caused the\n  // problem and how that type was used: key, atrribute, input value prop, etc.\n  // In most cases, this console output also shows the component and its\n  // ancestor components where the exception happened.\n  //\n  // eslint-disable-next-line react-internal/safe-string-coercion\n  return '' + value;\n}\nfunction checkKeyStringCoercion(value) {\n  {\n    if (willCoercionThrow(value)) {\n      error('The provided key is an unsupported type %s.' + ' This value must be coerced to a string before using it here.', typeName(value));\n\n      return testStringCoercion(value); // throw (to help callers find troubleshooting comments)\n    }\n  }\n}\n\nvar REACT_CLIENT_REFERENCE$1 = Symbol.for('react.client.reference');\nfunction isValidElementType(type) {\n  if (typeof type === 'string' || typeof type === 'function') {\n    return true;\n  } // Note: typeof might be other than 'symbol' or 'number' (e.g. if it's a polyfill).\n\n\n  if (type === REACT_FRAGMENT_TYPE || type === REACT_PROFILER_TYPE || enableDebugTracing  || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || enableLegacyHidden  || type === REACT_OFFSCREEN_TYPE || enableScopeAPI  || enableCacheElement  || enableTransitionTracing ) {\n    return true;\n  }\n\n  if (typeof type === 'object' && type !== null) {\n    if (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || enableRenderableContext  || type.$$typeof === REACT_FORWARD_REF_TYPE || // This needs to include all possible module reference object\n    // types supported by any Flight configuration anywhere since\n    // we don't know which Flight build this will end up being used\n    // with.\n    type.$$typeof === REACT_CLIENT_REFERENCE$1 || type.getModuleId !== undefined) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nvar isArrayImpl = Array.isArray; // eslint-disable-next-line no-redeclare\n\nfunction isArray(a) {\n  return isArrayImpl(a);\n}\n\n// Helpers to patch console.logs to avoid logging during side-effect free\n// replaying on render function. This currently only patches the object\n// lazily which won't cover if the log function was extracted eagerly.\n// We could also eagerly patch the method.\nvar disabledDepth = 0;\nvar prevLog;\nvar prevInfo;\nvar prevWarn;\nvar prevError;\nvar prevGroup;\nvar prevGroupCollapsed;\nvar prevGroupEnd;\n\nfunction disabledLog() {}\n\ndisabledLog.__reactDisabledLog = true;\nfunction disableLogs() {\n  {\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      prevLog = console.log;\n      prevInfo = console.info;\n      prevWarn = console.warn;\n      prevError = console.error;\n      prevGroup = console.group;\n      prevGroupCollapsed = console.groupCollapsed;\n      prevGroupEnd = console.groupEnd; // https://github.com/facebook/react/issues/19099\n\n      var props = {\n        configurable: true,\n        enumerable: true,\n        value: disabledLog,\n        writable: true\n      }; // $FlowFixMe[cannot-write] Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        info: props,\n        log: props,\n        warn: props,\n        error: props,\n        group: props,\n        groupCollapsed: props,\n        groupEnd: props\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    disabledDepth++;\n  }\n}\nfunction reenableLogs() {\n  {\n    disabledDepth--;\n\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      var props = {\n        configurable: true,\n        enumerable: true,\n        writable: true\n      }; // $FlowFixMe[cannot-write] Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        log: assign({}, props, {\n          value: prevLog\n        }),\n        info: assign({}, props, {\n          value: prevInfo\n        }),\n        warn: assign({}, props, {\n          value: prevWarn\n        }),\n        error: assign({}, props, {\n          value: prevError\n        }),\n        group: assign({}, props, {\n          value: prevGroup\n        }),\n        groupCollapsed: assign({}, props, {\n          value: prevGroupCollapsed\n        }),\n        groupEnd: assign({}, props, {\n          value: prevGroupEnd\n        })\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    if (disabledDepth < 0) {\n      error('disabledDepth fell below zero. ' + 'This is a bug in React. Please file an issue.');\n    }\n  }\n}\n\nvar ReactCurrentDispatcher = ReactSharedInternals.ReactCurrentDispatcher;\nvar prefix;\nfunction describeBuiltInComponentFrame(name, ownerFn) {\n  {\n    if (prefix === undefined) {\n      // Extract the VM specific prefix used by each line.\n      try {\n        throw Error();\n      } catch (x) {\n        var match = x.stack.trim().match(/\\n( *(at )?)/);\n        prefix = match && match[1] || '';\n      }\n    } // We use the prefix to ensure our stacks line up with native stack frames.\n\n\n    return '\\n' + prefix + name;\n  }\n}\nvar reentry = false;\nvar componentFrameCache;\n\n{\n  var PossiblyWeakMap = typeof WeakMap === 'function' ? WeakMap : Map;\n  componentFrameCache = new PossiblyWeakMap();\n}\n/**\n * Leverages native browser/VM stack frames to get proper details (e.g.\n * filename, line + col number) for a single component in a component stack. We\n * do this by:\n *   (1) throwing and catching an error in the function - this will be our\n *       control error.\n *   (2) calling the component which will eventually throw an error that we'll\n *       catch - this will be our sample error.\n *   (3) diffing the control and sample error stacks to find the stack frame\n *       which represents our component.\n */\n\n\nfunction describeNativeComponentFrame(fn, construct) {\n  // If something asked for a stack inside a fake render, it should get ignored.\n  if (!fn || reentry) {\n    return '';\n  }\n\n  {\n    var frame = componentFrameCache.get(fn);\n\n    if (frame !== undefined) {\n      return frame;\n    }\n  }\n\n  reentry = true;\n  var previousPrepareStackTrace = Error.prepareStackTrace; // $FlowFixMe[incompatible-type] It does accept undefined.\n\n  Error.prepareStackTrace = undefined;\n  var previousDispatcher;\n\n  {\n    previousDispatcher = ReactCurrentDispatcher.current; // Set the dispatcher in DEV because this might be call in the render function\n    // for warnings.\n\n    ReactCurrentDispatcher.current = null;\n    disableLogs();\n  }\n  /**\n   * Finding a common stack frame between sample and control errors can be\n   * tricky given the different types and levels of stack trace truncation from\n   * different JS VMs. So instead we'll attempt to control what that common\n   * frame should be through this object method:\n   * Having both the sample and control errors be in the function under the\n   * `DescribeNativeComponentFrameRoot` property, + setting the `name` and\n   * `displayName` properties of the function ensures that a stack\n   * frame exists that has the method name `DescribeNativeComponentFrameRoot` in\n   * it for both control and sample stacks.\n   */\n\n\n  var RunInRootFrame = {\n    DetermineComponentFrameRoot: function () {\n      var control;\n\n      try {\n        // This should throw.\n        if (construct) {\n          // Something should be setting the props in the constructor.\n          var Fake = function () {\n            throw Error();\n          }; // $FlowFixMe[prop-missing]\n\n\n          Object.defineProperty(Fake.prototype, 'props', {\n            set: function () {\n              // We use a throwing setter instead of frozen or non-writable props\n              // because that won't throw in a non-strict mode function.\n              throw Error();\n            }\n          });\n\n          if (typeof Reflect === 'object' && Reflect.construct) {\n            // We construct a different control for this case to include any extra\n            // frames added by the construct call.\n            try {\n              Reflect.construct(Fake, []);\n            } catch (x) {\n              control = x;\n            }\n\n            Reflect.construct(fn, [], Fake);\n          } else {\n            try {\n              Fake.call();\n            } catch (x) {\n              control = x;\n            } // $FlowFixMe[prop-missing] found when upgrading Flow\n\n\n            fn.call(Fake.prototype);\n          }\n        } else {\n          try {\n            throw Error();\n          } catch (x) {\n            control = x;\n          } // TODO(luna): This will currently only throw if the function component\n          // tries to access React/ReactDOM/props. We should probably make this throw\n          // in simple components too\n\n\n          var maybePromise = fn(); // If the function component returns a promise, it's likely an async\n          // component, which we don't yet support. Attach a noop catch handler to\n          // silence the error.\n          // TODO: Implement component stacks for async client components?\n\n          if (maybePromise && typeof maybePromise.catch === 'function') {\n            maybePromise.catch(function () {});\n          }\n        }\n      } catch (sample) {\n        // This is inlined manually because closure doesn't do it for us.\n        if (sample && control && typeof sample.stack === 'string') {\n          return [sample.stack, control.stack];\n        }\n      }\n\n      return [null, null];\n    }\n  }; // $FlowFixMe[prop-missing]\n\n  RunInRootFrame.DetermineComponentFrameRoot.displayName = 'DetermineComponentFrameRoot';\n  var namePropDescriptor = Object.getOwnPropertyDescriptor(RunInRootFrame.DetermineComponentFrameRoot, 'name'); // Before ES6, the `name` property was not configurable.\n\n  if (namePropDescriptor && namePropDescriptor.configurable) {\n    // V8 utilizes a function's `name` property when generating a stack trace.\n    Object.defineProperty(RunInRootFrame.DetermineComponentFrameRoot, // Configurable properties can be updated even if its writable descriptor\n    // is set to `false`.\n    // $FlowFixMe[cannot-write]\n    'name', {\n      value: 'DetermineComponentFrameRoot'\n    });\n  }\n\n  try {\n    var _RunInRootFrame$Deter = RunInRootFrame.DetermineComponentFrameRoot(),\n        sampleStack = _RunInRootFrame$Deter[0],\n        controlStack = _RunInRootFrame$Deter[1];\n\n    if (sampleStack && controlStack) {\n      // This extracts the first frame from the sample that isn't also in the control.\n      // Skipping one frame that we assume is the frame that calls the two.\n      var sampleLines = sampleStack.split('\\n');\n      var controlLines = controlStack.split('\\n');\n      var s = 0;\n      var c = 0;\n\n      while (s < sampleLines.length && !sampleLines[s].includes('DetermineComponentFrameRoot')) {\n        s++;\n      }\n\n      while (c < controlLines.length && !controlLines[c].includes('DetermineComponentFrameRoot')) {\n        c++;\n      } // We couldn't find our intentionally injected common root frame, attempt\n      // to find another common root frame by search from the bottom of the\n      // control stack...\n\n\n      if (s === sampleLines.length || c === controlLines.length) {\n        s = sampleLines.length - 1;\n        c = controlLines.length - 1;\n\n        while (s >= 1 && c >= 0 && sampleLines[s] !== controlLines[c]) {\n          // We expect at least one stack frame to be shared.\n          // Typically this will be the root most one. However, stack frames may be\n          // cut off due to maximum stack limits. In this case, one maybe cut off\n          // earlier than the other. We assume that the sample is longer or the same\n          // and there for cut off earlier. So we should find the root most frame in\n          // the sample somewhere in the control.\n          c--;\n        }\n      }\n\n      for (; s >= 1 && c >= 0; s--, c--) {\n        // Next we find the first one that isn't the same which should be the\n        // frame that called our sample function and the control.\n        if (sampleLines[s] !== controlLines[c]) {\n          // In V8, the first line is describing the message but other VMs don't.\n          // If we're about to return the first line, and the control is also on the same\n          // line, that's a pretty good indicator that our sample threw at same line as\n          // the control. I.e. before we entered the sample frame. So we ignore this result.\n          // This can happen if you passed a class to function component, or non-function.\n          if (s !== 1 || c !== 1) {\n            do {\n              s--;\n              c--; // We may still have similar intermediate frames from the construct call.\n              // The next one that isn't the same should be our match though.\n\n              if (c < 0 || sampleLines[s] !== controlLines[c]) {\n                // V8 adds a \"new\" prefix for native classes. Let's remove it to make it prettier.\n                var _frame = '\\n' + sampleLines[s].replace(' at new ', ' at '); // If our component frame is labeled \"<anonymous>\"\n                // but we have a user-provided \"displayName\"\n                // splice it in to make the stack more readable.\n\n\n                if (fn.displayName && _frame.includes('<anonymous>')) {\n                  _frame = _frame.replace('<anonymous>', fn.displayName);\n                }\n\n                if (true) {\n                  if (typeof fn === 'function') {\n                    componentFrameCache.set(fn, _frame);\n                  }\n                } // Return the line we found.\n\n\n                return _frame;\n              }\n            } while (s >= 1 && c >= 0);\n          }\n\n          break;\n        }\n      }\n    }\n  } finally {\n    reentry = false;\n\n    {\n      ReactCurrentDispatcher.current = previousDispatcher;\n      reenableLogs();\n    }\n\n    Error.prepareStackTrace = previousPrepareStackTrace;\n  } // Fallback to just using the name if we couldn't make it throw.\n\n\n  var name = fn ? fn.displayName || fn.name : '';\n  var syntheticFrame = name ? describeBuiltInComponentFrame(name) : '';\n\n  {\n    if (typeof fn === 'function') {\n      componentFrameCache.set(fn, syntheticFrame);\n    }\n  }\n\n  return syntheticFrame;\n}\nfunction describeFunctionComponentFrame(fn, ownerFn) {\n  {\n    return describeNativeComponentFrame(fn, false);\n  }\n}\n\nfunction shouldConstruct(Component) {\n  var prototype = Component.prototype;\n  return !!(prototype && prototype.isReactComponent);\n}\n\nfunction describeUnknownElementTypeFrameInDEV(type, ownerFn) {\n\n  if (type == null) {\n    return '';\n  }\n\n  if (typeof type === 'function') {\n    {\n      return describeNativeComponentFrame(type, shouldConstruct(type));\n    }\n  }\n\n  if (typeof type === 'string') {\n    return describeBuiltInComponentFrame(type);\n  }\n\n  switch (type) {\n    case REACT_SUSPENSE_TYPE:\n      return describeBuiltInComponentFrame('Suspense');\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return describeBuiltInComponentFrame('SuspenseList');\n  }\n\n  if (typeof type === 'object') {\n    switch (type.$$typeof) {\n      case REACT_FORWARD_REF_TYPE:\n        return describeFunctionComponentFrame(type.render);\n\n      case REACT_MEMO_TYPE:\n        // Memo may contain any component type so we recursively resolve it.\n        return describeUnknownElementTypeFrameInDEV(type.type, ownerFn);\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            // Lazy may contain any component type so we recursively resolve it.\n            return describeUnknownElementTypeFrameInDEV(init(payload), ownerFn);\n          } catch (x) {}\n        }\n    }\n  }\n\n  return '';\n}\n\nvar ReactCurrentOwner = ReactSharedInternals.ReactCurrentOwner;\nvar ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\nvar REACT_CLIENT_REFERENCE = Symbol.for('react.client.reference');\nvar specialPropKeyWarningShown;\nvar specialPropRefWarningShown;\nvar didWarnAboutStringRefs;\n\n{\n  didWarnAboutStringRefs = {};\n}\n\nfunction hasValidRef(config) {\n  {\n    if (hasOwnProperty.call(config, 'ref')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'ref').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.ref !== undefined;\n}\n\nfunction hasValidKey(config) {\n  {\n    if (hasOwnProperty.call(config, 'key')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'key').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.key !== undefined;\n}\n\nfunction warnIfStringRefCannotBeAutoConverted(config, self) {\n  {\n    if (typeof config.ref === 'string' && ReactCurrentOwner.current && self && ReactCurrentOwner.current.stateNode !== self) {\n      var componentName = getComponentNameFromType(ReactCurrentOwner.current.type);\n\n      if (!didWarnAboutStringRefs[componentName]) {\n        error('Component \"%s\" contains the string ref \"%s\". ' + 'Support for string refs will be removed in a future major release. ' + 'This case cannot be automatically converted to an arrow function. ' + 'We ask you to manually fix this case by using useRef() or createRef() instead. ' + 'Learn more about using refs safely here: ' + 'https://reactjs.org/link/strict-mode-string-ref', getComponentNameFromType(ReactCurrentOwner.current.type), config.ref);\n\n        didWarnAboutStringRefs[componentName] = true;\n      }\n    }\n  }\n}\n\nfunction defineKeyPropWarningGetter(props, displayName) {\n  {\n    var warnAboutAccessingKey = function () {\n      if (!specialPropKeyWarningShown) {\n        specialPropKeyWarningShown = true;\n\n        error('%s: `key` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n      }\n    };\n\n    warnAboutAccessingKey.isReactWarning = true;\n    Object.defineProperty(props, 'key', {\n      get: warnAboutAccessingKey,\n      configurable: true\n    });\n  }\n}\n\nfunction defineRefPropWarningGetter(props, displayName) {\n  {\n    {\n      var warnAboutAccessingRef = function () {\n        if (!specialPropRefWarningShown) {\n          specialPropRefWarningShown = true;\n\n          error('%s: `ref` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n        }\n      };\n\n      warnAboutAccessingRef.isReactWarning = true;\n      Object.defineProperty(props, 'ref', {\n        get: warnAboutAccessingRef,\n        configurable: true\n      });\n    }\n  }\n}\n/**\n * Factory method to create a new React element. This no longer adheres to\n * the class pattern, so do not use new to call it. Also, instanceof check\n * will not work. Instead test $$typeof field against Symbol.for('react.element') to check\n * if something is a React Element.\n *\n * @param {*} type\n * @param {*} props\n * @param {*} key\n * @param {string|object} ref\n * @param {*} owner\n * @param {*} self A *temporary* helper to detect places where `this` is\n * different from the `owner` when React.createElement is called, so that we\n * can warn. We want to get rid of owner and replace string `ref`s with arrow\n * functions, and as long as `this` and owner are the same, there will be no\n * change in behavior.\n * @param {*} source An annotation object (added by a transpiler or otherwise)\n * indicating filename, line number, and/or other information.\n * @internal\n */\n\n\nfunction ReactElement(type, key, _ref, self, source, owner, props) {\n  var ref;\n\n  {\n    ref = _ref;\n  }\n\n  var element;\n\n  {\n    // In prod, `ref` is a regular property. It will be removed in a\n    // future release.\n    element = {\n      // This tag allows us to uniquely identify this as a React Element\n      $$typeof: REACT_ELEMENT_TYPE,\n      // Built-in properties that belong on the element\n      type: type,\n      key: key,\n      ref: ref,\n      props: props,\n      // Record the component responsible for creating this element.\n      _owner: owner\n    };\n  }\n\n  {\n    // The validation flag is currently mutative. We put it on\n    // an external backing store so that we can freeze the whole object.\n    // This can be replaced with a WeakMap once they are implemented in\n    // commonly used development environments.\n    element._store = {}; // To make comparing ReactElements easier for testing purposes, we make\n    // the validation flag non-enumerable (where possible, which should\n    // include every environment we run tests in), so the test framework\n    // ignores it.\n\n    Object.defineProperty(element._store, 'validated', {\n      configurable: false,\n      enumerable: false,\n      writable: true,\n      value: false\n    }); // debugInfo contains Server Component debug information.\n\n    Object.defineProperty(element, '_debugInfo', {\n      configurable: false,\n      enumerable: false,\n      writable: true,\n      value: null\n    });\n\n    if (Object.freeze) {\n      Object.freeze(element.props);\n      Object.freeze(element);\n    }\n  }\n\n  return element;\n}\nvar didWarnAboutKeySpread = {};\n/**\n * https://github.com/reactjs/rfcs/pull/107\n * @param {*} type\n * @param {object} props\n * @param {string} key\n */\n\nfunction jsxDEV$1(type, config, maybeKey, isStaticChildren, source, self) {\n  {\n    if (!isValidElementType(type)) {\n      // This is an invalid element type.\n      //\n      // We warn in this case but don't throw. We expect the element creation to\n      // succeed and there will likely be errors in render.\n      var info = '';\n\n      if (type === undefined || typeof type === 'object' && type !== null && Object.keys(type).length === 0) {\n        info += ' You likely forgot to export your component from the file ' + \"it's defined in, or you might have mixed up default and named imports.\";\n      }\n\n      var typeString;\n\n      if (type === null) {\n        typeString = 'null';\n      } else if (isArray(type)) {\n        typeString = 'array';\n      } else if (type !== undefined && type.$$typeof === REACT_ELEMENT_TYPE) {\n        typeString = \"<\" + (getComponentNameFromType(type.type) || 'Unknown') + \" />\";\n        info = ' Did you accidentally export a JSX literal instead of a component?';\n      } else {\n        typeString = typeof type;\n      }\n\n      error('React.jsx: type is invalid -- expected a string (for ' + 'built-in components) or a class/function (for composite ' + 'components) but got: %s.%s', typeString, info);\n    } else {\n      // This is a valid element type.\n      // Skip key warning if the type isn't valid since our key validation logic\n      // doesn't expect a non-string/function type and can throw confusing\n      // errors. We don't want exception behavior to differ between dev and\n      // prod. (Rendering will throw with a helpful message and as soon as the\n      // type is fixed, the key warnings will appear.)\n      var children = config.children;\n\n      if (children !== undefined) {\n        if (isStaticChildren) {\n          if (isArray(children)) {\n            for (var i = 0; i < children.length; i++) {\n              validateChildKeys(children[i], type);\n            }\n\n            if (Object.freeze) {\n              Object.freeze(children);\n            }\n          } else {\n            error('React.jsx: Static children should always be an array. ' + 'You are likely explicitly calling React.jsxs or React.jsxDEV. ' + 'Use the Babel transform instead.');\n          }\n        } else {\n          validateChildKeys(children, type);\n        }\n      }\n    } // Warn about key spread regardless of whether the type is valid.\n\n\n    if (hasOwnProperty.call(config, 'key')) {\n      var componentName = getComponentNameFromType(type);\n      var keys = Object.keys(config).filter(function (k) {\n        return k !== 'key';\n      });\n      var beforeExample = keys.length > 0 ? '{key: someKey, ' + keys.join(': ..., ') + ': ...}' : '{key: someKey}';\n\n      if (!didWarnAboutKeySpread[componentName + beforeExample]) {\n        var afterExample = keys.length > 0 ? '{' + keys.join(': ..., ') + ': ...}' : '{}';\n\n        error('A props object containing a \"key\" prop is being spread into JSX:\\n' + '  let props = %s;\\n' + '  <%s {...props} />\\n' + 'React keys must be passed directly to JSX without using spread:\\n' + '  let props = %s;\\n' + '  <%s key={someKey} {...props} />', beforeExample, componentName, afterExample, componentName);\n\n        didWarnAboutKeySpread[componentName + beforeExample] = true;\n      }\n    }\n\n    var propName; // Reserved names are extracted\n\n    var props = {};\n    var key = null;\n    var ref = null; // Currently, key can be spread in as a prop. This causes a potential\n    // issue if key is also explicitly declared (ie. <div {...props} key=\"Hi\" />\n    // or <div key=\"Hi\" {...props} /> ). We want to deprecate key spread,\n    // but as an intermediary step, we will use jsxDEV for everything except\n    // <div {...props} key=\"Hi\" />, because we aren't currently able to tell if\n    // key is explicitly declared to be undefined or not.\n\n    if (maybeKey !== undefined) {\n      {\n        checkKeyStringCoercion(maybeKey);\n      }\n\n      key = '' + maybeKey;\n    }\n\n    if (hasValidKey(config)) {\n      {\n        checkKeyStringCoercion(config.key);\n      }\n\n      key = '' + config.key;\n    }\n\n    if (hasValidRef(config)) {\n      {\n        ref = config.ref;\n      }\n\n      warnIfStringRefCannotBeAutoConverted(config, self);\n    } // Remaining properties are added to a new props object\n\n\n    for (propName in config) {\n      if (hasOwnProperty.call(config, propName) && // Skip over reserved prop names\n      propName !== 'key' && (propName !== 'ref')) {\n        props[propName] = config[propName];\n      }\n    } // Resolve default props\n\n\n    if (type && type.defaultProps) {\n      var defaultProps = type.defaultProps;\n\n      for (propName in defaultProps) {\n        if (props[propName] === undefined) {\n          props[propName] = defaultProps[propName];\n        }\n      }\n    }\n\n    if (key || ref) {\n      var displayName = typeof type === 'function' ? type.displayName || type.name || 'Unknown' : type;\n\n      if (key) {\n        defineKeyPropWarningGetter(props, displayName);\n      }\n\n      if (ref) {\n        defineRefPropWarningGetter(props, displayName);\n      }\n    }\n\n    var element = ReactElement(type, key, ref, self, source, ReactCurrentOwner.current, props);\n\n    if (type === REACT_FRAGMENT_TYPE) {\n      validateFragmentProps(element);\n    }\n\n    return element;\n  }\n}\n\nfunction getDeclarationErrorAddendum() {\n  {\n    if (ReactCurrentOwner.current) {\n      var name = getComponentNameFromType(ReactCurrentOwner.current.type);\n\n      if (name) {\n        return '\\n\\nCheck the render method of `' + name + '`.';\n      }\n    }\n\n    return '';\n  }\n}\n/**\n * Ensure that every element either is passed in a static location, in an\n * array with an explicit keys property defined, or in an object literal\n * with valid key property.\n *\n * @internal\n * @param {ReactNode} node Statically passed child of any type.\n * @param {*} parentType node's parent's type.\n */\n\n\nfunction validateChildKeys(node, parentType) {\n  {\n    if (typeof node !== 'object' || !node) {\n      return;\n    }\n\n    if (node.$$typeof === REACT_CLIENT_REFERENCE) ; else if (isArray(node)) {\n      for (var i = 0; i < node.length; i++) {\n        var child = node[i];\n\n        if (isValidElement(child)) {\n          validateExplicitKey(child, parentType);\n        }\n      }\n    } else if (isValidElement(node)) {\n      // This element was passed in a valid location.\n      if (node._store) {\n        node._store.validated = true;\n      }\n    } else {\n      var iteratorFn = getIteratorFn(node);\n\n      if (typeof iteratorFn === 'function') {\n        // Entry iterators used to provide implicit keys,\n        // but now we print a separate warning for them later.\n        if (iteratorFn !== node.entries) {\n          var iterator = iteratorFn.call(node);\n          var step;\n\n          while (!(step = iterator.next()).done) {\n            if (isValidElement(step.value)) {\n              validateExplicitKey(step.value, parentType);\n            }\n          }\n        }\n      }\n    }\n  }\n}\n/**\n * Verifies the object is a ReactElement.\n * See https://reactjs.org/docs/react-api.html#isvalidelement\n * @param {?object} object\n * @return {boolean} True if `object` is a ReactElement.\n * @final\n */\n\n\nfunction isValidElement(object) {\n  return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\n}\nvar ownerHasKeyUseWarning = {};\n/**\n * Warn if the element doesn't have an explicit key assigned to it.\n * This element is in an array. The array could grow and shrink or be\n * reordered. All children that haven't already been validated are required to\n * have a \"key\" property assigned to it. Error statuses are cached so a warning\n * will only be shown once.\n *\n * @internal\n * @param {ReactElement} element Element that requires a key.\n * @param {*} parentType element's parent's type.\n */\n\nfunction validateExplicitKey(element, parentType) {\n  {\n    if (!element._store || element._store.validated || element.key != null) {\n      return;\n    }\n\n    element._store.validated = true;\n    var currentComponentErrorInfo = getCurrentComponentErrorInfo(parentType);\n\n    if (ownerHasKeyUseWarning[currentComponentErrorInfo]) {\n      return;\n    }\n\n    ownerHasKeyUseWarning[currentComponentErrorInfo] = true; // Usually the current owner is the offender, but if it accepts children as a\n    // property, it may be the creator of the child that's responsible for\n    // assigning it a key.\n\n    var childOwner = '';\n\n    if (element && element._owner && element._owner !== ReactCurrentOwner.current) {\n      // Give the component that originally created this child.\n      childOwner = \" It was passed a child from \" + getComponentNameFromType(element._owner.type) + \".\";\n    }\n\n    setCurrentlyValidatingElement(element);\n\n    error('Each child in a list should have a unique \"key\" prop.' + '%s%s See https://reactjs.org/link/warning-keys for more information.', currentComponentErrorInfo, childOwner);\n\n    setCurrentlyValidatingElement(null);\n  }\n}\n\nfunction setCurrentlyValidatingElement(element) {\n  {\n    if (element) {\n      var owner = element._owner;\n      var stack = describeUnknownElementTypeFrameInDEV(element.type, owner ? owner.type : null);\n      ReactDebugCurrentFrame.setExtraStackFrame(stack);\n    } else {\n      ReactDebugCurrentFrame.setExtraStackFrame(null);\n    }\n  }\n}\n\nfunction getCurrentComponentErrorInfo(parentType) {\n  {\n    var info = getDeclarationErrorAddendum();\n\n    if (!info) {\n      var parentName = getComponentNameFromType(parentType);\n\n      if (parentName) {\n        info = \"\\n\\nCheck the top-level render call using <\" + parentName + \">.\";\n      }\n    }\n\n    return info;\n  }\n}\n/**\n * Given a fragment, validate that it can only be provided with fragment props\n * @param {ReactElement} fragment\n */\n\n\nfunction validateFragmentProps(fragment) {\n  // TODO: Move this to render phase instead of at element creation.\n  {\n    var keys = Object.keys(fragment.props);\n\n    for (var i = 0; i < keys.length; i++) {\n      var key = keys[i];\n\n      if (key !== 'children' && key !== 'key') {\n        setCurrentlyValidatingElement(fragment);\n\n        error('Invalid prop `%s` supplied to `React.Fragment`. ' + 'React.Fragment can only have `key` and `children` props.', key);\n\n        setCurrentlyValidatingElement(null);\n        break;\n      }\n    }\n\n    if (fragment.ref !== null) {\n      setCurrentlyValidatingElement(fragment);\n\n      error('Invalid attribute `ref` supplied to `React.Fragment`.');\n\n      setCurrentlyValidatingElement(null);\n    }\n  }\n}\n\nvar jsxDEV = jsxDEV$1 ;\n\nexports.Fragment = REACT_FRAGMENT_TYPE;\nexports.jsxDEV = jsxDEV;\n  })();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!*******************************************************************!*\
  !*** ../node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \*******************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NvbXBpbGVkL3JlYWN0L2pzeC1kZXYtcnVudGltZS5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYixJQUFJLEtBQXFDLEVBQUUsRUFFMUMsQ0FBQztBQUNGLEVBQUUsK0xBQXNFO0FBQ3hFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NvbXBpbGVkL3JlYWN0L2pzeC1kZXYtcnVudGltZS5qcz9mMzc0Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAncHJvZHVjdGlvbicpIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9yZWFjdC1qc3gtZGV2LXJ1bnRpbWUucHJvZHVjdGlvbi5taW4uanMnKTtcbn0gZWxzZSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLmRldmVsb3BtZW50LmpzJyk7XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["main-app"], function() { return __webpack_exec__("(app-pages-browser)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);