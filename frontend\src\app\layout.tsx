/**
 * Root layout component for the Flexair Timekeeping App
 * Provides global styling, context providers, and common layout structure
 */

import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';
import { AuthProvider } from '@/contexts/AuthContext';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Flexair Timekeeping',
  description: 'Modern timekeeping application with biometric integration',
  keywords: ['timekeeping', 'attendance', 'biometric', 'HR', 'workforce management'],
  authors: [{ name: 'Flexair Team' }],
  viewport: 'width=device-width, initial-scale=1',
  robots: 'noindex, nofollow', // Remove in production
};

/**
 * Root layout component with authentication context
 * @param children - Child components to render
 */
export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className="h-full">
      <body className={`${inter.className} h-full bg-gray-50 antialiased`}>
        <AuthProvider>
          <div className="min-h-full">
            {children}
          </div>
        </AuthProvider>
      </body>
    </html>
  );
}