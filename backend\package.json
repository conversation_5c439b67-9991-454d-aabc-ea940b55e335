{"name": "flexair-backend", "version": "1.0.0", "description": "Express.js backend for Flexair Timekeeping App", "main": "src/index.js", "scripts": {"dev": "nodemon src/index.js", "build": "echo 'No build step needed for JavaScript'", "start": "node src/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.{js,ts}", "lint:fix": "eslint src/**/*.{js,ts} --fix", "db:setup": "node src/scripts/setup-database.js", "db:migrate": "node src/scripts/migrate.js", "db:seed": "node src/scripts/seed.js", "verify-xampp": "node ../verify-xampp.js", "test-db": "node test-connection.js", "setup-wizard": "node setup-wizard.js"}, "dependencies": {"bcrypt": "^5.1.1", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-validator": "^7.2.1", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "mysql2": "^3.6.5", "rate-limiter-flexible": "^5.0.3", "winston": "^3.11.0"}, "devDependencies": {"@types/node": "^20.10.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.55.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3", "typescript": "^5.3.2"}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["src/**/*.{js,ts}", "!src/scripts/**", "!src/index.js"], "coverageThreshold": {"global": {"branches": 80, "functions": 80, "lines": 80, "statements": 80}}}}