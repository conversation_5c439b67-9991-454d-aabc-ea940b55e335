/**
 * Time tracking routes
 */

const express = require('express');
const router = express.Router();
const timeController = require('./timeController');
const { authenticateToken, requireRole } = require('../../shared/middleware/authMiddleware');
const { timeValidation } = require('../../shared/middleware/validation');

// All routes require authentication
router.use(authenticateToken);

// Clock in
router.post('/clock-in', 
  timeValidation.clockIn,
  timeController.clockIn
);

// Clock out
router.post('/clock-out', 
  timeValidation.clockOut,
  timeController.clockOut
);

// Get active session
router.get('/active-session', 
  timeController.getActiveSession
);

// Get current user's time summary
router.get('/my-summary', 
  timeController.getMyTimeSummary
);

// Get time logs with filtering
router.get('/logs', 
  timeValidation.getTimeLogs,
  timeController.getTimeLogs
);

// Get time log by ID
router.get('/logs/:id', 
  timeController.getTimeLogById
);

// Update time log (admin/manager only)
router.put('/logs/:id', 
  requireRole(['admin', 'manager']),
  timeController.updateTimeLog
);

// Get time statistics
router.get('/stats', 
  timeController.getTimeStats
);

module.exports = router;