/**
 * User management routes
 */

const express = require('express');
const router = express.Router();
const userController = require('./userController');
const { authenticateToken, requireRole, requireSelfOrAdmin } = require('../../shared/middleware/authMiddleware');
const { userValidation } = require('../../shared/middleware/validation');

// All routes require authentication
router.use(authenticateToken);

// Get current user profile
router.get('/me', userController.getCurrentUser);

// Update current user profile
router.put('/me', userController.updateCurrentUser);

// Get all users (admin/manager only)
router.get('/', 
  requireRole(['admin', 'manager']), 
  userController.getAllUsers
);

// Get user statistics (admin only)
router.get('/stats', 
  requireRole('admin'), 
  userController.getUserStats
);

// Get user by ID (admin/manager or self)
router.get('/:id', 
  userValidation.getId,
  requireSelfOrAdmin,
  userController.getUserById
);

// Create new user (admin only)
router.post('/', 
  requireRole('admin'),
  userValidation.create,
  userController.createUser
);

// Update user (admin/manager or self with restrictions)
router.put('/:id', 
  userValidation.update,
  requireSelfOrAdmin,
  userController.updateUser
);

// Delete user (admin only)
router.delete('/:id', 
  requireRole('admin'),
  userValidation.getId,
  userController.deleteUser
);

// Update user password (admin or self)
router.put('/:id/password', 
  userValidation.getId,
  requireSelfOrAdmin,
  userController.updatePassword
);

module.exports = router;