/**
 * Tests for ApprovalRequestForm component
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import ApprovalRequestForm from '@/components/approvals/ApprovalRequestForm';
import * as approvalsLib from '@/lib/approvals';

// Mock the approvals library
jest.mock('@/lib/approvals', () => ({
  createApprovalRequest: jest.fn(),
}));

const mockCreateApprovalRequest = approvalsLib.createApprovalRequest as jest.MockedFunction<typeof approvalsLib.createApprovalRequest>;

describe('ApprovalRequestForm', () => {
  const defaultProps = {
    timeLogId: 1,
    currentClockIn: '2024-01-15T09:00:00',
    currentClockOut: '2024-01-15T17:00:00',
    onSuccess: jest.fn(),
    onCancel: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    it('renders the form with all required fields', () => {
      render(<ApprovalRequestForm {...defaultProps} />);

      expect(screen.getByText('Request Approval')).toBeInTheDocument();
      expect(screen.getByLabelText('Request Type')).toBeInTheDocument();
      expect(screen.getByLabelText(/Reason for Request/)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'Submit Request' })).toBeInTheDocument();
    });

    it('renders cancel button when onCancel is provided', () => {
      render(<ApprovalRequestForm {...defaultProps} />);
      expect(screen.getByRole('button', { name: 'Cancel' })).toBeInTheDocument();
    });

    it('does not render cancel button when onCancel is not provided', () => {
      const { onCancel, ...propsWithoutCancel } = defaultProps;
      render(<ApprovalRequestForm {...propsWithoutCancel} />);
      expect(screen.queryByRole('button', { name: 'Cancel' })).not.toBeInTheDocument();
    });

    it('pre-fills clock in/out times for correction requests', () => {
      render(<ApprovalRequestForm {...defaultProps} />);
      
      const clockInInput = screen.getByLabelText('Corrected Clock In Time');
      const clockOutInput = screen.getByLabelText('Corrected Clock Out Time');
      
      expect(clockInInput).toHaveValue('2024-01-15T09:00:00');
      expect(clockOutInput).toHaveValue('2024-01-15T17:00:00');
    });
  });

  describe('Request Type Selection', () => {
    it('shows correction fields by default', () => {
      render(<ApprovalRequestForm {...defaultProps} />);
      
      expect(screen.getByLabelText('Corrected Clock In Time')).toBeInTheDocument();
      expect(screen.getByLabelText('Corrected Clock Out Time')).toBeInTheDocument();
    });

    it('shows overtime fields when overtime is selected', async () => {
      const user = userEvent.setup();
      render(<ApprovalRequestForm {...defaultProps} />);
      
      const requestTypeSelect = screen.getByLabelText('Request Type');
      await user.selectOptions(requestTypeSelect, 'overtime');
      
      expect(screen.getByLabelText('Overtime Hours')).toBeInTheDocument();
      expect(screen.getByLabelText('Overtime Date')).toBeInTheDocument();
    });

    it('shows leave fields when leave is selected', async () => {
      const user = userEvent.setup();
      render(<ApprovalRequestForm {...defaultProps} />);
      
      const requestTypeSelect = screen.getByLabelText('Request Type');
      await user.selectOptions(requestTypeSelect, 'leave');
      
      expect(screen.getByLabelText('Start Date')).toBeInTheDocument();
      expect(screen.getByLabelText('End Date')).toBeInTheDocument();
      expect(screen.getByLabelText('Leave Type')).toBeInTheDocument();
    });
  });

  describe('Form Validation', () => {
    it('shows error when reason is empty', async () => {
      const user = userEvent.setup();
      render(<ApprovalRequestForm {...defaultProps} />);
      
      const submitButton = screen.getByRole('button', { name: 'Submit Request' });
      await user.click(submitButton);
      
      expect(screen.getByText('Reason is required')).toBeInTheDocument();
    });

    it('shows error when clock in time is missing for corrections', async () => {
      const user = userEvent.setup();
      render(<ApprovalRequestForm {...defaultProps} currentClockIn="" />);
      
      const reasonInput = screen.getByLabelText(/Reason for Request/);
      await user.type(reasonInput, 'Test reason');
      
      const submitButton = screen.getByRole('button', { name: 'Submit Request' });
      await user.click(submitButton);
      
      expect(screen.getByText('Clock in time is required for corrections')).toBeInTheDocument();
    });

    it('shows error when overtime hours are invalid', async () => {
      const user = userEvent.setup();
      render(<ApprovalRequestForm {...defaultProps} />);
      
      const requestTypeSelect = screen.getByLabelText('Request Type');
      await user.selectOptions(requestTypeSelect, 'overtime');
      
      const reasonInput = screen.getByLabelText(/Reason for Request/);
      await user.type(reasonInput, 'Test reason');
      
      const submitButton = screen.getByRole('button', { name: 'Submit Request' });
      await user.click(submitButton);
      
      expect(screen.getByText('Valid overtime hours are required')).toBeInTheDocument();
    });

    it('shows error when leave start date is after end date', async () => {
      const user = userEvent.setup();
      render(<ApprovalRequestForm {...defaultProps} />);
      
      const requestTypeSelect = screen.getByLabelText('Request Type');
      await user.selectOptions(requestTypeSelect, 'leave');
      
      const startDateInput = screen.getByLabelText('Start Date');
      const endDateInput = screen.getByLabelText('End Date');
      const reasonInput = screen.getByLabelText(/Reason for Request/);
      
      await user.type(startDateInput, '2024-01-20');
      await user.type(endDateInput, '2024-01-15');
      await user.type(reasonInput, 'Test reason');
      
      const submitButton = screen.getByRole('button', { name: 'Submit Request' });
      await user.click(submitButton);
      
      expect(screen.getByText('Leave start date must be before end date')).toBeInTheDocument();
    });
  });

  describe('Form Submission', () => {
    it('submits correction request successfully', async () => {
      const user = userEvent.setup();
      const mockApproval = { id: 1, status: 'pending' };
      mockCreateApprovalRequest.mockResolvedValue(mockApproval as any);
      
      render(<ApprovalRequestForm {...defaultProps} />);
      
      const reasonInput = screen.getByLabelText(/Reason for Request/);
      await user.type(reasonInput, 'Need to correct clock out time');
      
      const submitButton = screen.getByRole('button', { name: 'Submit Request' });
      await user.click(submitButton);
      
      await waitFor(() => {
        expect(mockCreateApprovalRequest).toHaveBeenCalledWith({
          timeLogId: 1,
          requestType: 'correction',
          requestData: {
            clockIn: '2024-01-15T09:00:00',
            clockOut: '2024-01-15T17:00:00'
          },
          reason: 'Need to correct clock out time'
        });
      });
      
      expect(defaultProps.onSuccess).toHaveBeenCalled();
    });

    it('submits overtime request successfully', async () => {
      const user = userEvent.setup();
      const mockApproval = { id: 1, status: 'pending' };
      mockCreateApprovalRequest.mockResolvedValue(mockApproval as any);
      
      render(<ApprovalRequestForm {...defaultProps} />);
      
      const requestTypeSelect = screen.getByLabelText('Request Type');
      await user.selectOptions(requestTypeSelect, 'overtime');
      
      const overtimeHoursInput = screen.getByLabelText('Overtime Hours');
      const overtimeDateInput = screen.getByLabelText('Overtime Date');
      const reasonInput = screen.getByLabelText(/Reason for Request/);
      
      await user.type(overtimeHoursInput, '2.5');
      await user.type(overtimeDateInput, '2024-01-15');
      await user.type(reasonInput, 'Project deadline');
      
      const submitButton = screen.getByRole('button', { name: 'Submit Request' });
      await user.click(submitButton);
      
      await waitFor(() => {
        expect(mockCreateApprovalRequest).toHaveBeenCalledWith({
          timeLogId: 1,
          requestType: 'overtime',
          requestData: {
            hours: 2.5,
            date: '2024-01-15'
          },
          reason: 'Project deadline'
        });
      });
      
      expect(defaultProps.onSuccess).toHaveBeenCalled();
    });

    it('handles submission errors', async () => {
      const user = userEvent.setup();
      mockCreateApprovalRequest.mockRejectedValue(new Error('Network error'));
      
      render(<ApprovalRequestForm {...defaultProps} />);
      
      const reasonInput = screen.getByLabelText(/Reason for Request/);
      await user.type(reasonInput, 'Test reason');
      
      const submitButton = screen.getByRole('button', { name: 'Submit Request' });
      await user.click(submitButton);
      
      await waitFor(() => {
        expect(screen.getByText('Network error')).toBeInTheDocument();
      });
      
      expect(defaultProps.onSuccess).not.toHaveBeenCalled();
    });

    it('disables submit button during submission', async () => {
      const user = userEvent.setup();
      mockCreateApprovalRequest.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)));
      
      render(<ApprovalRequestForm {...defaultProps} />);
      
      const reasonInput = screen.getByLabelText(/Reason for Request/);
      await user.type(reasonInput, 'Test reason');
      
      const submitButton = screen.getByRole('button', { name: 'Submit Request' });
      await user.click(submitButton);
      
      expect(screen.getByRole('button', { name: 'Submitting...' })).toBeDisabled();
    });
  });

  describe('Cancel Functionality', () => {
    it('calls onCancel when cancel button is clicked', async () => {
      const user = userEvent.setup();
      render(<ApprovalRequestForm {...defaultProps} />);
      
      const cancelButton = screen.getByRole('button', { name: 'Cancel' });
      await user.click(cancelButton);
      
      expect(defaultProps.onCancel).toHaveBeenCalled();
    });

    it('calls onCancel when close button is clicked', async () => {
      const user = userEvent.setup();
      render(<ApprovalRequestForm {...defaultProps} />);

      // Find close button by its SVG content
      const closeButton = screen.getByRole('button');
      const svgElement = closeButton.querySelector('svg');
      if (svgElement) {
        await user.click(closeButton);
        expect(defaultProps.onCancel).toHaveBeenCalled();
      }
    });
  });

  describe('Error Handling', () => {
    it('clears error when user starts typing', async () => {
      const user = userEvent.setup();
      render(<ApprovalRequestForm {...defaultProps} />);
      
      // Trigger validation error
      const submitButton = screen.getByRole('button', { name: 'Submit Request' });
      await user.click(submitButton);
      
      expect(screen.getByText('Reason is required')).toBeInTheDocument();
      
      // Start typing to clear error
      const reasonInput = screen.getByLabelText(/Reason for Request/);
      await user.type(reasonInput, 'T');
      
      expect(screen.queryByText('Reason is required')).not.toBeInTheDocument();
    });
  });
});
