/**
 * Migration script to rename password column to password_hash
 * Fixes the column name mismatch between database and application code
 */

const mysql = require('mysql2/promise');
const dotenv = require('dotenv');
const path = require('path');

// Load environment variables
dotenv.config({ path: path.join(__dirname, '../../.env') });

async function migratePasswordColumn() {
  let connection;

  try {
    // Create connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST,
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME
    });

    console.log('🔗 Connected to database for migration...');

    // Check if the password column exists and password_hash doesn't
    const [columns] = await connection.execute(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'users' 
      AND COLUMN_NAME IN ('password', 'password_hash')
    `, [process.env.DB_NAME]);

    const hasPassword = columns.some(col => col.COLUMN_NAME === 'password');
    const hasPasswordHash = columns.some(col => col.COLUMN_NAME === 'password_hash');

    console.log(`📊 Current state: password=${hasPassword}, password_hash=${hasPasswordHash}`);

    if (hasPassword && !hasPasswordHash) {
      console.log('🔄 Renaming password column to password_hash...');
      await connection.execute(`
        ALTER TABLE users 
        CHANGE COLUMN password password_hash VARCHAR(255) NOT NULL
      `);
      console.log('✅ Successfully renamed password column to password_hash');
    } else if (hasPasswordHash) {
      console.log('✅ password_hash column already exists, no migration needed');
    } else {
      console.log('❌ Neither password nor password_hash column found');
    }

  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔐 Database connection closed');
    }
  }
}

// Run migration if this script is executed directly
if (require.main === module) {
  migratePasswordColumn()
    .then(() => {
      console.log('🎉 Migration completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Migration failed:', error);
      process.exit(1);
    });
}

module.exports = { migratePasswordColumn };