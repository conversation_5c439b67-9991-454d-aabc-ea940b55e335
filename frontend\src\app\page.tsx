/**
 * Home page component - Landing page for Flexair Timekeeping
 */

'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';

export default function HomePage() {
  const router = useRouter();
  const { isAuthenticated, loading } = useAuth();

  useEffect(() => {
    // Redirect authenticated users to dashboard
    if (!loading && isAuthenticated) {
      router.push('/dashboard');
    }
  }, [isAuthenticated, loading, router]);

  // Show loading while checking auth state
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-16">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-5xl font-bold text-gray-900 mb-4">
            Flexair Timekeeping
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Modern timekeeping application with biometric integration, 
            comprehensive analytics, and seamless workforce management.
          </p>
        </div>

        {/* Feature Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          <FeatureCard
            title="Biometric Integration"
            description="Secure fingerprint and facial recognition for accurate time tracking"
            icon="🔐"
          />
          <FeatureCard
            title="Real-time Analytics"
            description="Comprehensive dashboard with insights and reporting capabilities"
            icon="📊"
          />
          <FeatureCard
            title="Approval Workflow"
            description="Streamlined approval process for time corrections and requests"
            icon="✅"
          />
          <FeatureCard
            title="Role-based Access"
            description="Admin, manager, and employee roles with appropriate permissions"
            icon="👥"
          />
          <FeatureCard
            title="Audit Logging"
            description="Complete audit trail for all system activities and changes"
            icon="📝"
          />
          <FeatureCard
            title="REST API"
            description="Modern Express.js backend with comprehensive API endpoints"
            icon="🚀"
          />
        </div>

        {/* Authentication Actions */}
        <div className="text-center mb-12">
          <div className="space-y-4 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-center">
            <Link
              href="/auth/login"
              className="inline-block bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-3 px-8 rounded-lg transition-colors"
            >
              Sign In
            </Link>
            <Link
              href="/auth/register"
              className="inline-block bg-white hover:bg-gray-50 text-indigo-600 font-semibold py-3 px-8 rounded-lg border-2 border-indigo-600 transition-colors"
            >
              Create Account
            </Link>
          </div>
        </div>

        {/* Status Section */}
        <div className="bg-white rounded-lg shadow-lg p-8 text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Development Status
          </h2>
          <div className="grid md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-xl font-semibold text-green-600 mb-2">
                ✅ Backend Complete
              </h3>
              <ul className="text-left text-gray-600 space-y-1">
                <li>• Authentication with JWT</li>
                <li>• User Management</li>
                <li>• Time Tracking</li>
                <li>• Approval Workflow</li>
                <li>• Biometric Integration</li>
                <li>• Dashboard Analytics</li>
              </ul>
            </div>
            <div>
              <h3 className="text-xl font-semibold text-green-600 mb-2">
                ✅ Frontend Authentication
              </h3>
              <ul className="text-left text-gray-600 space-y-1">
                <li>• Login & Registration Forms</li>
                <li>• Protected Routes</li>
                <li>• JWT Token Management</li>
                <li>• Authentication Context</li>
                <li>• Auto Token Refresh</li>
                <li>• Role-based Access Control</li>
              </ul>
            </div>
          </div>
          
          <div className="mt-8 p-4 bg-gray-50 rounded-lg">
            <p className="text-sm text-gray-500">
              API Status: <span className="text-green-600 font-semibold">Running on http://localhost:5002</span>
            </p>
            <p className="text-sm text-gray-500 mt-1">
              Frontend: <span className="text-green-600 font-semibold">Authentication System Ready</span>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

/**
 * Feature card component
 */
function FeatureCard({ title, description, icon }: {
  title: string;
  description: string;
  icon: string;
}) {
  return (
    <div className="bg-white rounded-lg shadow-md p-6 text-center hover:shadow-lg transition-shadow">
      <div className="text-4xl mb-4">{icon}</div>
      <h3 className="text-xl font-semibold text-gray-900 mb-2">{title}</h3>
      <p className="text-gray-600">{description}</p>
    </div>
  );
}