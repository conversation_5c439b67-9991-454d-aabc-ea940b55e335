/**
 * Authentication controller
 * Handles authentication business logic
 */

const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const { logger, logSecurityEvent } = require('../../shared/logger');
const { 
  AuthenticationError, 
  ValidationError, 
  NotFoundError 
} = require('../../shared/middleware/errorHandler');
const userService = require('../users/userService');
const authService = require('./authService');

/**
 * Register a new user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function register(req, res) {
  const { email, password, firstName, lastName, department } = req.body;

  // Check if user already exists
  const existingUser = await userService.getUserByEmail(email);
  if (existingUser) {
    throw new ValidationError('User with this email already exists');
  }

  // Create user (userService will handle password hashing)
  const userData = {
    email,
    password: password, // userService will hash this
    firstName,
    lastName,
    department,
    role: 'employee' // Default role
  };

  const user = await userService.createUser(userData);

  // Generate tokens
  const { accessToken, refreshToken } = authService.generateTokens(user);

  // Store refresh token
  await authService.storeRefreshToken(user.id, refreshToken);

  // Log successful registration
  logger.info('User registered successfully', { 
    userId: user.id, 
    email: user.email,
    ip: req.ip 
  });

  // Transform and clean user response
  const transformedUser = userService.transformUserObject(user);
  delete transformedUser.password;
  delete transformedUser.password_hash;

  res.status(201).json({
    message: 'User registered successfully',
    user: transformedUser,
    tokens: {
      accessToken,
      refreshToken
    }
  });
}

/**
 * Authenticate user and return tokens
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function login(req, res) {
  const { email, password, rememberMe = false } = req.body;

  // Find user by email
  const user = await userService.getUserByEmail(email);
  if (!user) {
    logSecurityEvent('LOGIN_FAILED', {
      email,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      message: 'User not found'
    });
    throw new AuthenticationError('Invalid credentials');
  }

  // Check if user is active
  if (!user.is_active) {
    logSecurityEvent('LOGIN_FAILED', {
      userId: user.id,
      email,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      message: 'Account disabled'
    });
    throw new AuthenticationError('Account is disabled');
  }

  // Verify password
  const isPasswordValid = await bcrypt.compare(password, user.password_hash);
  if (!isPasswordValid) {
    logSecurityEvent('LOGIN_FAILED', {
      userId: user.id,
      email,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      message: 'Invalid password'
    });
    throw new AuthenticationError('Invalid credentials');
  }

  // Generate tokens
  const tokenOptions = rememberMe ? { expiresIn: '30d' } : {};
  const { accessToken, refreshToken } = authService.generateTokens(user, tokenOptions);

  // Store refresh token
  await authService.storeRefreshToken(user.id, refreshToken);

  // Update last login
  await userService.updateLastLogin(user.id);

  // Log successful login
  logger.info('User logged in successfully', {
    userId: user.id,
    email: user.email,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });

  // Transform and clean user response
  const transformedUser = userService.transformUserObject(user);
  delete transformedUser.password;
  delete transformedUser.password_hash;

  res.json({
    message: 'Login successful',
    user: transformedUser,
    tokens: {
      accessToken,
      refreshToken
    }
  });
}

/**
 * Logout user and invalidate token
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function logout(req, res) {
  const { refreshToken } = req.body;
  const userId = req.user.id;

  // Invalidate refresh token if provided
  if (refreshToken) {
    await authService.invalidateRefreshToken(refreshToken);
  }

  // Add token to blacklist (for immediate invalidation)
  const token = req.headers.authorization?.replace('Bearer ', '');
  if (token) {
    await authService.blacklistToken(token);
  }

  logger.info('User logged out successfully', {
    userId,
    ip: req.ip
  });

  res.json({
    message: 'Logout successful'
  });
}

/**
 * Refresh access token
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function refreshToken(req, res) {
  const { refreshToken } = req.body;

  // Verify refresh token
  const payload = await authService.verifyRefreshToken(refreshToken);
  
  // Get user
  const user = await userService.getUserById(payload.userId);
  if (!user || !user.is_active) {
    throw new AuthenticationError('Invalid refresh token');
  }

  // Generate new tokens
  const tokens = authService.generateTokens(user);

  // Store new refresh token and invalidate old one
  await authService.storeRefreshToken(user.id, tokens.refreshToken);
  await authService.invalidateRefreshToken(refreshToken);

  logger.info('Token refreshed successfully', {
    userId: user.id,
    ip: req.ip
  });

  res.json({
    message: 'Token refreshed successfully',
    tokens
  });
}

/**
 * Send password reset email
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function forgotPassword(req, res) {
  const { email } = req.body;

  const user = await userService.getUserByEmail(email);
  if (!user) {
    // Don't reveal if email exists
    res.json({
      message: 'If the email exists, a password reset link has been sent'
    });
    return;
  }

  // Generate reset token
  const resetToken = crypto.randomBytes(32).toString('hex');
  const resetTokenExpiry = new Date(Date.now() + 60 * 60 * 1000); // 1 hour

  // Store reset token
  await authService.storePasswordResetToken(user.id, resetToken, resetTokenExpiry);

  // TODO: Send email with reset link
  // For now, just log the token (remove in production)
  logger.info('Password reset requested', {
    userId: user.id,
    email: user.email,
    resetToken, // Remove this in production
    ip: req.ip
  });

  res.json({
    message: 'If the email exists, a password reset link has been sent'
  });
}

/**
 * Reset password using reset token
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function resetPassword(req, res) {
  const { token, newPassword } = req.body;

  // Verify reset token
  const user = await authService.verifyPasswordResetToken(token);
  if (!user) {
    throw new ValidationError('Invalid or expired reset token');
  }

  // Hash new password
  const saltRounds = parseInt(process.env.BCRYPT_ROUNDS) || 12;
  const hashedPassword = await bcrypt.hash(newPassword, saltRounds);

  // Update password and remove reset token
  await userService.updateUserPassword(user.id, hashedPassword);
  await authService.clearPasswordResetToken(user.id);

  // Invalidate all refresh tokens for security
  await authService.invalidateAllUserTokens(user.id);

  logger.info('Password reset successfully', {
    userId: user.id,
    ip: req.ip
  });

  res.json({
    message: 'Password reset successfully'
  });
}

/**
 * Change password for authenticated user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function changePassword(req, res) {
  const { currentPassword, newPassword } = req.body;
  const userId = req.user.id;

  // Get user with password
  const user = await userService.getUserByEmail(req.user.email);
  
  // Verify current password
  const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password_hash);
  if (!isCurrentPasswordValid) {
    throw new ValidationError('Current password is incorrect');
  }

  // Hash new password
  const saltRounds = parseInt(process.env.BCRYPT_ROUNDS) || 12;
  const hashedPassword = await bcrypt.hash(newPassword, saltRounds);

  // Update password
  await userService.updateUserPassword(userId, hashedPassword);

  logger.info('Password changed successfully', {
    userId,
    ip: req.ip
  });

  res.json({
    message: 'Password changed successfully'
  });
}

/**
 * Verify token and return user info
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function verifyToken(req, res) {
  // User is already attached by auth middleware
  const transformedUser = userService.transformUserObject(req.user);
  delete transformedUser.password;
  delete transformedUser.password_hash;

  res.json({
    valid: true,
    user: transformedUser
  });
}

/**
 * Get current user profile
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function getProfile(req, res) {
  const user = await userService.getUserById(req.user.id);
  if (!user) {
    throw new NotFoundError('User not found');
  }

  const transformedUser = userService.transformUserObject(user);
  delete transformedUser.password;
  delete transformedUser.password_hash;

  res.json({
    user: transformedUser
  });
}

/**
 * Update current user profile
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function updateProfile(req, res) {
  const userId = req.user.id;
  const updateData = req.body;

  // Remove sensitive fields that shouldn't be updated via this endpoint
  delete updateData.password;
  delete updateData.role;
  delete updateData.isActive;
  delete updateData.emailVerified;

  const updatedUser = await userService.updateUser(userId, updateData, userId);

  logger.info('User profile updated', {
    userId,
    updatedFields: Object.keys(updateData),
    ip: req.ip
  });

  const transformedUser = userService.transformUserObject(updatedUser);
  delete transformedUser.password;
  delete transformedUser.password_hash;

  res.json({
    message: 'Profile updated successfully',
    user: transformedUser
  });
}

module.exports = {
  register,
  login,
  logout,
  refreshToken,
  forgotPassword,
  resetPassword,
  changePassword,
  verifyToken,
  getProfile,
  updateProfile
};