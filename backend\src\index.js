/**
 * Main entry point for the Flexair Timekeeping API server
 * Sets up Express app with middleware, routes, and error handling
 */

// Load environment variables first
require('dotenv').config();

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const morgan = require('morgan');
const { createConnection } = require('./shared/database');
const { logger } = require('./shared/logger');
const { errorHandler } = require('./shared/middleware/errorHandler');
const { rateLimiter } = require('./shared/middleware/rateLimiter');

// Import routes
const authRoutes = require('./modules/auth/authRoutes');
const userRoutes = require('./modules/users/userRoutes');
const timeRoutes = require('./modules/time/timeRoutes');
const approvalRoutes = require('./modules/approvals/approvalRoutes');
const biometricRoutes = require('./modules/biometric/biometricRoutes');
const dashboardRoutes = require('./modules/dashboard/dashboardRoutes');

const app = express();
const PORT = process.env.PORT || 5000;

/**
 * Initialize database connection
 * @returns {Promise<void>}
 */
async function initializeDatabase() {
  try {
    await createConnection();
    logger.info('Database connection established successfully');
  } catch (error) {
    logger.error('Failed to connect to database:', error);
    process.exit(1);
  }
}

/**
 * Configure middleware
 */
function setupMiddleware() {
  // Security middleware
  app.use(helmet());
  // Enable CORS for all routes and origins in development
  const corsOptions = {
    origin: function (origin, callback) {
      // Allow requests with no origin (like mobile apps or curl requests)
      if (!origin) return callback(null, true);
      
      const allowedOrigins = [
        'http://localhost:3000',
        'http://127.0.0.1:3000',
        'http://localhost:5002',
        'http://127.0.0.1:5002',
        'http://localhost:3001',
        'http://127.0.0.1:3001'
      ];
      
      if (allowedOrigins.indexOf(origin) !== -1) {
        callback(null, true);
      } else {
        logger.warn(`CORS blocked origin: ${origin}`);
        callback(new Error('Not allowed by CORS'));
      }
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH', 'HEAD'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept', 'Origin'],
    exposedHeaders: ['Content-Length', 'Content-Type'],
    optionsSuccessStatus: 200, // Some legacy browsers (IE11, various SmartTVs) choke on 204
    preflightContinue: false
  };
  
  app.use(cors(corsOptions));
  app.options('*', cors(corsOptions)); // Enable preflight for all routes
  
  // Compression and logging
  app.use(compression());
  app.use(morgan('combined', { stream: { write: message => logger.info(message.trim()) }}));
  
  // Rate limiting
  app.use(rateLimiter);
  
  // Body parsing
  app.use(express.json({ limit: '10mb' }));
  app.use(express.urlencoded({ extended: true, limit: '10mb' }));
}

/**
 * Configure API routes
 */
function setupRoutes() {
  // Health check
  app.get('/health', (req, res) => {
    res.json({ 
      status: 'healthy', 
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || '1.0.0'
    });
  });

  // API routes
  app.use('/api/auth', authRoutes);
  app.use('/api/users', userRoutes);
  app.use('/api/time', timeRoutes);
  app.use('/api/approvals', approvalRoutes);
  app.use('/api/biometric', biometricRoutes);
  app.use('/api/dashboard', dashboardRoutes);

  // 404 handler
  app.use('*', (req, res) => {
    res.status(404).json({ 
      error: 'Endpoint not found',
      path: req.originalUrl,
      method: req.method
    });
  });
}

/**
 * Start the server
 * @returns {Promise<void>}
 */
async function startServer() {
  try {
    await initializeDatabase();
    setupMiddleware();
    setupRoutes();
    
    // Error handling middleware (must be last)
    app.use(errorHandler);
    
    const server = app.listen(PORT, '0.0.0.0', () => {
      const address = server.address();
      logger.info(`🚀 Flexair Timekeeping API server running on port ${address.port}`);
      logger.info(`📍 Environment: ${process.env.NODE_ENV || 'development'}`);
      logger.info(`🔗 Health check: http://localhost:${address.port}/health`);
    });

    server.on('error', (error) => {
      if (error.code === 'EADDRINUSE') {
        logger.error(`Port ${PORT} is already in use`);
        process.exit(1);
      } else {
        logger.error('Server error:', error);
        process.exit(1);
      }
    });

    server.on('listening', () => {
      const address = server.address();
      logger.info(`Server successfully bound to ${address.address}:${address.port}`);
    });
  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
}

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

// Start the server
if (require.main === module) {
  startServer();
}

module.exports = app;