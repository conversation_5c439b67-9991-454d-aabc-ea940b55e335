'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { apiRequest } from '@/lib/auth';

interface ActiveSession {
  id: number;
  clock_in_time: string;
  clock_in_location?: string;
  break_minutes?: number;
  overtime_minutes?: number;
}

interface TimeStats {
  today_hours: string;
  week_hours: string;
  month_hours: string;
  overtime_week: string;
}

/**
 * Clock In/Out component for time tracking
 * Allows employees to clock in/out and view current session info
 */
export default function ClockInOut() {
  const { user } = useAuth();
  const [activeSession, setActiveSession] = useState<ActiveSession | null>(null);
  const [timeStats, setTimeStats] = useState<TimeStats | null>(null);
  const [location, setLocation] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isDetectingLocation, setIsDetectingLocation] = useState(false);
  const [message, setMessage] = useState('');
  const [currentTime, setCurrentTime] = useState(new Date());
  const [coordinates, setCoordinates] = useState<{lat: number, lng: number} | null>(null);

  // Update current time every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Load active session and stats on component mount
  useEffect(() => {
    loadActiveSession();
    loadTimeStats();
  }, []);

  // Note: Removed auto-detection to prevent interference with manual detection
  // Users can manually click the GPS button when they want location detection

  /**
   * Load the current active session from API
   */
  const loadActiveSession = async () => {
    try {
      const response = await apiRequest('/time/active-session');
      if (response.success && response.data) {
        setActiveSession(response.data);
      }
    } catch (error) {
      console.error('Error loading active session:', error);
    }
  };

  /**
   * Load time statistics from API
   */
  const loadTimeStats = async () => {
    try {
      const response = await apiRequest('/time/stats');
      if (response.success && response.data) {
        setTimeStats(response.data);
      }
    } catch (error) {
      console.error('Error loading time stats:', error);
    }
  };

  /**
   * Detect user's current location using GPS
   */
  const detectLocation = async () => {
    setIsDetectingLocation(true);
    setMessage('Getting your location...');
    
    // Clear previous coordinates to ensure fresh detection
    setCoordinates(null);
    setLocation('');

    try {
      if (!navigator.geolocation) {
        setMessage('Geolocation is not supported by this browser');
        return;
      }

      setMessage('Acquiring GPS coordinates...');
      const position = await getCurrentPosition();
      const { latitude, longitude, accuracy } = position.coords;
      
      console.log('GPS Position acquired:', { latitude, longitude, accuracy });
      
      setCoordinates({ lat: latitude, lng: longitude });
      setMessage('Converting coordinates to address...');
      
      // Reverse geocode to get readable address
      const address = await reverseGeocode(latitude, longitude);
      setLocation(address);
      
      if (accuracy > 100) {
        setMessage(`Location detected (accuracy: ${Math.round(accuracy)}m). You may want to try again for better accuracy.`);
      } else {
        setMessage(`Location detected successfully! (accuracy: ${Math.round(accuracy)}m)`);
      }
      
    } catch (error) {
      console.error('Location detection error:', error);
      setCoordinates(null); // Clear coordinates on error
      
      if (error instanceof GeolocationPositionError) {
        switch (error.code) {
          case error.PERMISSION_DENIED:
            setMessage('Location access denied. Please enable location permissions in your browser and try again.');
            break;
          case error.POSITION_UNAVAILABLE:
            setMessage('Location information unavailable. Please check your GPS/network and try again, or enter location manually.');
            break;
          case error.TIMEOUT:
            setMessage('Location detection timed out. Please try again or enter location manually.');
            break;
          default:
            setMessage('Location detection failed. Please enter location manually.');
            break;
        }
      } else {
        setMessage('Unable to detect location. Please enter location manually.');
      }
    } finally {
      setIsDetectingLocation(false);
    }
  };

  /**
   * Get current position as a Promise with improved accuracy
   */
  const getCurrentPosition = (): Promise<GeolocationPosition> => {
    return new Promise((resolve, reject) => {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          // Validate position accuracy
          if (position.coords.accuracy > 100) {
            console.warn('GPS accuracy is low:', position.coords.accuracy, 'meters');
            // Still resolve but with warning
          }
          resolve(position);
        },
        reject,
        {
          enableHighAccuracy: true,
          timeout: 15000, // Increased timeout for better accuracy
          maximumAge: 60000 // Reduced cache time to 1 minute for fresher readings
        }
      );
    });
  };

  /**
   * Reverse geocode coordinates to readable address
   */
  const reverseGeocode = async (lat: number, lng: number): Promise<string> => {
    try {
      console.log('Reverse geocoding coordinates:', { lat, lng });
      
      // Using OpenStreetMap Nominatim API (free, no API key required)
      const response = await fetch(
        `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&zoom=18&addressdetails=1&limit=1`,
        {
          headers: {
            'User-Agent': 'FlexairTimekeeping/1.0',
            'Accept': 'application/json'
          }
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      console.log('Geocoding response:', data);
      
      if (data && data.display_name) {
        // Extract relevant parts of the address
        const address = data.address || {};
        const parts = [];
        
        // Building number and street
        if (address.house_number && address.road) {
          parts.push(`${address.house_number} ${address.road}`);
        } else if (address.road) {
          parts.push(address.road);
        } else if (address.pedestrian) {
          parts.push(address.pedestrian);
        }
        
        // Neighborhood/area
        if (address.neighbourhood || address.suburb || address.quarter) {
          parts.push(address.neighbourhood || address.suburb || address.quarter);
        }
        
        // City/town
        if (address.city || address.town || address.village || address.municipality) {
          parts.push(address.city || address.town || address.village || address.municipality);
        }
        
        // State/region
        if (address.state || address.region) {
          parts.push(address.state || address.region);
        }
        
        // Country (only if international)
        if (address.country && address.country !== 'United States') {
          parts.push(address.country);
        }
        
        let formattedAddress = parts.length > 0 ? parts.join(', ') : data.display_name;
        
        // Clean up the address
        formattedAddress = formattedAddress.replace(/,\s*,/g, ',').trim();
        
        // Limit length to keep it reasonable
        if (formattedAddress.length > 100) {
          formattedAddress = formattedAddress.substring(0, 97) + '...';
        }
        
        console.log('Formatted address:', formattedAddress);
        return formattedAddress;
      }
      
      throw new Error('No address data in response');
      
    } catch (error) {
      console.error('Reverse geocoding error:', error);
      // Fallback to coordinates if geocoding fails
      const fallbackAddress = `GPS: ${lat.toFixed(6)}, ${lng.toFixed(6)}`;
      console.log('Using fallback address:', fallbackAddress);
      return fallbackAddress;
    }
  };

  /**
   * Handle clock in action
   */
  const handleClockIn = async () => {
    if (!location.trim()) {
      setMessage('Please detect your location or enter it manually');
      return;
    }

    setIsLoading(true);
    setMessage('');

    try {
      const requestData: any = { location: location.trim() };
      
      // Include GPS coordinates if available
      if (coordinates) {
        requestData.coordinates = coordinates;
      }

      const response = await apiRequest('/time/clock-in', {
        method: 'POST',
        body: JSON.stringify(requestData)
      });

      if (response.success) {
        setMessage('Successfully clocked in!');
        setLocation('');
        await loadActiveSession();
        await loadTimeStats();
      } else {
        setMessage(response.message || 'Failed to clock in');
      }
    } catch (error) {
      setMessage('Error clocking in. Please try again.');
      console.error('Clock in error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Handle clock out action
   */
  const handleClockOut = async () => {
    setIsLoading(true);
    setMessage('');

    try {
      const response = await apiRequest('/time/clock-out', {
        method: 'POST',
        body: JSON.stringify({})
      });

      if (response.success) {
        setMessage('Successfully clocked out!');
        setActiveSession(null);
        await loadTimeStats();
      } else {
        setMessage(response.message || 'Failed to clock out');
      }
    } catch (error) {
      setMessage('Error clocking out. Please try again.');
      console.error('Clock out error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Calculate session duration
   */
  const getSessionDuration = () => {
    if (!activeSession) return '0:00:00';
    
    const clockInTime = new Date(activeSession.clock_in_time);
    const duration = currentTime.getTime() - clockInTime.getTime();
    
    const hours = Math.floor(duration / (1000 * 60 * 60));
    const minutes = Math.floor((duration % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((duration % (1000 * 60)) / 1000);
    
    return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  /**
   * Format time for display
   */
  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: true 
    });
  };

  return (
    <div className="compact-card">
      <div className="mb-4">
        <h2 className="text-xl font-bold text-gray-800 mb-2">Time Clock</h2>
        <p className="text-gray-600">Welcome back, {user?.firstName}!</p>
        <p className="text-lg font-mono text-gray-800 mt-2">
          {currentTime.toLocaleString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: true
          })}
        </p>
      </div>

      {/* Active Session Display */}
      {activeSession && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-3 mb-4">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-lg font-semibold text-green-800">Currently Clocked In</h3>
            <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-sm font-medium">
              Active
            </span>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            <div>
              <p className="text-sm text-green-600">Clock In Time</p>
              <p className="font-semibold text-green-800">
                {formatTime(activeSession.clock_in_time)}
              </p>
            </div>
            <div>
              <p className="text-sm text-green-600">Session Duration</p>
              <p className="font-mono text-xl font-bold text-green-800">
                {getSessionDuration()}
              </p>
            </div>
            <div>
              <p className="text-sm text-green-600">Location</p>
              <p className="font-semibold text-green-800">
                {activeSession.clock_in_location || 'Not specified'}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Clock In/Out Actions */}
      <div className="space-y-3 mb-4">
        {!activeSession ? (
          <div className="space-y-3">
            <div>
              <label htmlFor="location" className="block text-sm font-medium text-gray-700 mb-2">
                Location
              </label>
              <div className="space-y-3">
                <div className="flex gap-2">
                  <input
                    type="text"
                    id="location"
                    value={location}
                    onChange={(e) => setLocation(e.target.value)}
                    placeholder="Click GPS to detect location or enter manually"
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    disabled={isLoading || isDetectingLocation}
                  />
                  <button
                    onClick={detectLocation}
                    disabled={isLoading || isDetectingLocation}
                    className="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-md transition duration-200 flex items-center gap-2"
                    title="Detect my location using GPS"
                  >
                    {isDetectingLocation ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                        <span className="hidden sm:inline">Detecting...</span>
                      </>
                    ) : (
                      <>
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        <span className="hidden sm:inline">GPS</span>
                      </>
                    )}
                  </button>
                  {(location || coordinates) && (
                    <button
                      onClick={() => {
                        setLocation('');
                        setCoordinates(null);
                        setMessage('');
                      }}
                      disabled={isLoading || isDetectingLocation}
                      className="px-3 py-2 bg-gray-500 hover:bg-gray-600 disabled:bg-gray-400 text-white rounded-md transition duration-200"
                      title="Clear location"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
                      </svg>
                    </button>
                  )}
                </div>
                {coordinates && (
                  <div className="flex items-center justify-between text-xs text-gray-500 bg-gray-50 p-2 rounded border">
                    <div className="flex items-center gap-1">
                      <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                      </svg>
                      GPS: {coordinates.lat.toFixed(6)}, {coordinates.lng.toFixed(6)}
                    </div>
                    <button
                      onClick={detectLocation}
                      disabled={isDetectingLocation}
                      className="text-blue-600 hover:text-blue-800 disabled:text-gray-400"
                      title="Refresh location"
                    >
                      <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                      </svg>
                    </button>
                  </div>
                )}
              </div>
            </div>
            <button
              onClick={handleClockIn}
              disabled={isLoading || isDetectingLocation}
              className="w-full bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-bold py-3 px-4 rounded-lg transition duration-200"
            >
              {isLoading ? 'Clocking In...' : 'Clock In'}
            </button>
          </div>
        ) : (
          <button
            onClick={handleClockOut}
            disabled={isLoading}
            className="w-full bg-red-600 hover:bg-red-700 disabled:bg-gray-400 text-white font-bold py-3 px-4 rounded-lg transition duration-200"
          >
            {isLoading ? 'Clocking Out...' : 'Clock Out'}
          </button>
        )}
      </div>

      {/* Message Display */}
      {message && (
        <div className={`p-3 rounded-md mb-6 ${
          message.includes('Successfully') || message.includes('detected successfully')
            ? 'bg-green-100 text-green-700 border border-green-200' 
            : message.includes('Please detect') || message.includes('enter manually')
            ? 'bg-yellow-100 text-yellow-700 border border-yellow-200'
            : 'bg-red-100 text-red-700 border border-red-200'
        }`}>
          {message}
        </div>
      )}

      {/* Time Statistics */}
      {timeStats && (
        <div className="border-t pt-4">
          <h3 className="text-lg font-semibold text-gray-800 mb-3">Time Summary</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            <div className="text-center p-2 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-600">Today</p>
              <p className="text-lg font-bold text-gray-800">{timeStats.today_hours}</p>
            </div>
            <div className="text-center p-2 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-600">This Week</p>
              <p className="text-lg font-bold text-gray-800">{timeStats.week_hours}</p>
            </div>
            <div className="text-center p-2 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-600">This Month</p>
              <p className="text-lg font-bold text-gray-800">{timeStats.month_hours}</p>
            </div>
            <div className="text-center p-2 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-600">Overtime (Week)</p>
              <p className="text-lg font-bold text-orange-600">{timeStats.overtime_week}</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}