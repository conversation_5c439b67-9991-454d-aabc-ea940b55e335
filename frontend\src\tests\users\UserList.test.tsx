/**
 * UserList Component Tests
 * Tests user list functionality, pagination, filtering, and user interactions
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import UserList from '@/components/users/UserList';
import { useAuth } from '@/contexts/AuthContext';
import * as userService from '@/lib/users';

// Mock auth context
jest.mock('@/contexts/AuthContext', () => ({
  useAuth: jest.fn(),
}));

// Mock user service
jest.mock('@/lib/users', () => ({
  getAllUsers: jest.fn(),
  deleteUser: jest.fn(),
  toggleUserStatus: jest.fn(),
}));

const mockUsers = [
  {
    id: 1,
    email: '<EMAIL>',
    firstName: 'John',
    lastName: 'Doe',
    role: 'employee' as const,
    isActive: true,
    emailVerified: true,
    biometricEnabled: false,
    createdAt: '2024-01-01T00:00:00Z',
    lastLogin: '2024-01-15T10:00:00Z',
  },
  {
    id: 2,
    email: '<EMAIL>',
    firstName: 'Jane',
    lastName: 'Smith',
    role: 'manager' as const,
    isActive: true,
    emailVerified: true,
    biometricEnabled: true,
    createdAt: '2024-01-02T00:00:00Z',
    department: 'Engineering',
  },
  {
    id: 3,
    email: '<EMAIL>',
    firstName: 'Admin',
    lastName: 'User',
    role: 'admin' as const,
    isActive: false,
    emailVerified: true,
    biometricEnabled: false,
    createdAt: '2024-01-03T00:00:00Z',
  },
];

const mockUserListResponse = {
  users: mockUsers,
  pagination: {
    page: 1,
    limit: 20,
    total: 3,
    totalPages: 1,
  },
};

const defaultAuthContextValue = {
  user: {
    id: 1,
    email: '<EMAIL>',
    firstName: 'Admin',
    lastName: 'User',
    role: 'admin' as const,
    isActive: true,
  },
  loading: false,
  error: null,
  isAuthenticated: true,
  login: jest.fn(),
  register: jest.fn(),
  logout: jest.fn(),
  clearError: jest.fn(),
  refreshUser: jest.fn(),
};

describe('UserList', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (useAuth as jest.Mock).mockReturnValue(defaultAuthContextValue);
    (userService.getAllUsers as jest.Mock).mockResolvedValue(mockUserListResponse);
  });

  /**
   * Test: Component renders correctly with user list
   */
  it('renders user list with correct data', async () => {
    render(<UserList />);
    
    expect(screen.getByText('Users')).toBeInTheDocument();
    expect(screen.getByText('3 total users')).toBeInTheDocument();
    
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      expect(screen.getByText('Admin User')).toBeInTheDocument();
    });
  });

  /**
   * Test: Loading state is displayed
   */
  it('shows loading state initially', () => {
    render(<UserList />);
    
    expect(screen.getByText('Loading users...')).toBeInTheDocument();
  });

  /**
   * Test: Search functionality works
   */
  it('filters users by search query', async () => {
    const user = userEvent.setup();
    render(<UserList />);
    
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });
    
    const searchInput = screen.getByPlaceholderText(/search users by name or email/i);
    await user.type(searchInput, 'john');
    
    await waitFor(() => {
      expect(userService.getAllUsers).toHaveBeenCalledWith(
        { search: 'john' },
        1,
        20
      );
    }, { timeout: 1000 });
  });

  /**
   * Test: Role filter works correctly
   */
  it('filters users by role', async () => {
    const user = userEvent.setup();
    render(<UserList />);
    
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });
    
    // Open filters
    const filterButton = screen.getByText(/filters/i);
    await user.click(filterButton);
    
    // Select admin role
    const roleSelect = screen.getByDisplayValue('All Roles');
    await user.selectOptions(roleSelect, 'admin');
    
    expect(userService.getAllUsers).toHaveBeenCalledWith(
      { role: 'admin' },
      1,
      20
    );
  });

  /**
   * Test: Status filter works correctly
   */
  it('filters users by status', async () => {
    const user = userEvent.setup();
    render(<UserList />);
    
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });
    
    // Open filters
    const filterButton = screen.getByText(/filters/i);
    await user.click(filterButton);
    
    // Select inactive status
    const statusSelect = screen.getByDisplayValue('All Status');
    await user.selectOptions(statusSelect, 'false');
    
    expect(userService.getAllUsers).toHaveBeenCalledWith(
      { isActive: false },
      1,
      20
    );
  });

  /**
   * Test: Clear filters functionality
   */
  it('clears all filters when clear button is clicked', async () => {
    const user = userEvent.setup();
    render(<UserList />);
    
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });
    
    // Open filters and set some filters
    const filterButton = screen.getByText(/filters/i);
    await user.click(filterButton);
    
    const roleSelect = screen.getByDisplayValue('All Roles');
    await user.selectOptions(roleSelect, 'admin');
    
    // Clear filters
    const clearButton = screen.getByText('Clear Filters');
    await user.click(clearButton);
    
    expect(userService.getAllUsers).toHaveBeenCalledWith(
      {},
      1,
      20
    );
  });

  /**
   * Test: User selection callback works
   */
  it('calls onUserSelect when view button is clicked', async () => {
    const mockOnUserSelect = jest.fn();
    const user = userEvent.setup();
    
    render(<UserList onUserSelect={mockOnUserSelect} />);
    
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });
    
    const viewButtons = screen.getAllByText('View');
    await user.click(viewButtons[0]);
    
    expect(mockOnUserSelect).toHaveBeenCalledWith(mockUsers[0]);
  });

  /**
   * Test: User edit callback works
   */
  it('calls onUserEdit when edit button is clicked', async () => {
    const mockOnUserEdit = jest.fn();
    const user = userEvent.setup();
    
    render(<UserList onUserEdit={mockOnUserEdit} />);
    
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });
    
    const editButtons = screen.getAllByText('Edit');
    await user.click(editButtons[0]);
    
    expect(mockOnUserEdit).toHaveBeenCalledWith(mockUsers[0]);
  });

  /**
   * Test: User status toggle works
   */
  it('toggles user status when activate/deactivate is clicked', async () => {
    const user = userEvent.setup();
    (userService.toggleUserStatus as jest.Mock).mockResolvedValue(mockUsers[0]);
    
    render(<UserList />);
    
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });
    
    const deactivateButton = screen.getByText('Deactivate');
    await user.click(deactivateButton);
    
    expect(userService.toggleUserStatus).toHaveBeenCalledWith(1, false);
  });

  /**
   * Test: Bulk selection works
   */
  it('allows bulk selection of users', async () => {
    const user = userEvent.setup();
    
    render(<UserList />);
    
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });
    
    // Select all checkbox
    const selectAllCheckbox = screen.getByRole('checkbox', { name: '' });
    await user.click(selectAllCheckbox);
    
    expect(screen.getByText('3 user(s) selected')).toBeInTheDocument();
  });

  /**
   * Test: Bulk actions work
   */
  it('performs bulk activation', async () => {
    const user = userEvent.setup();
    (userService.toggleUserStatus as jest.Mock).mockResolvedValue({});
    
    render(<UserList />);
    
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });
    
    // Select all users
    const selectAllCheckbox = screen.getByRole('checkbox', { name: '' });
    await user.click(selectAllCheckbox);
    
    // Click bulk activate
    const activateButton = screen.getByText('Activate');
    await user.click(activateButton);
    
    expect(userService.toggleUserStatus).toHaveBeenCalledTimes(3);
  });

  /**
   * Test: Add user button is shown for admins
   */
  it('shows add user button for admin users', async () => {
    const mockOnUserCreate = jest.fn();
    
    render(<UserList onUserCreate={mockOnUserCreate} />);
    
    expect(screen.getByText('Add User')).toBeInTheDocument();
  });

  /**
   * Test: Add user button is hidden for non-admin users
   */
  it('hides add user button for non-admin users', async () => {
    (useAuth as jest.Mock).mockReturnValue({
      ...defaultAuthContextValue,
      user: { ...defaultAuthContextValue.user, role: 'employee' },
    });
    
    const mockOnUserCreate = jest.fn();
    
    render(<UserList onUserCreate={mockOnUserCreate} />);
    
    expect(screen.queryByText('Add User')).not.toBeInTheDocument();
  });

  /**
   * Test: Pagination works correctly
   */
  it('handles pagination correctly', async () => {
    const user = userEvent.setup();
    const paginatedResponse = {
      ...mockUserListResponse,
      pagination: {
        page: 1,
        limit: 20,
        total: 50,
        totalPages: 3,
      },
    };
    
    (userService.getAllUsers as jest.Mock).mockResolvedValue(paginatedResponse);
    
    render(<UserList />);
    
    await waitFor(() => {
      expect(screen.getByText('Page 1 of 3')).toBeInTheDocument();
    });
    
    const nextButton = screen.getByText('Next');
    await user.click(nextButton);
    
    expect(userService.getAllUsers).toHaveBeenCalledWith({}, 2, 20);
  });

  /**
   * Test: Error handling works
   */
  it('displays error message when loading fails', async () => {
    (userService.getAllUsers as jest.Mock).mockRejectedValue(new Error('Failed to load'));
    
    render(<UserList />);
    
    await waitFor(() => {
      expect(screen.getByText('Failed to load users. Please try again.')).toBeInTheDocument();
    });
  });

  /**
   * Test: Empty state is displayed when no users found
   */
  it('shows empty state when no users are found', async () => {
    (userService.getAllUsers as jest.Mock).mockResolvedValue({
      users: [],
      pagination: { page: 1, limit: 20, total: 0, totalPages: 0 },
    });
    
    render(<UserList />);
    
    await waitFor(() => {
      expect(screen.getByText('No users found')).toBeInTheDocument();
    });
  });
});
