/**
 * Dashboard service layer
 * Provides analytics and summary data for the dashboard
 */

const { query } = require('../../shared/database');
const { logger } = require('../../shared/logger');

/**
 * Get comprehensive dashboard data
 * @param {number} userId - User ID (for role-based filtering)
 * @param {string} userRole - User role
 * @param {Object} filters - Date filters
 * @returns {Object} Dashboard data
 */
const getDashboardData = async (userId, userRole, filters = {}) => {
  try {
    const { startDate, endDate } = filters;
    const isAdmin = userRole === 'admin';
    const isManager = userRole === 'manager';

    // Get user statistics
    const userStats = await getUserStatistics(isAdmin, isManager);
    
    // Get time tracking statistics
    const timeStats = await getTimeTrackingStatistics(userId, userRole, startDate, endDate);
    
    // Get approval statistics
    const approvalStats = await getApprovalStatistics(userId, userRole, startDate, endDate);
    
    // Get biometric statistics (if admin/manager)
    const biometricStats = (isAdmin || isManager) ? 
      await getBiometricStatistics(startDate, endDate) : null;
    
    // Get recent activities
    const recentActivities = await getRecentActivities(userId, userRole);
    
    // Get pending items that need attention
    const pendingItems = await getPendingItems(userId, userRole);

    return {
      userStats,
      timeStats,
      approvalStats,
      biometricStats,
      recentActivities,
      pendingItems,
      generatedAt: new Date().toISOString()
    };
  } catch (error) {
    logger.error('Error getting dashboard data:', error);
    throw new Error('Failed to retrieve dashboard data');
  }
};

/**
 * Get user statistics
 * @param {boolean} isAdmin - Is admin user
 * @param {boolean} isManager - Is manager user
 * @returns {Object} User statistics
 */
const getUserStatistics = async (isAdmin, isManager) => {
  try {
    if (!isAdmin && !isManager) {
      return null; // Regular employees don't see user stats
    }

    const stats = await query(`
      SELECT 
        COUNT(*) as total_users,
        SUM(CASE WHEN is_active = true THEN 1 ELSE 0 END) as active_users,
        SUM(CASE WHEN role = 'admin' THEN 1 ELSE 0 END) as admin_count,
        SUM(CASE WHEN role = 'manager' THEN 1 ELSE 0 END) as manager_count,
        SUM(CASE WHEN role = 'employee' THEN 1 ELSE 0 END) as employee_count,
        SUM(CASE WHEN last_login >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 ELSE 0 END) as active_last_week,
        SUM(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) as new_this_month
      FROM users
    `);

    return stats[0];
  } catch (error) {
    logger.error('Error getting user statistics:', error);
    return null;
  }
};

/**
 * Get time tracking statistics
 * @param {number} userId - User ID
 * @param {string} userRole - User role
 * @param {string} startDate - Start date filter
 * @param {string} endDate - End date filter
 * @returns {Object} Time tracking statistics
 */
const getTimeTrackingStatistics = async (userId, userRole, startDate, endDate) => {
  try {
    let whereClause = 'WHERE tl.clock_out_time IS NOT NULL';
    const params = [];

    // Role-based filtering
    if (userRole === 'employee') {
      whereClause += ' AND tl.user_id = ?';
      params.push(userId);
    }

    // Date filtering
    if (startDate) {
      whereClause += ' AND DATE(tl.clock_in_time) >= ?';
      params.push(startDate);
    }

    if (endDate) {
      whereClause += ' AND DATE(tl.clock_in_time) <= ?';
      params.push(endDate);
    }

    const stats = await query(`
      SELECT 
        COUNT(*) as total_sessions,
        SUM(tl.hours_worked) as total_hours,
        AVG(tl.hours_worked) as avg_hours_per_session,
        COUNT(DISTINCT tl.user_id) as unique_users,
        SUM(CASE WHEN tl.biometric_verified = true THEN 1 ELSE 0 END) as biometric_verified_sessions,
        SUM(CASE WHEN DATE(tl.clock_in_time) = CURDATE() THEN 1 ELSE 0 END) as sessions_today,
        SUM(CASE WHEN DATE(tl.clock_in_time) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) THEN tl.hours_worked ELSE 0 END) as hours_this_week
      FROM time_logs tl
      ${whereClause}
    `, params);

    // Get active sessions count
    const activeSessions = await query(`
      SELECT COUNT(*) as active_sessions
      FROM time_logs tl
      WHERE tl.clock_out_time IS NULL
      ${userRole === 'employee' ? 'AND tl.user_id = ?' : ''}
    `, userRole === 'employee' ? [userId] : []);

    return {
      ...stats[0],
      active_sessions: activeSessions[0].active_sessions
    };
  } catch (error) {
    logger.error('Error getting time tracking statistics:', error);
    return null;
  }
};

/**
 * Get approval statistics
 * @param {number} userId - User ID
 * @param {string} userRole - User role
 * @param {string} startDate - Start date filter
 * @param {string} endDate - End date filter
 * @returns {Object} Approval statistics
 */
const getApprovalStatistics = async (userId, userRole, startDate, endDate) => {
  try {
    let whereClause = 'WHERE 1=1';
    const params = [];

    // Role-based filtering
    if (userRole === 'employee') {
      whereClause += ' AND requested_by = ?';
      params.push(userId);
    } else if (userRole === 'manager') {
      whereClause += ' AND (assigned_to = ? OR requested_by = ?)';
      params.push(userId, userId);
    }

    // Date filtering
    if (startDate) {
      whereClause += ' AND DATE(created_at) >= ?';
      params.push(startDate);
    }

    if (endDate) {
      whereClause += ' AND DATE(created_at) <= ?';
      params.push(endDate);
    }

    const stats = await query(`
      SELECT 
        COUNT(*) as total_requests,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_requests,
        SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved_requests,
        SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected_requests,
        SUM(CASE WHEN request_type = 'correction' THEN 1 ELSE 0 END) as correction_requests,
        AVG(CASE WHEN approved_at IS NOT NULL 
            THEN TIMESTAMPDIFF(HOUR, created_at, approved_at) 
            ELSE NULL END) as avg_processing_time_hours
      FROM approvals
      ${whereClause}
    `, params);

    return stats[0];
  } catch (error) {
    logger.error('Error getting approval statistics:', error);
    return null;
  }
};

/**
 * Get biometric statistics
 * @param {string} startDate - Start date filter
 * @param {string} endDate - End date filter
 * @returns {Object} Biometric statistics
 */
const getBiometricStatistics = async (startDate, endDate) => {
  try {
    let whereClause = 'WHERE used_for_verification = false';
    const params = [];

    if (startDate) {
      whereClause += ' AND DATE(created_at) >= ?';
      params.push(startDate);
    }

    if (endDate) {
      whereClause += ' AND DATE(created_at) <= ?';
      params.push(endDate);
    }

    const stats = await query(`
      SELECT 
        COUNT(*) as total_templates,
        COUNT(DISTINCT user_id) as users_with_biometrics,
        SUM(CASE WHEN template_type = 'fingerprint' THEN 1 ELSE 0 END) as fingerprint_templates,
        SUM(CASE WHEN template_type = 'face' THEN 1 ELSE 0 END) as face_templates,
        AVG(quality) as avg_quality
      FROM biometric_data
      ${whereClause}
    `, params);

    return stats[0];
  } catch (error) {
    logger.error('Error getting biometric statistics:', error);
    return null;
  }
};

/**
 * Get recent activities
 * @param {number} userId - User ID
 * @param {string} userRole - User role
 * @returns {Array} Recent activities
 */
const getRecentActivities = async (userId, userRole) => {
  try {
    let whereClause = 'WHERE 1=1';
    const params = [];

    // Role-based filtering for activities
    if (userRole === 'employee') {
      whereClause += ' AND user_id = ?';
      params.push(userId);
    }

    const activities = await query(`
      SELECT 
        al.id,
        al.user_id,
        al.action,
        al.resource_type,
        al.resource_id,
        al.details,
        al.created_at,
        u.first_name,
        u.last_name,
        u.email
      FROM audit_logs al
      JOIN users u ON al.user_id = u.id
      ${whereClause}
      ORDER BY al.created_at DESC
      LIMIT 10
    `, params);

    // Parse details for each activity
    activities.forEach(activity => {
      try {
        activity.details = JSON.parse(activity.details || '{}');
      } catch (error) {
        activity.details = {};
      }
    });

    return activities;
  } catch (error) {
    logger.error('Error getting recent activities:', error);
    return [];
  }
};

/**
 * Get pending items that need attention
 * @param {number} userId - User ID
 * @param {string} userRole - User role
 * @returns {Object} Pending items
 */
const getPendingItems = async (userId, userRole) => {
  try {
    const pendingItems = {
      approvals: [],
      activeSessions: [],
      expiredSessions: []
    };

    // Get pending approvals
    if (userRole === 'manager' || userRole === 'admin') {
      const approvals = await query(`
        SELECT 
          a.id,
          a.time_log_id,
          a.request_type,
          a.reason,
          a.created_at,
          u.first_name,
          u.last_name,
          u.email,
          tl.clock_in_time,
          tl.clock_out_time
        FROM approvals a
        JOIN users u ON a.requested_by = u.id
        JOIN time_logs tl ON a.time_log_id = tl.id
        WHERE a.status = 'pending'
        ${userRole === 'manager' ? 'AND a.assigned_to = ?' : ''}
        ORDER BY a.created_at ASC
        LIMIT 5
      `, userRole === 'manager' ? [userId] : []);

      pendingItems.approvals = approvals;
    }

    // Get active sessions (for managers/admins or own session for employees)
    const activeSessionsQuery = userRole === 'employee' 
      ? 'SELECT tl.id, tl.user_id, tl.clock_in_time, u.first_name, u.last_name FROM time_logs tl JOIN users u ON tl.user_id = u.id WHERE tl.clock_out_time IS NULL AND tl.user_id = ? ORDER BY tl.clock_in_time DESC LIMIT 5'
      : 'SELECT tl.id, tl.user_id, tl.clock_in_time, u.first_name, u.last_name FROM time_logs tl JOIN users u ON tl.user_id = u.id WHERE tl.clock_out_time IS NULL ORDER BY tl.clock_in_time DESC LIMIT 10';

    const activeSessions = await query(
      activeSessionsQuery,
      userRole === 'employee' ? [userId] : []
    );

    pendingItems.activeSessions = activeSessions;

    // Get expired sessions (sessions longer than 12 hours without clock out)
    if (userRole === 'manager' || userRole === 'admin') {
      const expiredSessions = await query(`
        SELECT 
          tl.id,
          tl.user_id,
          tl.clock_in_time,
          u.first_name,
          u.last_name,
          u.email
        FROM time_logs tl
        JOIN users u ON tl.user_id = u.id
        WHERE tl.clock_out_time IS NULL 
        AND tl.clock_in_time < DATE_SUB(NOW(), INTERVAL 12 HOUR)
        ORDER BY tl.clock_in_time ASC
        LIMIT 5
      `);

      pendingItems.expiredSessions = expiredSessions;
    }

    return pendingItems;
  } catch (error) {
    logger.error('Error getting pending items:', error);
    return {
      approvals: [],
      activeSessions: [],
      expiredSessions: []
    };
  }
};

/**
 * Get time tracking trends
 * @param {number} userId - User ID
 * @param {string} userRole - User role
 * @param {number} days - Number of days to analyze
 * @returns {Array} Daily time tracking data
 */
const getTimeTrackingTrends = async (userId, userRole, days = 7) => {
  try {
    let whereClause = 'WHERE tl.clock_out_time IS NOT NULL';
    const params = [];

    // Role-based filtering
    if (userRole === 'employee') {
      whereClause += ' AND tl.user_id = ?';
      params.push(userId);
    }

    whereClause += ' AND DATE(tl.clock_in_time) >= DATE_SUB(CURDATE(), INTERVAL ? DAY)';
    params.push(days);

    const trends = await query(`
      SELECT 
        DATE(tl.clock_in_time) as date,
        COUNT(*) as sessions,
        SUM(tl.hours_worked) as total_hours,
        AVG(tl.hours_worked) as avg_hours,
        COUNT(DISTINCT tl.user_id) as unique_users
      FROM time_logs tl
      ${whereClause}
      GROUP BY DATE(tl.clock_in_time)
      ORDER BY date ASC
    `, params);

    return trends;
  } catch (error) {
    logger.error('Error getting time tracking trends:', error);
    return [];
  }
};

module.exports = {
  getDashboardData,
  getTimeTrackingTrends
};