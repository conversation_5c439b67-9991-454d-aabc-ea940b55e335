/**
 * Biometric integration controller
 * Handles HTTP requests for biometric operations
 */

const biometricService = require('./biometricService');
const { logger } = require('../../shared/logger');

/**
 * Enroll biometric template
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const enrollBiometricTemplate = async (req, res) => {
  try {
    const userId = req.user.id;
    const templateData = req.body;

    const result = await biometricService.enrollBiometricTemplate(userId, templateData);

    logger.info(`Biometric template enrolled for user ${req.user.email}`);

    res.status(201).json({
      success: true,
      message: 'Biometric template enrolled successfully',
      ...result
    });
  } catch (error) {
    logger.error('Enroll biometric template error:', error);
    
    if (error.message === 'Invalid template type. Must be fingerprint or face.') {
      return res.status(400).json({
        error: error.message,
        code: 'INVALID_TEMPLATE_TYPE'
      });
    }

    res.status(500).json({
      error: 'Failed to enroll biometric template',
      code: 'BIOMETRIC_ENROLL_ERROR'
    });
  }
};

/**
 * Verify biometric template
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const verifyBiometricTemplate = async (req, res) => {
  try {
    const userId = req.user.id;
    const verificationData = req.body;

    const result = await biometricService.verifyBiometricTemplate(userId, verificationData);

    res.json({
      success: true,
      ...result
    });
  } catch (error) {
    logger.error('Verify biometric template error:', error);
    res.status(500).json({
      error: 'Failed to verify biometric template',
      code: 'BIOMETRIC_VERIFY_ERROR'
    });
  }
};

/**
 * Get user's biometric templates
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getUserBiometricTemplates = async (req, res) => {
  try {
    const userId = req.user.id;
    const templates = await biometricService.getUserBiometricTemplates(userId);

    res.json({
      success: true,
      templates
    });
  } catch (error) {
    logger.error('Get user biometric templates error:', error);
    res.status(500).json({
      error: 'Failed to retrieve biometric templates',
      code: 'BIOMETRIC_TEMPLATES_ERROR'
    });
  }
};

/**
 * Get biometric templates for specific user (admin/manager only)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getBiometricTemplatesForUser = async (req, res) => {
  try {
    const { userId } = req.params;
    const templates = await biometricService.getUserBiometricTemplates(parseInt(userId));

    res.json({
      success: true,
      templates
    });
  } catch (error) {
    logger.error('Get biometric templates for user error:', error);
    res.status(500).json({
      error: 'Failed to retrieve biometric templates',
      code: 'BIOMETRIC_TEMPLATES_ERROR'
    });
  }
};

/**
 * Delete biometric template
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const deleteBiometricTemplate = async (req, res) => {
  try {
    const userId = req.user.id;
    const { templateType } = req.params;

    await biometricService.deleteBiometricTemplate(userId, templateType);

    logger.info(`Biometric template deleted for user ${req.user.email}, type: ${templateType}`);

    res.json({
      success: true,
      message: 'Biometric template deleted successfully'
    });
  } catch (error) {
    logger.error('Delete biometric template error:', error);
    
    if (error.message === 'Template not found or cannot be deleted') {
      return res.status(404).json({
        error: error.message,
        code: 'TEMPLATE_NOT_FOUND'
      });
    }

    res.status(500).json({
      error: 'Failed to delete biometric template',
      code: 'BIOMETRIC_DELETE_ERROR'
    });
  }
};

/**
 * Delete biometric template for specific user (admin only)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const deleteBiometricTemplateForUser = async (req, res) => {
  try {
    const { userId, templateType } = req.params;

    await biometricService.deleteBiometricTemplate(parseInt(userId), templateType);

    logger.info(`Biometric template deleted by ${req.user.email} for user ID ${userId}, type: ${templateType}`);

    res.json({
      success: true,
      message: 'Biometric template deleted successfully'
    });
  } catch (error) {
    logger.error('Delete biometric template for user error:', error);
    
    if (error.message === 'Template not found or cannot be deleted') {
      return res.status(404).json({
        error: error.message,
        code: 'TEMPLATE_NOT_FOUND'
      });
    }

    res.status(500).json({
      error: 'Failed to delete biometric template',
      code: 'BIOMETRIC_DELETE_ERROR'
    });
  }
};

/**
 * Get biometric statistics (admin/manager only)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getBiometricStats = async (req, res) => {
  try {
    const { startDate, endDate } = req.query;
    const filters = {};

    if (startDate) filters.startDate = startDate;
    if (endDate) filters.endDate = endDate;

    const stats = await biometricService.getBiometricStats(filters);

    res.json({
      success: true,
      stats
    });
  } catch (error) {
    logger.error('Get biometric stats error:', error);
    res.status(500).json({
      error: 'Failed to retrieve biometric statistics',
      code: 'BIOMETRIC_STATS_ERROR'
    });
  }
};

/**
 * Get biometric verification history
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getBiometricHistory = async (req, res) => {
  try {
    const userId = req.user.id;
    const { page = 1, limit = 20 } = req.query;

    const result = await biometricService.getBiometricHistory(
      userId,
      parseInt(page),
      parseInt(limit)
    );

    res.json({
      success: true,
      ...result
    });
  } catch (error) {
    logger.error('Get biometric history error:', error);
    res.status(500).json({
      error: 'Failed to retrieve biometric history',
      code: 'BIOMETRIC_HISTORY_ERROR'
    });
  }
};

/**
 * Get biometric verification history for specific user (admin/manager only)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getBiometricHistoryForUser = async (req, res) => {
  try {
    const { userId } = req.params;
    const { page = 1, limit = 20 } = req.query;

    const result = await biometricService.getBiometricHistory(
      parseInt(userId),
      parseInt(page),
      parseInt(limit)
    );

    res.json({
      success: true,
      ...result
    });
  } catch (error) {
    logger.error('Get biometric history for user error:', error);
    res.status(500).json({
      error: 'Failed to retrieve biometric history',
      code: 'BIOMETRIC_HISTORY_ERROR'
    });
  }
};

/**
 * Verify biometric for another user (admin/manager only)
 * Used for administrative verification
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const verifyBiometricForUser = async (req, res) => {
  try {
    const { userId } = req.params;
    const verificationData = req.body;

    const result = await biometricService.verifyBiometricTemplate(
      parseInt(userId),
      verificationData
    );

    logger.info(`Biometric verification performed by ${req.user.email} for user ID ${userId}`);

    res.json({
      success: true,
      ...result
    });
  } catch (error) {
    logger.error('Verify biometric for user error:', error);
    res.status(500).json({
      error: 'Failed to verify biometric template',
      code: 'BIOMETRIC_VERIFY_ERROR'
    });
  }
};

module.exports = {
  enrollBiometricTemplate,
  verifyBiometricTemplate,
  getUserBiometricTemplates,
  getBiometricTemplatesForUser,
  deleteBiometricTemplate,
  deleteBiometricTemplateForUser,
  getBiometricStats,
  getBiometricHistory,
  getBiometricHistoryForUser,
  verifyBiometricForUser
};