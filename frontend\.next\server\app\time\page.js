/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/time/page";
exports.ids = ["app/time/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ftime%2Fpage&page=%2Ftime%2Fpage&appPaths=%2Ftime%2Fpage&pagePath=private-next-app-dir%2Ftime%2Fpage.tsx&appDir=C%3A%5CUsers%5Cijureidini%5CWorkspace%5Cflexair_timekeeping_app%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cijureidini%5CWorkspace%5Cflexair_timekeeping_app%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ftime%2Fpage&page=%2Ftime%2Fpage&appPaths=%2Ftime%2Fpage&pagePath=private-next-app-dir%2Ftime%2Fpage.tsx&appDir=C%3A%5CUsers%5Cijureidini%5CWorkspace%5Cflexair_timekeeping_app%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cijureidini%5CWorkspace%5Cflexair_timekeeping_app%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?41d8\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/../node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/../node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'time',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/time/page.tsx */ \"(rsc)/./src/app/time/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\time\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/../node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\time\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/time/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/time/page\",\n        pathname: \"/time\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ftime%2Fpage&page=%2Ftime%2Fpage&appPaths=%2Ftime%2Fpage&pagePath=private-next-app-dir%2Ftime%2Fpage.tsx&appDir=C%3A%5CUsers%5Cijureidini%5CWorkspace%5Cflexair_timekeeping_app%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cijureidini%5CWorkspace%5Cflexair_timekeeping_app%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Ctime%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Ctime%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/time/page.tsx */ \"(ssr)/./src/app/time/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNpanVyZWlkaW5pJTVDJTVDV29ya3NwYWNlJTVDJTVDZmxleGFpcl90aW1la2VlcGluZ19hcHAlNUMlNUNmcm9udGVuZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3RpbWUlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsMEpBQWtJIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZmxleGFpci1mcm9udGVuZC8/ZGI3MiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXGlqdXJlaWRpbmlcXFxcV29ya3NwYWNlXFxcXGZsZXhhaXJfdGltZWtlZXBpbmdfYXBwXFxcXGZyb250ZW5kXFxcXHNyY1xcXFxhcHBcXFxcdGltZVxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Ctime%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(ssr)/./src/contexts/AuthContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNpanVyZWlkaW5pJTVDJTVDV29ya3NwYWNlJTVDJTVDZmxleGFpcl90aW1la2VlcGluZ19hcHAlNUMlNUNmcm9udGVuZCU1QyU1Q3NyYyU1QyU1Q2NvbnRleHRzJTVDJTVDQXV0aENvbnRleHQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQXV0aFByb3ZpZGVyJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2lqdXJlaWRpbmklNUMlNUNXb3Jrc3BhY2UlNUMlNUNmbGV4YWlyX3RpbWVrZWVwaW5nX2FwcCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNmb250JTVDJTVDZ29vZ2xlJTVDJTVDdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJzcmMlNUMlNUMlNUMlNUNhcHAlNUMlNUMlNUMlNUNsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIySW50ZXIlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJpbnRlciU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNpanVyZWlkaW5pJTVDJTVDV29ya3NwYWNlJTVDJTVDZmxleGFpcl90aW1la2VlcGluZ19hcHAlNUMlNUNmcm9udGVuZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3S0FBMEsiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mbGV4YWlyLWZyb250ZW5kLz82NjM5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiQXV0aFByb3ZpZGVyXCJdICovIFwiQzpcXFxcVXNlcnNcXFxcaWp1cmVpZGluaVxcXFxXb3Jrc3BhY2VcXFxcZmxleGFpcl90aW1la2VlcGluZ19hcHBcXFxcZnJvbnRlbmRcXFxcc3JjXFxcXGNvbnRleHRzXFxcXEF1dGhDb250ZXh0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/app-router.js */ \"(ssr)/../node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/client-page.js */ \"(ssr)/../node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/../node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/../node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/../node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/../node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cijureidini%5C%5CWorkspace%5C%5Cflexair_timekeeping_app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/time/page.tsx":
/*!*******************************!*\
  !*** ./src/app/time/page.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/auth/ProtectedRoute */ \"(ssr)/./src/components/auth/ProtectedRoute.tsx\");\n/* harmony import */ var _components_time_ClockInOut__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/time/ClockInOut */ \"(ssr)/./src/components/time/ClockInOut.tsx\");\n/* harmony import */ var _components_time_TimeLogs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/time/TimeLogs */ \"(ssr)/./src/components/time/TimeLogs.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n/**\r\n * Time Tracking page - main interface for employee time management\r\n * Combines clock in/out functionality with time logs viewing\r\n */ function TimeTrackingPage() {\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"clock\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-100 py-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"Time Tracking\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\time\\\\page.tsx\",\n                                lineNumber: 21,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1 text-gray-600\",\n                                children: \"Manage your work hours, clock in/out, and view your time logs\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\time\\\\page.tsx\",\n                                lineNumber: 22,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\time\\\\page.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"flex space-x-8\",\n                            \"aria-label\": \"Tabs\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab(\"clock\"),\n                                    className: `py-2 px-1 border-b-2 font-medium text-sm ${activeTab === \"clock\" ? \"border-blue-500 text-blue-600\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"}`,\n                                    children: \"Time Clock\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\time\\\\page.tsx\",\n                                    lineNumber: 30,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab(\"logs\"),\n                                    className: `py-2 px-1 border-b-2 font-medium text-sm ${activeTab === \"logs\" ? \"border-blue-500 text-blue-600\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"}`,\n                                    children: \"Time Logs\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\time\\\\page.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\time\\\\page.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\time\\\\page.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"transition-all duration-300\",\n                        children: [\n                            activeTab === \"clock\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_time_ClockInOut__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\time\\\\page.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 39\n                            }, this),\n                            activeTab === \"logs\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_time_TimeLogs__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\time\\\\page.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 38\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\time\\\\page.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 compact-card\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-800 mb-3\",\n                                children: \"Quick Actions\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\time\\\\page.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveTab(\"clock\"),\n                                        className: \"p-3 text-left bg-blue-50 hover:bg-blue-100 border border-blue-200 rounded-lg transition duration-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"icon-container bg-blue-600 mr-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 text-white\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: \"2\",\n                                                            d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\time\\\\page.tsx\",\n                                                            lineNumber: 70,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\time\\\\page.tsx\",\n                                                        lineNumber: 69,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\time\\\\page.tsx\",\n                                                    lineNumber: 68,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold text-blue-900\",\n                                                            children: \"Time Clock\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\time\\\\page.tsx\",\n                                                            lineNumber: 74,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-blue-600\",\n                                                            children: \"Clock in or out of your shift\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\time\\\\page.tsx\",\n                                                            lineNumber: 75,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\time\\\\page.tsx\",\n                                                    lineNumber: 73,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\time\\\\page.tsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\time\\\\page.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveTab(\"logs\"),\n                                        className: \"p-3 text-left bg-green-50 hover:bg-green-100 border border-green-200 rounded-lg transition duration-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"icon-container bg-green-600 mr-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 text-white\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: \"2\",\n                                                            d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\time\\\\page.tsx\",\n                                                            lineNumber: 87,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\time\\\\page.tsx\",\n                                                        lineNumber: 86,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\time\\\\page.tsx\",\n                                                    lineNumber: 85,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold text-green-900\",\n                                                            children: \"View Time Logs\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\time\\\\page.tsx\",\n                                                            lineNumber: 91,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-green-600\",\n                                                            children: \"Review your time entries and hours\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\time\\\\page.tsx\",\n                                                            lineNumber: 92,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\time\\\\page.tsx\",\n                                                    lineNumber: 90,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\time\\\\page.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\time\\\\page.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/dashboard\",\n                                        className: \"p-3 text-left bg-purple-50 hover:bg-purple-100 border border-purple-200 rounded-lg transition duration-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"icon-container bg-purple-600 mr-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 text-white\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: \"2\",\n                                                                d: \"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\time\\\\page.tsx\",\n                                                                lineNumber: 104,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: \"2\",\n                                                                d: \"M8 5a2 2 0 012-2h4a2 2 0 012 2v2H8V5z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\time\\\\page.tsx\",\n                                                                lineNumber: 105,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\time\\\\page.tsx\",\n                                                        lineNumber: 103,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\time\\\\page.tsx\",\n                                                    lineNumber: 102,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold text-purple-900\",\n                                                            children: \"Dashboard\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\time\\\\page.tsx\",\n                                                            lineNumber: 109,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-purple-600\",\n                                                            children: \"View summary and reports\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\time\\\\page.tsx\",\n                                                            lineNumber: 110,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\time\\\\page.tsx\",\n                                                    lineNumber: 108,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\time\\\\page.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\time\\\\page.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\time\\\\page.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\time\\\\page.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\time\\\\page.tsx\",\n                lineNumber: 18,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\time\\\\page.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\time\\\\page.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TimeTrackingPage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/time/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/approvals/ApprovalRequestForm.tsx":
/*!**********************************************************!*\
  !*** ./src/components/approvals/ApprovalRequestForm.tsx ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ApprovalRequestForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_approvals__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/approvals */ \"(ssr)/./src/lib/approvals.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n/**\n * ApprovalRequestForm Component\n * Form for employees to submit approval requests for time corrections, overtime, or leave\n */ function ApprovalRequestForm({ timeLogId, currentClockIn = \"\", currentClockOut = \"\", onSuccess, onCancel }) {\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        requestType: \"correction\",\n        reason: \"\",\n        clockIn: currentClockIn,\n        clockOut: currentClockOut,\n        overtimeHours: \"\",\n        overtimeDate: \"\",\n        leaveStartDate: \"\",\n        leaveEndDate: \"\",\n        leaveType: \"sick\"\n    });\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    /**\n   * Handle form field changes\n   */ const handleChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n        // Clear error when user starts typing\n        if (error) {\n            setError(null);\n        }\n    };\n    /**\n   * Validate form data\n   */ const validateForm = ()=>{\n        if (!formData.reason.trim()) {\n            return \"Reason is required\";\n        }\n        if (formData.requestType === \"correction\") {\n            if (!formData.clockIn) {\n                return \"Clock in time is required for corrections\";\n            }\n            if (!formData.clockOut) {\n                return \"Clock out time is required for corrections\";\n            }\n        }\n        if (formData.requestType === \"overtime\") {\n            if (!formData.overtimeHours || parseFloat(formData.overtimeHours) <= 0) {\n                return \"Valid overtime hours are required\";\n            }\n            if (!formData.overtimeDate) {\n                return \"Overtime date is required\";\n            }\n        }\n        if (formData.requestType === \"leave\") {\n            if (!formData.leaveStartDate) {\n                return \"Leave start date is required\";\n            }\n            if (!formData.leaveEndDate) {\n                return \"Leave end date is required\";\n            }\n            if (new Date(formData.leaveStartDate) > new Date(formData.leaveEndDate)) {\n                return \"Leave start date must be before end date\";\n            }\n        }\n        return null;\n    };\n    /**\n   * Prepare request data based on request type\n   */ const prepareRequestData = ()=>{\n        switch(formData.requestType){\n            case \"correction\":\n                return {\n                    clockIn: formData.clockIn,\n                    clockOut: formData.clockOut\n                };\n            case \"overtime\":\n                return {\n                    hours: parseFloat(formData.overtimeHours),\n                    date: formData.overtimeDate\n                };\n            case \"leave\":\n                return {\n                    startDate: formData.leaveStartDate,\n                    endDate: formData.leaveEndDate,\n                    type: formData.leaveType\n                };\n            default:\n                return {};\n        }\n    };\n    /**\n   * Handle form submission\n   */ const handleSubmit = async (e)=>{\n        e.preventDefault();\n        const validationError = validateForm();\n        if (validationError) {\n            setError(validationError);\n            return;\n        }\n        setIsSubmitting(true);\n        setError(null);\n        try {\n            const requestData = {\n                timeLogId,\n                requestType: formData.requestType,\n                requestData: prepareRequestData(),\n                reason: formData.reason.trim()\n            };\n            await (0,_lib_approvals__WEBPACK_IMPORTED_MODULE_2__.createApprovalRequest)(requestData);\n            if (onSuccess) {\n                onSuccess();\n            }\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"Failed to submit approval request\");\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"compact-card\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-gray-900\",\n                        children: \"Request Approval\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 9\n                    }, this),\n                    onCancel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onCancel,\n                        className: \"text-gray-400 hover:text-gray-600\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-5 h-5\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M6 18L18 6M6 6l12 12\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                lineNumber: 178,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-3 p-2 bg-red-50 border border-red-200 rounded-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-red-600\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                    lineNumber: 196,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                lineNumber: 195,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"requestType\",\n                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                children: \"Request Type\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                id: \"requestType\",\n                                name: \"requestType\",\n                                value: formData.requestType,\n                                onChange: handleChange,\n                                className: \"form-input\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"correction\",\n                                        children: \"Time Correction\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"overtime\",\n                                        children: \"Overtime Request\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"leave\",\n                                        children: \"Leave Request\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 9\n                    }, this),\n                    formData.requestType === \"correction\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"clockIn\",\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"Corrected Clock In Time\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"datetime-local\",\n                                        id: \"clockIn\",\n                                        name: \"clockIn\",\n                                        value: formData.clockIn,\n                                        onChange: handleChange,\n                                        className: \"form-input\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"clockOut\",\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"Corrected Clock Out Time\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"datetime-local\",\n                                        id: \"clockOut\",\n                                        name: \"clockOut\",\n                                        value: formData.clockOut,\n                                        onChange: handleChange,\n                                        className: \"form-input\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 11\n                    }, this),\n                    formData.requestType === \"overtime\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"overtimeHours\",\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"Overtime Hours\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"number\",\n                                        id: \"overtimeHours\",\n                                        name: \"overtimeHours\",\n                                        value: formData.overtimeHours,\n                                        onChange: handleChange,\n                                        min: \"0\",\n                                        step: \"0.5\",\n                                        placeholder: \"e.g., 2.5\",\n                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"overtimeDate\",\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"Overtime Date\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"date\",\n                                        id: \"overtimeDate\",\n                                        name: \"overtimeDate\",\n                                        value: formData.overtimeDate,\n                                        onChange: handleChange,\n                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                        lineNumber: 253,\n                        columnNumber: 11\n                    }, this),\n                    formData.requestType === \"leave\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"leaveStartDate\",\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Start Date\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"date\",\n                                                id: \"leaveStartDate\",\n                                                name: \"leaveStartDate\",\n                                                value: formData.leaveStartDate,\n                                                onChange: handleChange,\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"leaveEndDate\",\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"End Date\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                                lineNumber: 304,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"date\",\n                                                id: \"leaveEndDate\",\n                                                name: \"leaveEndDate\",\n                                                value: formData.leaveEndDate,\n                                                onChange: handleChange,\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"leaveType\",\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"Leave Type\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        id: \"leaveType\",\n                                        name: \"leaveType\",\n                                        value: formData.leaveType,\n                                        onChange: handleChange,\n                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"sick\",\n                                                children: \"Sick Leave\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                                lineNumber: 328,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"vacation\",\n                                                children: \"Vacation\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"personal\",\n                                                children: \"Personal Leave\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"emergency\",\n                                                children: \"Emergency Leave\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                                lineNumber: 331,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"reason\",\n                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                children: [\n                                    \"Reason for Request \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-red-500\",\n                                        children: \"*\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                        lineNumber: 340,\n                                        columnNumber: 32\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                id: \"reason\",\n                                name: \"reason\",\n                                value: formData.reason,\n                                onChange: handleChange,\n                                rows: 4,\n                                placeholder: \"Please provide a detailed reason for this request...\",\n                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                lineNumber: 342,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                        lineNumber: 338,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-end space-x-3 pt-4\",\n                        children: [\n                            onCancel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: onCancel,\n                                className: \"btn btn-outline\",\n                                children: \"Cancel\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                lineNumber: 356,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                disabled: isSubmitting,\n                                className: \"btn btn-primary\",\n                                children: isSubmitting ? \"Submitting...\" : \"Submit Request\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                lineNumber: 364,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                        lineNumber: 354,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                lineNumber: 200,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n        lineNumber: 177,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/approvals/ApprovalRequestForm.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/auth/ProtectedRoute.tsx":
/*!************************************************!*\
  !*** ./src/components/auth/ProtectedRoute.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProtectedRoute),\n/* harmony export */   withAuth: () => (/* binding */ withAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/../node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/**\r\n * Protected Route Component\r\n * Wraps components that require authentication\r\n */ /* __next_internal_client_entry_do_not_use__ default,withAuth auto */ \n\n\n\n/**\r\n * Protected route component that handles authentication and authorization\r\n * @param children - Components to render if user is authorized\r\n * @param requireAuth - Whether authentication is required (default: true)\r\n * @param requiredRole - Minimum role required to access the route\r\n * @param redirectTo - URL to redirect to if not authorized\r\n * @param fallback - Component to show while loading\r\n */ function ProtectedRoute({ children, requireAuth = true, requiredRole, redirectTo = \"/auth/login\", fallback }) {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user, loading, isAuthenticated } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (loading) return; // Wait for auth state to be determined\n        // If authentication is required but user is not authenticated\n        if (requireAuth && !isAuthenticated) {\n            router.push(redirectTo);\n            return;\n        }\n        // If specific role is required, check user role\n        if (requiredRole && user && !hasRequiredRole(user.role, requiredRole)) {\n            router.push(\"/unauthorized\");\n            return;\n        }\n    }, [\n        loading,\n        isAuthenticated,\n        user,\n        requireAuth,\n        requiredRole,\n        router,\n        redirectTo\n    ]);\n    /**\r\n   * Check if user has required role\r\n   * @param userRole - Current user's role\r\n   * @param requiredRole - Required role\r\n   * @returns True if user has sufficient role\r\n   */ const hasRequiredRole = (userRole, requiredRole)=>{\n        const roleHierarchy = {\n            admin: 3,\n            manager: 2,\n            employee: 1\n        };\n        return roleHierarchy[userRole] >= roleHierarchy[requiredRole];\n    };\n    // Show loading state\n    if (loading) {\n        return fallback || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoadingSpinner, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n            lineNumber: 75,\n            columnNumber: 24\n        }, this);\n    }\n    // Show unauthorized if auth is required but user is not authenticated\n    if (requireAuth && !isAuthenticated) {\n        return null; // Router will handle redirect\n    }\n    // Show unauthorized if role is required but user doesn't have it\n    if (requiredRole && user && !hasRequiredRole(user.role, requiredRole)) {\n        return null; // Router will handle redirect\n    }\n    // Render children if all checks pass\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n/**\r\n * Loading spinner component\r\n */ function LoadingSpinner() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"animate-spin -ml-1 mr-3 h-6 w-6 text-indigo-600\",\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                            className: \"opacity-25\",\n                            cx: \"12\",\n                            cy: \"12\",\n                            r: \"10\",\n                            stroke: \"currentColor\",\n                            strokeWidth: \"4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            className: \"opacity-75\",\n                            fill: \"currentColor\",\n                            d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mt-2 text-sm text-gray-600\",\n                    children: \"Loading...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                    lineNumber: 119,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n            lineNumber: 98,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n        lineNumber: 97,\n        columnNumber: 5\n    }, this);\n}\n/**\r\n * Higher-order component for protecting pages\r\n * @param Component - Component to protect\r\n * @param options - Protection options\r\n * @returns Protected component\r\n */ function withAuth(Component, options = {}) {\n    const ProtectedComponent = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProtectedRoute, {\n            ...options,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...props\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n            lineNumber: 136,\n            columnNumber: 5\n        }, this);\n    ProtectedComponent.displayName = `withAuth(${Component.displayName || Component.name})`;\n    return ProtectedComponent;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/auth/ProtectedRoute.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/time/ClockInOut.tsx":
/*!********************************************!*\
  !*** ./src/components/time/ClockInOut.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ClockInOut)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/auth */ \"(ssr)/./src/lib/auth.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n/**\r\n * Clock In/Out component for time tracking\r\n * Allows employees to clock in/out and view current session info\r\n */ function ClockInOut() {\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [activeSession, setActiveSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [timeStats, setTimeStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [location, setLocation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isDetectingLocation, setIsDetectingLocation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [coordinates, setCoordinates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Update current time every second\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timer = setInterval(()=>{\n            setCurrentTime(new Date());\n        }, 1000);\n        return ()=>clearInterval(timer);\n    }, []);\n    // Load active session and stats on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadActiveSession();\n        loadTimeStats();\n    }, []);\n    // Note: Removed auto-detection to prevent interference with manual detection\n    // Users can manually click the GPS button when they want location detection\n    /**\r\n   * Load the current active session from API\r\n   */ const loadActiveSession = async ()=>{\n        try {\n            const response = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.apiRequest)(\"/time/active-session\");\n            if (response.success && response.data) {\n                setActiveSession(response.data);\n            }\n        } catch (error) {\n            console.error(\"Error loading active session:\", error);\n        }\n    };\n    /**\r\n   * Load time statistics from API\r\n   */ const loadTimeStats = async ()=>{\n        try {\n            const response = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.apiRequest)(\"/time/stats\");\n            if (response.success && response.data) {\n                setTimeStats(response.data);\n            }\n        } catch (error) {\n            console.error(\"Error loading time stats:\", error);\n        }\n    };\n    /**\r\n   * Detect user's current location using GPS\r\n   */ const detectLocation = async ()=>{\n        setIsDetectingLocation(true);\n        setMessage(\"Getting your location...\");\n        // Clear previous coordinates to ensure fresh detection\n        setCoordinates(null);\n        setLocation(\"\");\n        try {\n            if (!navigator.geolocation) {\n                setMessage(\"Geolocation is not supported by this browser\");\n                return;\n            }\n            setMessage(\"Acquiring GPS coordinates...\");\n            const position = await getCurrentPosition();\n            const { latitude, longitude, accuracy } = position.coords;\n            console.log(\"GPS Position acquired:\", {\n                latitude,\n                longitude,\n                accuracy\n            });\n            setCoordinates({\n                lat: latitude,\n                lng: longitude\n            });\n            setMessage(\"Converting coordinates to address...\");\n            // Reverse geocode to get readable address\n            const address = await reverseGeocode(latitude, longitude);\n            setLocation(address);\n            if (accuracy > 100) {\n                setMessage(`Location detected (accuracy: ${Math.round(accuracy)}m). You may want to try again for better accuracy.`);\n            } else {\n                setMessage(`Location detected successfully! (accuracy: ${Math.round(accuracy)}m)`);\n            }\n        } catch (error) {\n            console.error(\"Location detection error:\", error);\n            setCoordinates(null); // Clear coordinates on error\n            if (error instanceof GeolocationPositionError) {\n                switch(error.code){\n                    case error.PERMISSION_DENIED:\n                        setMessage(\"Location access denied. Please enable location permissions in your browser and try again.\");\n                        break;\n                    case error.POSITION_UNAVAILABLE:\n                        setMessage(\"Location information unavailable. Please check your GPS/network and try again, or enter location manually.\");\n                        break;\n                    case error.TIMEOUT:\n                        setMessage(\"Location detection timed out. Please try again or enter location manually.\");\n                        break;\n                    default:\n                        setMessage(\"Location detection failed. Please enter location manually.\");\n                        break;\n                }\n            } else {\n                setMessage(\"Unable to detect location. Please enter location manually.\");\n            }\n        } finally{\n            setIsDetectingLocation(false);\n        }\n    };\n    /**\r\n   * Get current position as a Promise with improved accuracy\r\n   */ const getCurrentPosition = ()=>{\n        return new Promise((resolve, reject)=>{\n            navigator.geolocation.getCurrentPosition((position)=>{\n                // Validate position accuracy\n                if (position.coords.accuracy > 100) {\n                    console.warn(\"GPS accuracy is low:\", position.coords.accuracy, \"meters\");\n                // Still resolve but with warning\n                }\n                resolve(position);\n            }, reject, {\n                enableHighAccuracy: true,\n                timeout: 15000,\n                maximumAge: 60000 // Reduced cache time to 1 minute for fresher readings\n            });\n        });\n    };\n    /**\r\n   * Reverse geocode coordinates to readable address\r\n   */ const reverseGeocode = async (lat, lng)=>{\n        try {\n            console.log(\"Reverse geocoding coordinates:\", {\n                lat,\n                lng\n            });\n            // Using OpenStreetMap Nominatim API (free, no API key required)\n            const response = await fetch(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&zoom=18&addressdetails=1&limit=1`, {\n                headers: {\n                    \"User-Agent\": \"FlexairTimekeeping/1.0\",\n                    \"Accept\": \"application/json\"\n                }\n            });\n            if (!response.ok) {\n                throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n            }\n            const data = await response.json();\n            console.log(\"Geocoding response:\", data);\n            if (data && data.display_name) {\n                // Extract relevant parts of the address\n                const address = data.address || {};\n                const parts = [];\n                // Building number and street\n                if (address.house_number && address.road) {\n                    parts.push(`${address.house_number} ${address.road}`);\n                } else if (address.road) {\n                    parts.push(address.road);\n                } else if (address.pedestrian) {\n                    parts.push(address.pedestrian);\n                }\n                // Neighborhood/area\n                if (address.neighbourhood || address.suburb || address.quarter) {\n                    parts.push(address.neighbourhood || address.suburb || address.quarter);\n                }\n                // City/town\n                if (address.city || address.town || address.village || address.municipality) {\n                    parts.push(address.city || address.town || address.village || address.municipality);\n                }\n                // State/region\n                if (address.state || address.region) {\n                    parts.push(address.state || address.region);\n                }\n                // Country (only if international)\n                if (address.country && address.country !== \"United States\") {\n                    parts.push(address.country);\n                }\n                let formattedAddress = parts.length > 0 ? parts.join(\", \") : data.display_name;\n                // Clean up the address\n                formattedAddress = formattedAddress.replace(/,\\s*,/g, \",\").trim();\n                // Limit length to keep it reasonable\n                if (formattedAddress.length > 100) {\n                    formattedAddress = formattedAddress.substring(0, 97) + \"...\";\n                }\n                console.log(\"Formatted address:\", formattedAddress);\n                return formattedAddress;\n            }\n            throw new Error(\"No address data in response\");\n        } catch (error) {\n            console.error(\"Reverse geocoding error:\", error);\n            // Fallback to coordinates if geocoding fails\n            const fallbackAddress = `GPS: ${lat.toFixed(6)}, ${lng.toFixed(6)}`;\n            console.log(\"Using fallback address:\", fallbackAddress);\n            return fallbackAddress;\n        }\n    };\n    /**\r\n   * Handle clock in action\r\n   */ const handleClockIn = async ()=>{\n        if (!location.trim()) {\n            setMessage(\"Please detect your location or enter it manually\");\n            return;\n        }\n        setIsLoading(true);\n        setMessage(\"\");\n        try {\n            const requestData = {\n                location: location.trim()\n            };\n            // Include GPS coordinates if available\n            if (coordinates) {\n                requestData.coordinates = coordinates;\n            }\n            const response = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.apiRequest)(\"/time/clock-in\", {\n                method: \"POST\",\n                body: JSON.stringify(requestData)\n            });\n            if (response.success) {\n                setMessage(\"Successfully clocked in!\");\n                setLocation(\"\");\n                await loadActiveSession();\n                await loadTimeStats();\n            } else {\n                setMessage(response.message || \"Failed to clock in\");\n            }\n        } catch (error) {\n            setMessage(\"Error clocking in. Please try again.\");\n            console.error(\"Clock in error:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    /**\r\n   * Handle clock out action\r\n   */ const handleClockOut = async ()=>{\n        setIsLoading(true);\n        setMessage(\"\");\n        try {\n            const response = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.apiRequest)(\"/time/clock-out\", {\n                method: \"POST\",\n                body: JSON.stringify({})\n            });\n            if (response.success) {\n                setMessage(\"Successfully clocked out!\");\n                setActiveSession(null);\n                await loadTimeStats();\n            } else {\n                setMessage(response.message || \"Failed to clock out\");\n            }\n        } catch (error) {\n            setMessage(\"Error clocking out. Please try again.\");\n            console.error(\"Clock out error:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    /**\r\n   * Calculate session duration\r\n   */ const getSessionDuration = ()=>{\n        if (!activeSession) return \"0:00:00\";\n        const clockInTime = new Date(activeSession.clock_in_time);\n        const duration = currentTime.getTime() - clockInTime.getTime();\n        const hours = Math.floor(duration / (1000 * 60 * 60));\n        const minutes = Math.floor(duration % (1000 * 60 * 60) / (1000 * 60));\n        const seconds = Math.floor(duration % (1000 * 60) / 1000);\n        return `${hours}:${minutes.toString().padStart(2, \"0\")}:${seconds.toString().padStart(2, \"0\")}`;\n    };\n    /**\r\n   * Format time for display\r\n   */ const formatTime = (dateString)=>{\n        const date = new Date(dateString);\n        return date.toLocaleTimeString(\"en-US\", {\n            hour: \"2-digit\",\n            minute: \"2-digit\",\n            hour12: true\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"compact-card\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold text-gray-800 mb-2\",\n                        children: \"Time Clock\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                        lineNumber: 354,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: [\n                            \"Welcome back, \",\n                            user?.firstName,\n                            \"!\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                        lineNumber: 355,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg font-mono text-gray-800 mt-2\",\n                        children: currentTime.toLocaleString(\"en-US\", {\n                            weekday: \"long\",\n                            year: \"numeric\",\n                            month: \"long\",\n                            day: \"numeric\",\n                            hour: \"2-digit\",\n                            minute: \"2-digit\",\n                            second: \"2-digit\",\n                            hour12: true\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                        lineNumber: 356,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                lineNumber: 353,\n                columnNumber: 7\n            }, this),\n            activeSession && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-green-50 border border-green-200 rounded-lg p-3 mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-green-800\",\n                                children: \"Currently Clocked In\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                lineNumber: 374,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"bg-green-100 text-green-800 px-2 py-1 rounded-full text-sm font-medium\",\n                                children: \"Active\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                lineNumber: 375,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                        lineNumber: 373,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-green-600\",\n                                        children: \"Clock In Time\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"font-semibold text-green-800\",\n                                        children: formatTime(activeSession.clock_in_time)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                        lineNumber: 382,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                lineNumber: 380,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-green-600\",\n                                        children: \"Session Duration\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                        lineNumber: 387,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"font-mono text-xl font-bold text-green-800\",\n                                        children: getSessionDuration()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                lineNumber: 386,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-green-600\",\n                                        children: \"Location\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                        lineNumber: 393,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"font-semibold text-green-800\",\n                                        children: activeSession.clock_in_location || \"Not specified\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                        lineNumber: 394,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                lineNumber: 392,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                        lineNumber: 379,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                lineNumber: 372,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3 mb-4\",\n                children: !activeSession ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"location\",\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Location\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                    lineNumber: 407,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    id: \"location\",\n                                                    value: location,\n                                                    onChange: (e)=>setLocation(e.target.value),\n                                                    placeholder: \"Click GPS to detect location or enter manually\",\n                                                    className: \"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                    disabled: isLoading || isDetectingLocation\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                                    lineNumber: 412,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: detectLocation,\n                                                    disabled: isLoading || isDetectingLocation,\n                                                    className: \"px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-md transition duration-200 flex items-center gap-2\",\n                                                    title: \"Detect my location using GPS\",\n                                                    children: isDetectingLocation ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                                                lineNumber: 429,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"hidden sm:inline\",\n                                                                children: \"Detecting...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                                                lineNumber: 430,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-4 h-4\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: \"2\",\n                                                                        d: \"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                                                        lineNumber: 435,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: \"2\",\n                                                                        d: \"M15 11a3 3 0 11-6 0 3 3 0 016 0z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                                                        lineNumber: 436,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                                                lineNumber: 434,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"hidden sm:inline\",\n                                                                children: \"GPS\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                                                lineNumber: 438,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                                    lineNumber: 421,\n                                                    columnNumber: 19\n                                                }, this),\n                                                (location || coordinates) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>{\n                                                        setLocation(\"\");\n                                                        setCoordinates(null);\n                                                        setMessage(\"\");\n                                                    },\n                                                    disabled: isLoading || isDetectingLocation,\n                                                    className: \"px-3 py-2 bg-gray-500 hover:bg-gray-600 disabled:bg-gray-400 text-white rounded-md transition duration-200\",\n                                                    title: \"Clear location\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: \"2\",\n                                                            d: \"M6 18L18 6M6 6l12 12\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                                            lineNumber: 454,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                                        lineNumber: 453,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                                    lineNumber: 443,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                            lineNumber: 411,\n                                            columnNumber: 17\n                                        }, this),\n                                        coordinates && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between text-xs text-gray-500 bg-gray-50 p-2 rounded border\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-3 h-3\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: \"2\",\n                                                                d: \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                                                lineNumber: 463,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                                            lineNumber: 462,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"GPS: \",\n                                                        coordinates.lat.toFixed(6),\n                                                        \", \",\n                                                        coordinates.lng.toFixed(6)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                                    lineNumber: 461,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: detectLocation,\n                                                    disabled: isDetectingLocation,\n                                                    className: \"text-blue-600 hover:text-blue-800 disabled:text-gray-400\",\n                                                    title: \"Refresh location\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-3 h-3\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: \"2\",\n                                                            d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                                            lineNumber: 474,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                                        lineNumber: 473,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                                    lineNumber: 467,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                            lineNumber: 460,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                    lineNumber: 410,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                            lineNumber: 406,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleClockIn,\n                            disabled: isLoading || isDetectingLocation,\n                            className: \"w-full bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-bold py-3 px-4 rounded-lg transition duration-200\",\n                            children: isLoading ? \"Clocking In...\" : \"Clock In\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                            lineNumber: 481,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                    lineNumber: 405,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleClockOut,\n                    disabled: isLoading,\n                    className: \"w-full bg-red-600 hover:bg-red-700 disabled:bg-gray-400 text-white font-bold py-3 px-4 rounded-lg transition duration-200\",\n                    children: isLoading ? \"Clocking Out...\" : \"Clock Out\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                    lineNumber: 490,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                lineNumber: 403,\n                columnNumber: 7\n            }, this),\n            message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `p-3 rounded-md mb-6 ${message.includes(\"Successfully\") || message.includes(\"detected successfully\") ? \"bg-green-100 text-green-700 border border-green-200\" : message.includes(\"Please detect\") || message.includes(\"enter manually\") ? \"bg-yellow-100 text-yellow-700 border border-yellow-200\" : \"bg-red-100 text-red-700 border border-red-200\"}`,\n                children: message\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                lineNumber: 502,\n                columnNumber: 9\n            }, this),\n            timeStats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t pt-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-800 mb-3\",\n                        children: \"Time Summary\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                        lineNumber: 516,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center p-2 bg-gray-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"Today\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                        lineNumber: 519,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-bold text-gray-800\",\n                                        children: timeStats.today_hours\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                        lineNumber: 520,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                lineNumber: 518,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center p-2 bg-gray-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"This Week\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                        lineNumber: 523,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-bold text-gray-800\",\n                                        children: timeStats.week_hours\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                        lineNumber: 524,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                lineNumber: 522,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center p-2 bg-gray-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"This Month\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                        lineNumber: 527,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-bold text-gray-800\",\n                                        children: timeStats.month_hours\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                        lineNumber: 528,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                lineNumber: 526,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center p-2 bg-gray-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"Overtime (Week)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                        lineNumber: 531,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-bold text-orange-600\",\n                                        children: timeStats.overtime_week\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                        lineNumber: 532,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                lineNumber: 530,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                        lineNumber: 517,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                lineNumber: 515,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n        lineNumber: 352,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/time/ClockInOut.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/time/TimeLogs.tsx":
/*!******************************************!*\
  !*** ./src/components/time/TimeLogs.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TimeLogs)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/auth */ \"(ssr)/./src/lib/auth.ts\");\n/* harmony import */ var _components_approvals_ApprovalRequestForm__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/approvals/ApprovalRequestForm */ \"(ssr)/./src/components/approvals/ApprovalRequestForm.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n/**\r\n * Time Logs component for viewing and managing time entries\r\n * Displays paginated list of time logs with filtering options\r\n */ function TimeLogs() {\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [timeLogs, setTimeLogs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalCount, setTotalCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Filter states\n    const [startDate, setStartDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [endDate, setEndDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Approval request modal state\n    const [showApprovalModal, setShowApprovalModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedTimeLog, setSelectedTimeLog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Initialize with current week\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const now = new Date();\n        const weekStart = new Date(now.setDate(now.getDate() - now.getDay()));\n        const weekEnd = new Date(weekStart);\n        weekEnd.setDate(weekStart.getDate() + 6);\n        setStartDate(weekStart.toISOString().split(\"T\")[0]);\n        setEndDate(weekEnd.toISOString().split(\"T\")[0]);\n    }, []);\n    // Load time logs when filters or page changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (startDate && endDate) {\n            loadTimeLogs();\n        }\n    }, [\n        currentPage,\n        startDate,\n        endDate,\n        status\n    ]);\n    /**\r\n   * Load time logs from API with current filters\r\n   */ const loadTimeLogs = async ()=>{\n        setIsLoading(true);\n        setError(\"\");\n        try {\n            const params = new URLSearchParams({\n                page: currentPage.toString(),\n                limit: \"10\",\n                start_date: startDate,\n                end_date: endDate,\n                ...status !== \"all\" && {\n                    status\n                }\n            });\n            const response = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.apiRequest)(`/time/logs?${params}`);\n            if (response.success && response.data) {\n                const data = response.data;\n                setTimeLogs(data.logs);\n                setTotalPages(data.totalPages);\n                setTotalCount(data.totalCount);\n            } else {\n                setError(response.message || \"Failed to load time logs\");\n            }\n        } catch (error) {\n            setError(\"Error loading time logs. Please try again.\");\n            console.error(\"Time logs error:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    /**\r\n   * Handle filter changes\r\n   */ const handleFilterChange = ()=>{\n        setCurrentPage(1); // Reset to first page when filters change\n        loadTimeLogs();\n    };\n    /**\r\n   * Handle page changes\r\n   */ const handlePageChange = (page)=>{\n        setCurrentPage(page);\n    };\n    /**\r\n   * Format date for display\r\n   */ const formatDate = (dateString)=>{\n        const date = new Date(dateString);\n        return date.toLocaleDateString(\"en-US\", {\n            month: \"short\",\n            day: \"numeric\",\n            year: \"numeric\"\n        });\n    };\n    /**\r\n   * Format time for display\r\n   */ const formatTime = (dateString)=>{\n        const date = new Date(dateString);\n        return date.toLocaleTimeString(\"en-US\", {\n            hour: \"2-digit\",\n            minute: \"2-digit\",\n            hour12: true\n        });\n    };\n    /**\r\n   * Get status badge color\r\n   */ const getStatusBadge = (status)=>{\n        const colors = {\n            active: \"bg-blue-100 text-blue-800\",\n            completed: \"bg-green-100 text-green-800\",\n            approved: \"bg-purple-100 text-purple-800\"\n        };\n        return colors[status] || \"bg-gray-100 text-gray-800\";\n    };\n    /**\r\n   * Calculate total hours for the period\r\n   */ const getTotalHours = ()=>{\n        return timeLogs.reduce((total, log)=>{\n            if (log.total_hours) {\n                const hours = parseFloat(log.total_hours);\n                return total + hours;\n            }\n            return total;\n        }, 0).toFixed(2);\n    };\n    /**\r\n   * Set quick date filters\r\n   */ const setQuickDateFilter = (days)=>{\n        const end = new Date();\n        const start = new Date();\n        start.setDate(end.getDate() - days);\n        setStartDate(start.toISOString().split(\"T\")[0]);\n        setEndDate(end.toISOString().split(\"T\")[0]);\n    };\n    /**\r\n   * Filter time logs based on search term\r\n   */ const filteredTimeLogs = timeLogs.filter((log)=>{\n        if (!searchTerm) return true;\n        const searchLower = searchTerm.toLowerCase();\n        return log.clock_in_location?.toLowerCase().includes(searchLower) || log.notes?.toLowerCase().includes(searchLower) || log.status.toLowerCase().includes(searchLower);\n    });\n    /**\r\n   * Open approval request modal\r\n   */ const openApprovalModal = (timeLog)=>{\n        setSelectedTimeLog(timeLog);\n        setShowApprovalModal(true);\n    };\n    /**\r\n   * Close approval request modal\r\n   */ const closeApprovalModal = ()=>{\n        setShowApprovalModal(false);\n        setSelectedTimeLog(null);\n    };\n    /**\r\n   * Handle approval request success\r\n   */ const handleApprovalSuccess = ()=>{\n        closeApprovalModal();\n        // Optionally refresh the time logs to show updated status\n        loadTimeLogs();\n    };\n    /**\r\n   * Check if time log can be corrected\r\n   */ const canRequestCorrection = (timeLog)=>{\n        // Only allow correction requests for completed time logs\n        return timeLog.status === \"completed\" && timeLog.clock_out_time;\n    };\n    /**\r\n   * Export time logs to CSV\r\n   */ const exportToCSV = ()=>{\n        const headers = [\n            \"Date\",\n            \"Clock In\",\n            \"Clock Out\",\n            \"Total Hours\",\n            \"Location\",\n            \"Status\",\n            \"Notes\"\n        ];\n        const csvData = filteredTimeLogs.map((log)=>[\n                formatDate(log.clock_in_time),\n                formatTime(log.clock_in_time),\n                log.clock_out_time ? formatTime(log.clock_out_time) : \"\",\n                log.total_hours || \"\",\n                log.clock_in_location || \"\",\n                log.status,\n                log.notes || \"\"\n            ]);\n        const csvContent = [\n            headers,\n            ...csvData\n        ].map((row)=>row.map((field)=>`\"${field}\"`).join(\",\")).join(\"\\n\");\n        const blob = new Blob([\n            csvContent\n        ], {\n            type: \"text/csv\"\n        });\n        const url = window.URL.createObjectURL(blob);\n        const a = document.createElement(\"a\");\n        a.href = url;\n        a.download = `time-logs-${startDate}-to-${endDate}.csv`;\n        a.click();\n        window.URL.revokeObjectURL(url);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"compact-card\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-bold text-gray-800 mb-1\",\n                                    children: \"Time Logs\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"View and manage your time entries\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                            lineNumber: 262,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-3 sm:mt-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: exportToCSV,\n                                disabled: filteredTimeLogs.length === 0,\n                                className: \"btn btn-success\",\n                                children: \"\\uD83D\\uDCE5 Export CSV\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                            lineNumber: 266,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                    lineNumber: 261,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                lineNumber: 260,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-50 rounded-lg p-3 mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-800 mb-2 lg:mb-0\",\n                                children: \"Search & Filters\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                lineNumber: 281,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setQuickDateFilter(0),\n                                        className: \"btn btn-sm bg-blue-100 hover:bg-blue-200 text-blue-800\",\n                                        children: \"Today\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setQuickDateFilter(7),\n                                        className: \"btn btn-sm bg-blue-100 hover:bg-blue-200 text-blue-800\",\n                                        children: \"Last 7 Days\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setQuickDateFilter(30),\n                                        className: \"btn btn-sm bg-blue-100 hover:bg-blue-200 text-blue-800\",\n                                        children: \"Last 30 Days\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                        lineNumber: 295,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setQuickDateFilter(90),\n                                        className: \"btn btn-sm bg-blue-100 hover:bg-blue-200 text-blue-800\",\n                                        children: \"Last 3 Months\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                        lineNumber: 280,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\",\n                                    children: \"\\uD83D\\uDD0D\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                    lineNumber: 313,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"Search by location, notes, or status...\",\n                                    value: searchTerm,\n                                    onChange: (e)=>setSearchTerm(e.target.value),\n                                    className: \"form-input pl-10\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                            lineNumber: 312,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                        lineNumber: 311,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-4 gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"startDate\",\n                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                        children: \"Start Date\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"date\",\n                                        id: \"startDate\",\n                                        value: startDate,\n                                        onChange: (e)=>setStartDate(e.target.value),\n                                        className: \"form-input\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                        lineNumber: 330,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"endDate\",\n                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                        children: \"End Date\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"date\",\n                                        id: \"endDate\",\n                                        value: endDate,\n                                        onChange: (e)=>setEndDate(e.target.value),\n                                        className: \"form-input\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"status\",\n                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                        children: \"Status\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                        lineNumber: 351,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        id: \"status\",\n                                        value: status,\n                                        onChange: (e)=>setStatus(e.target.value),\n                                        className: \"form-input\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"all\",\n                                                children: \"All Statuses\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                                lineNumber: 360,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"active\",\n                                                children: \"Active\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                                lineNumber: 361,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"completed\",\n                                                children: \"Completed\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                                lineNumber: 362,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"approved\",\n                                                children: \"Approved\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                                lineNumber: 363,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                        lineNumber: 354,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                lineNumber: 350,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-end\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleFilterChange,\n                                    disabled: isLoading,\n                                    className: \"btn btn-primary w-full\",\n                                    children: isLoading ? \"Loading...\" : \"Apply Filters\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                    lineNumber: 367,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                lineNumber: 366,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                        lineNumber: 325,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                lineNumber: 279,\n                columnNumber: 7\n            }, this),\n            timeLogs.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-blue-600\",\n                                    children: \"Total Entries\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                    lineNumber: 383,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl font-bold text-blue-800\",\n                                    children: totalCount\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                    lineNumber: 384,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                            lineNumber: 382,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-blue-600\",\n                                    children: \"Showing\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                    lineNumber: 387,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl font-bold text-blue-800\",\n                                    children: filteredTimeLogs.length\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                    lineNumber: 388,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                            lineNumber: 386,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-blue-600\",\n                                    children: \"Total Hours\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl font-bold text-blue-800\",\n                                    children: getTotalHours()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                    lineNumber: 392,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                            lineNumber: 390,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-blue-600\",\n                                    children: \"Date Range\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                    lineNumber: 395,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"font-semibold text-blue-800\",\n                                    children: [\n                                        formatDate(startDate),\n                                        \" - \",\n                                        formatDate(endDate)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                    lineNumber: 396,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                            lineNumber: 394,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                    lineNumber: 381,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                lineNumber: 380,\n                columnNumber: 9\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-100 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6\",\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                lineNumber: 406,\n                columnNumber: 9\n            }, this),\n            isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center items-center py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                        lineNumber: 414,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"ml-2 text-gray-600\",\n                        children: \"Loading time logs...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                        lineNumber: 415,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                lineNumber: 413,\n                columnNumber: 9\n            }, this) : filteredTimeLogs.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-500\",\n                    children: timeLogs.length === 0 ? \"No time logs found for the selected period.\" : \"No time logs match your search criteria.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                    lineNumber: 419,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                lineNumber: 418,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"overflow-x-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                    className: \"table\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                            className: \"table-header\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"table-header-cell\",\n                                        children: \"Date\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                        lineNumber: 430,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"table-header-cell\",\n                                        children: \"Clock In\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                        lineNumber: 433,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"table-header-cell\",\n                                        children: \"Clock Out\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                        lineNumber: 436,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"table-header-cell\",\n                                        children: \"Total Hours\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                        lineNumber: 439,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"table-header-cell\",\n                                        children: \"Location\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                        lineNumber: 442,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"table-header-cell\",\n                                        children: \"Status\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                        lineNumber: 445,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"table-header-cell\",\n                                        children: \"Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                        lineNumber: 448,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                lineNumber: 429,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                            lineNumber: 428,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                            className: \"bg-white divide-y divide-gray-200\",\n                            children: filteredTimeLogs.map((log)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    className: \"table-row\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"table-cell\",\n                                            children: formatDate(log.clock_in_time)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                            lineNumber: 456,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"table-cell\",\n                                            children: formatTime(log.clock_in_time)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                            lineNumber: 459,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"table-cell\",\n                                            children: log.clock_out_time ? formatTime(log.clock_out_time) : \"-\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                            lineNumber: 462,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"table-cell font-medium\",\n                                            children: log.total_hours || \"-\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                            lineNumber: 465,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"table-cell text-gray-500\",\n                                            children: log.clock_in_location || \"Not specified\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                            lineNumber: 468,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"table-cell\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: `badge ${getStatusBadge(log.status)}`,\n                                                children: log.status.charAt(0).toUpperCase() + log.status.slice(1)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                                lineNumber: 472,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                            lineNumber: 471,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"table-cell font-medium\",\n                                            children: canRequestCorrection(log) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>openApprovalModal(log),\n                                                className: \"text-blue-600 hover:text-blue-900 transition duration-200\",\n                                                children: \"Request Correction\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                                lineNumber: 478,\n                                                columnNumber: 23\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-400\",\n                                                children: \"-\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                                lineNumber: 485,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                            lineNumber: 476,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, log.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                    lineNumber: 455,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                            lineNumber: 453,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                    lineNumber: 427,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                lineNumber: 426,\n                columnNumber: 9\n            }, this),\n            totalPages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mt-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-gray-700\",\n                        children: [\n                            \"Showing page \",\n                            currentPage,\n                            \" of \",\n                            totalPages,\n                            \" (\",\n                            totalCount,\n                            \" total entries)\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                        lineNumber: 498,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handlePageChange(currentPage - 1),\n                                disabled: currentPage === 1,\n                                className: \"px-3 py-1 text-sm bg-gray-200 hover:bg-gray-300 disabled:bg-gray-100 disabled:text-gray-400 rounded-md transition duration-200\",\n                                children: \"Previous\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                lineNumber: 502,\n                                columnNumber: 13\n                            }, this),\n                            Array.from({\n                                length: Math.min(5, totalPages)\n                            }, (_, i)=>{\n                                const page = i + 1;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handlePageChange(page),\n                                    className: `px-3 py-1 text-sm rounded-md transition duration-200 ${currentPage === page ? \"bg-blue-600 text-white\" : \"bg-gray-200 hover:bg-gray-300\"}`,\n                                    children: page\n                                }, page, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                    lineNumber: 512,\n                                    columnNumber: 17\n                                }, this);\n                            }),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handlePageChange(currentPage + 1),\n                                disabled: currentPage === totalPages,\n                                className: \"px-3 py-1 text-sm bg-gray-200 hover:bg-gray-300 disabled:bg-gray-100 disabled:text-gray-400 rounded-md transition duration-200\",\n                                children: \"Next\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                lineNumber: 525,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                        lineNumber: 501,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                lineNumber: 497,\n                columnNumber: 9\n            }, this),\n            showApprovalModal && selectedTimeLog && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_approvals_ApprovalRequestForm__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        timeLogId: selectedTimeLog.id,\n                        currentClockIn: selectedTimeLog.clock_in_time,\n                        currentClockOut: selectedTimeLog.clock_out_time || \"\",\n                        onSuccess: handleApprovalSuccess,\n                        onCancel: closeApprovalModal\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                        lineNumber: 540,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                    lineNumber: 539,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                lineNumber: 538,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n        lineNumber: 259,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy90aW1lL1RpbWVMb2dzLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFFbUQ7QUFDRjtBQUNUO0FBQ3FDO0FBMEI3RTs7O0NBR0MsR0FDYyxTQUFTTTtJQUN0QixNQUFNLEVBQUVDLElBQUksRUFBRSxHQUFHSiw4REFBT0E7SUFDeEIsTUFBTSxDQUFDSyxVQUFVQyxZQUFZLEdBQUdSLCtDQUFRQSxDQUFZLEVBQUU7SUFDdEQsTUFBTSxDQUFDUyxZQUFZQyxjQUFjLEdBQUdWLCtDQUFRQSxDQUFDO0lBQzdDLE1BQU0sQ0FBQ1csYUFBYUMsZUFBZSxHQUFHWiwrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUNhLFlBQVlDLGNBQWMsR0FBR2QsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDZSxXQUFXQyxhQUFhLEdBQUdoQiwrQ0FBUUEsQ0FBQztJQUMzQyxNQUFNLENBQUNpQixPQUFPQyxTQUFTLEdBQUdsQiwrQ0FBUUEsQ0FBQztJQUVuQyxnQkFBZ0I7SUFDaEIsTUFBTSxDQUFDbUIsV0FBV0MsYUFBYSxHQUFHcEIsK0NBQVFBLENBQUM7SUFDM0MsTUFBTSxDQUFDcUIsU0FBU0MsV0FBVyxHQUFHdEIsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDdUIsUUFBUUMsVUFBVSxHQUFHeEIsK0NBQVFBLENBQUM7SUFDckMsTUFBTSxDQUFDeUIsWUFBWUMsY0FBYyxHQUFHMUIsK0NBQVFBLENBQUM7SUFFN0MsK0JBQStCO0lBQy9CLE1BQU0sQ0FBQzJCLG1CQUFtQkMscUJBQXFCLEdBQUc1QiwrQ0FBUUEsQ0FBQztJQUMzRCxNQUFNLENBQUM2QixpQkFBaUJDLG1CQUFtQixHQUFHOUIsK0NBQVFBLENBQWlCO0lBRXZFLCtCQUErQjtJQUMvQkMsZ0RBQVNBLENBQUM7UUFDUixNQUFNOEIsTUFBTSxJQUFJQztRQUNoQixNQUFNQyxZQUFZLElBQUlELEtBQUtELElBQUlHLE9BQU8sQ0FBQ0gsSUFBSUksT0FBTyxLQUFLSixJQUFJSyxNQUFNO1FBQ2pFLE1BQU1DLFVBQVUsSUFBSUwsS0FBS0M7UUFDekJJLFFBQVFILE9BQU8sQ0FBQ0QsVUFBVUUsT0FBTyxLQUFLO1FBRXRDZixhQUFhYSxVQUFVSyxXQUFXLEdBQUdDLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRTtRQUNsRGpCLFdBQVdlLFFBQVFDLFdBQVcsR0FBR0MsS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFO0lBQ2hELEdBQUcsRUFBRTtJQUVMLDhDQUE4QztJQUM5Q3RDLGdEQUFTQSxDQUFDO1FBQ1IsSUFBSWtCLGFBQWFFLFNBQVM7WUFDeEJtQjtRQUNGO0lBQ0YsR0FBRztRQUFDN0I7UUFBYVE7UUFBV0U7UUFBU0U7S0FBTztJQUU1Qzs7R0FFQyxHQUNELE1BQU1pQixlQUFlO1FBQ25CeEIsYUFBYTtRQUNiRSxTQUFTO1FBRVQsSUFBSTtZQUNGLE1BQU11QixTQUFTLElBQUlDLGdCQUFnQjtnQkFDakNDLE1BQU1oQyxZQUFZaUMsUUFBUTtnQkFDMUJDLE9BQU87Z0JBQ1BDLFlBQVkzQjtnQkFDWjRCLFVBQVUxQjtnQkFDVixHQUFJRSxXQUFXLFNBQVM7b0JBQUVBO2dCQUFPLENBQUM7WUFDcEM7WUFFQSxNQUFNeUIsV0FBVyxNQUFNN0MscURBQVVBLENBQUMsQ0FBQyxXQUFXLEVBQUVzQyxPQUFPLENBQUM7WUFFeEQsSUFBSU8sU0FBU0MsT0FBTyxJQUFJRCxTQUFTRSxJQUFJLEVBQUU7Z0JBQ3JDLE1BQU1BLE9BQU9GLFNBQVNFLElBQUk7Z0JBQzFCMUMsWUFBWTBDLEtBQUtDLElBQUk7Z0JBQ3JCekMsY0FBY3dDLEtBQUt6QyxVQUFVO2dCQUM3QkssY0FBY29DLEtBQUtyQyxVQUFVO1lBQy9CLE9BQU87Z0JBQ0xLLFNBQVM4QixTQUFTSSxPQUFPLElBQUk7WUFDL0I7UUFDRixFQUFFLE9BQU9uQyxPQUFPO1lBQ2RDLFNBQVM7WUFDVG1DLFFBQVFwQyxLQUFLLENBQUMsb0JBQW9CQTtRQUNwQyxTQUFVO1lBQ1JELGFBQWE7UUFDZjtJQUNGO0lBRUE7O0dBRUMsR0FDRCxNQUFNc0MscUJBQXFCO1FBQ3pCMUMsZUFBZSxJQUFJLDBDQUEwQztRQUM3RDRCO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELE1BQU1lLG1CQUFtQixDQUFDWjtRQUN4Qi9CLGVBQWUrQjtJQUNqQjtJQUVBOztHQUVDLEdBQ0QsTUFBTWEsYUFBYSxDQUFDQztRQUNsQixNQUFNQyxPQUFPLElBQUkxQixLQUFLeUI7UUFDdEIsT0FBT0MsS0FBS0Msa0JBQWtCLENBQUMsU0FBUztZQUN0Q0MsT0FBTztZQUNQQyxLQUFLO1lBQ0xDLE1BQU07UUFDUjtJQUNGO0lBRUE7O0dBRUMsR0FDRCxNQUFNQyxhQUFhLENBQUNOO1FBQ2xCLE1BQU1DLE9BQU8sSUFBSTFCLEtBQUt5QjtRQUN0QixPQUFPQyxLQUFLTSxrQkFBa0IsQ0FBQyxTQUFTO1lBQ3RDQyxNQUFNO1lBQ05DLFFBQVE7WUFDUkMsUUFBUTtRQUNWO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELE1BQU1DLGlCQUFpQixDQUFDN0M7UUFDdEIsTUFBTThDLFNBQVM7WUFDYkMsUUFBUTtZQUNSQyxXQUFXO1lBQ1hDLFVBQVU7UUFDWjtRQUNBLE9BQU9ILE1BQU0sQ0FBQzlDLE9BQThCLElBQUk7SUFDbEQ7SUFFQTs7R0FFQyxHQUNELE1BQU1rRCxnQkFBZ0I7UUFDcEIsT0FBT2xFLFNBQVNtRSxNQUFNLENBQUMsQ0FBQ0MsT0FBT0M7WUFDN0IsSUFBSUEsSUFBSUMsV0FBVyxFQUFFO2dCQUNuQixNQUFNQyxRQUFRQyxXQUFXSCxJQUFJQyxXQUFXO2dCQUN4QyxPQUFPRixRQUFRRztZQUNqQjtZQUNBLE9BQU9IO1FBQ1QsR0FBRyxHQUFHSyxPQUFPLENBQUM7SUFDaEI7SUFFQTs7R0FFQyxHQUNELE1BQU1DLHFCQUFxQixDQUFDQztRQUMxQixNQUFNQyxNQUFNLElBQUluRDtRQUNoQixNQUFNb0QsUUFBUSxJQUFJcEQ7UUFDbEJvRCxNQUFNbEQsT0FBTyxDQUFDaUQsSUFBSWhELE9BQU8sS0FBSytDO1FBRTlCOUQsYUFBYWdFLE1BQU05QyxXQUFXLEdBQUdDLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRTtRQUM5Q2pCLFdBQVc2RCxJQUFJN0MsV0FBVyxHQUFHQyxLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUU7SUFDNUM7SUFFQTs7R0FFQyxHQUNELE1BQU04QyxtQkFBbUI5RSxTQUFTK0UsTUFBTSxDQUFDVixDQUFBQTtRQUN2QyxJQUFJLENBQUNuRCxZQUFZLE9BQU87UUFFeEIsTUFBTThELGNBQWM5RCxXQUFXK0QsV0FBVztRQUMxQyxPQUNFWixJQUFJYSxpQkFBaUIsRUFBRUQsY0FBY0UsU0FBU0gsZ0JBQzlDWCxJQUFJZSxLQUFLLEVBQUVILGNBQWNFLFNBQVNILGdCQUNsQ1gsSUFBSXJELE1BQU0sQ0FBQ2lFLFdBQVcsR0FBR0UsUUFBUSxDQUFDSDtJQUV0QztJQUVBOztHQUVDLEdBQ0QsTUFBTUssb0JBQW9CLENBQUNDO1FBQ3pCL0QsbUJBQW1CK0Q7UUFDbkJqRSxxQkFBcUI7SUFDdkI7SUFFQTs7R0FFQyxHQUNELE1BQU1rRSxxQkFBcUI7UUFDekJsRSxxQkFBcUI7UUFDckJFLG1CQUFtQjtJQUNyQjtJQUVBOztHQUVDLEdBQ0QsTUFBTWlFLHdCQUF3QjtRQUM1QkQ7UUFDQSwwREFBMEQ7UUFDMUR0RDtJQUNGO0lBRUE7O0dBRUMsR0FDRCxNQUFNd0QsdUJBQXVCLENBQUNIO1FBQzVCLHlEQUF5RDtRQUN6RCxPQUFPQSxRQUFRdEUsTUFBTSxLQUFLLGVBQWVzRSxRQUFRSSxjQUFjO0lBQ2pFO0lBRUE7O0dBRUMsR0FDRCxNQUFNQyxjQUFjO1FBQ2xCLE1BQU1DLFVBQVU7WUFBQztZQUFRO1lBQVk7WUFBYTtZQUFlO1lBQVk7WUFBVTtTQUFRO1FBQy9GLE1BQU1DLFVBQVVmLGlCQUFpQmdCLEdBQUcsQ0FBQ3pCLENBQUFBLE1BQU87Z0JBQzFDcEIsV0FBV29CLElBQUkwQixhQUFhO2dCQUM1QnZDLFdBQVdhLElBQUkwQixhQUFhO2dCQUM1QjFCLElBQUlxQixjQUFjLEdBQUdsQyxXQUFXYSxJQUFJcUIsY0FBYyxJQUFJO2dCQUN0RHJCLElBQUlDLFdBQVcsSUFBSTtnQkFDbkJELElBQUlhLGlCQUFpQixJQUFJO2dCQUN6QmIsSUFBSXJELE1BQU07Z0JBQ1ZxRCxJQUFJZSxLQUFLLElBQUk7YUFDZDtRQUVELE1BQU1ZLGFBQWE7WUFBQ0o7ZUFBWUM7U0FBUSxDQUNyQ0MsR0FBRyxDQUFDRyxDQUFBQSxNQUFPQSxJQUFJSCxHQUFHLENBQUNJLENBQUFBLFFBQVMsQ0FBQyxDQUFDLEVBQUVBLE1BQU0sQ0FBQyxDQUFDLEVBQUVDLElBQUksQ0FBQyxNQUMvQ0EsSUFBSSxDQUFDO1FBRVIsTUFBTUMsT0FBTyxJQUFJQyxLQUFLO1lBQUNMO1NBQVcsRUFBRTtZQUFFTSxNQUFNO1FBQVc7UUFDdkQsTUFBTUMsTUFBTUMsT0FBT0MsR0FBRyxDQUFDQyxlQUFlLENBQUNOO1FBQ3ZDLE1BQU1PLElBQUlDLFNBQVNDLGFBQWEsQ0FBQztRQUNqQ0YsRUFBRUcsSUFBSSxHQUFHUDtRQUNUSSxFQUFFSSxRQUFRLEdBQUcsQ0FBQyxVQUFVLEVBQUVuRyxVQUFVLElBQUksRUFBRUUsUUFBUSxJQUFJLENBQUM7UUFDdkQ2RixFQUFFSyxLQUFLO1FBQ1BSLE9BQU9DLEdBQUcsQ0FBQ1EsZUFBZSxDQUFDVjtJQUM3QjtJQUVBLHFCQUNFLDhEQUFDVztRQUFJQyxXQUFVOzswQkFDYiw4REFBQ0Q7Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0Q7OzhDQUNDLDhEQUFDRTtvQ0FBR0QsV0FBVTs4Q0FBdUM7Ozs7Ozs4Q0FDckQsOERBQUNFO29DQUFFRixXQUFVOzhDQUFnQjs7Ozs7Ozs7Ozs7O3NDQUUvQiw4REFBQ0Q7NEJBQUlDLFdBQVU7c0NBQ2IsNEVBQUNHO2dDQUNDQyxTQUFTNUI7Z0NBQ1Q2QixVQUFVMUMsaUJBQWlCMkMsTUFBTSxLQUFLO2dDQUN0Q04sV0FBVTswQ0FDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFRUCw4REFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNPO2dDQUFHUCxXQUFVOzBDQUFtRDs7Ozs7OzBDQUNqRSw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRzt3Q0FDQ0MsU0FBUyxJQUFNN0MsbUJBQW1CO3dDQUNsQ3lDLFdBQVU7a0RBQ1g7Ozs7OztrREFHRCw4REFBQ0c7d0NBQ0NDLFNBQVMsSUFBTTdDLG1CQUFtQjt3Q0FDbEN5QyxXQUFVO2tEQUNYOzs7Ozs7a0RBR0QsOERBQUNHO3dDQUNDQyxTQUFTLElBQU03QyxtQkFBbUI7d0NBQ2xDeUMsV0FBVTtrREFDWDs7Ozs7O2tEQUdELDhEQUFDRzt3Q0FDQ0MsU0FBUyxJQUFNN0MsbUJBQW1CO3dDQUNsQ3lDLFdBQVU7a0RBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FPTCw4REFBQ0Q7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ1E7b0NBQUtSLFdBQVU7OENBQW1FOzs7Ozs7OENBQ25GLDhEQUFDUztvQ0FDQ3RCLE1BQUs7b0NBQ0x1QixhQUFZO29DQUNaQyxPQUFPNUc7b0NBQ1A2RyxVQUFVLENBQUNDLElBQU03RyxjQUFjNkcsRUFBRUMsTUFBTSxDQUFDSCxLQUFLO29DQUM3Q1gsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBTWhCLDhEQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEOztrREFDQyw4REFBQ2dCO3dDQUFNQyxTQUFRO3dDQUFZaEIsV0FBVTtrREFBK0M7Ozs7OztrREFHcEYsOERBQUNTO3dDQUNDdEIsTUFBSzt3Q0FDTDhCLElBQUc7d0NBQ0hOLE9BQU9sSDt3Q0FDUG1ILFVBQVUsQ0FBQ0MsSUFBTW5ILGFBQWFtSCxFQUFFQyxNQUFNLENBQUNILEtBQUs7d0NBQzVDWCxXQUFVOzs7Ozs7Ozs7Ozs7MENBR2QsOERBQUNEOztrREFDQyw4REFBQ2dCO3dDQUFNQyxTQUFRO3dDQUFVaEIsV0FBVTtrREFBK0M7Ozs7OztrREFHbEYsOERBQUNTO3dDQUNDdEIsTUFBSzt3Q0FDTDhCLElBQUc7d0NBQ0hOLE9BQU9oSDt3Q0FDUGlILFVBQVUsQ0FBQ0MsSUFBTWpILFdBQVdpSCxFQUFFQyxNQUFNLENBQUNILEtBQUs7d0NBQzFDWCxXQUFVOzs7Ozs7Ozs7Ozs7MENBR2QsOERBQUNEOztrREFDQyw4REFBQ2dCO3dDQUFNQyxTQUFRO3dDQUFTaEIsV0FBVTtrREFBK0M7Ozs7OztrREFHakYsOERBQUNrQjt3Q0FDQ0QsSUFBRzt3Q0FDSE4sT0FBTzlHO3dDQUNQK0csVUFBVSxDQUFDQyxJQUFNL0csVUFBVStHLEVBQUVDLE1BQU0sQ0FBQ0gsS0FBSzt3Q0FDekNYLFdBQVU7OzBEQUVWLDhEQUFDbUI7Z0RBQU9SLE9BQU07MERBQU07Ozs7OzswREFDcEIsOERBQUNRO2dEQUFPUixPQUFNOzBEQUFTOzs7Ozs7MERBQ3ZCLDhEQUFDUTtnREFBT1IsT0FBTTswREFBWTs7Ozs7OzBEQUMxQiw4REFBQ1E7Z0RBQU9SLE9BQU07MERBQVc7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FHN0IsOERBQUNaO2dDQUFJQyxXQUFVOzBDQUNiLDRFQUFDRztvQ0FDQ0MsU0FBU3hFO29DQUNUeUUsVUFBVWhIO29DQUNWMkcsV0FBVTs4Q0FFVDNHLFlBQVksZUFBZTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFPbkNSLFNBQVN5SCxNQUFNLEdBQUcsbUJBQ2pCLDhEQUFDUDtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs7OENBQ0MsOERBQUNHO29DQUFFRixXQUFVOzhDQUF3Qjs7Ozs7OzhDQUNyQyw4REFBQ0U7b0NBQUVGLFdBQVU7OENBQW1DN0c7Ozs7Ozs7Ozs7OztzQ0FFbEQsOERBQUM0Rzs7OENBQ0MsOERBQUNHO29DQUFFRixXQUFVOzhDQUF3Qjs7Ozs7OzhDQUNyQyw4REFBQ0U7b0NBQUVGLFdBQVU7OENBQW1DckMsaUJBQWlCMkMsTUFBTTs7Ozs7Ozs7Ozs7O3NDQUV6RSw4REFBQ1A7OzhDQUNDLDhEQUFDRztvQ0FBRUYsV0FBVTs4Q0FBd0I7Ozs7Ozs4Q0FDckMsOERBQUNFO29DQUFFRixXQUFVOzhDQUFtQ2pEOzs7Ozs7Ozs7Ozs7c0NBRWxELDhEQUFDZ0Q7OzhDQUNDLDhEQUFDRztvQ0FBRUYsV0FBVTs4Q0FBd0I7Ozs7Ozs4Q0FDckMsOERBQUNFO29DQUFFRixXQUFVOzt3Q0FDVmxFLFdBQVdyQzt3Q0FBVzt3Q0FBSXFDLFdBQVduQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBUS9DSix1QkFDQyw4REFBQ3dHO2dCQUFJQyxXQUFVOzBCQUNaekc7Ozs7OztZQUtKRiwwQkFDQyw4REFBQzBHO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0Q7d0JBQUlDLFdBQVU7Ozs7OztrQ0FDZiw4REFBQ1E7d0JBQUtSLFdBQVU7a0NBQXFCOzs7Ozs7Ozs7Ozt1QkFFckNyQyxpQkFBaUIyQyxNQUFNLEtBQUssa0JBQzlCLDhEQUFDUDtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0U7b0JBQUVGLFdBQVU7OEJBQ1ZuSCxTQUFTeUgsTUFBTSxLQUFLLElBQ2pCLGdEQUNBOzs7Ozs7Ozs7O3FDQUlSLDhEQUFDUDtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ29CO29CQUFNcEIsV0FBVTs7c0NBQ2YsOERBQUNxQjs0QkFBTXJCLFdBQVU7c0NBQ2YsNEVBQUNzQjs7a0RBQ0MsOERBQUNDO3dDQUFHdkIsV0FBVTtrREFBb0I7Ozs7OztrREFHbEMsOERBQUN1Qjt3Q0FBR3ZCLFdBQVU7a0RBQW9COzs7Ozs7a0RBR2xDLDhEQUFDdUI7d0NBQUd2QixXQUFVO2tEQUFvQjs7Ozs7O2tEQUdsQyw4REFBQ3VCO3dDQUFHdkIsV0FBVTtrREFBb0I7Ozs7OztrREFHbEMsOERBQUN1Qjt3Q0FBR3ZCLFdBQVU7a0RBQW9COzs7Ozs7a0RBR2xDLDhEQUFDdUI7d0NBQUd2QixXQUFVO2tEQUFvQjs7Ozs7O2tEQUdsQyw4REFBQ3VCO3dDQUFHdkIsV0FBVTtrREFBb0I7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQUt0Qyw4REFBQ3dCOzRCQUFNeEIsV0FBVTtzQ0FDZHJDLGlCQUFpQmdCLEdBQUcsQ0FBQyxDQUFDekIsb0JBQ3JCLDhEQUFDb0U7b0NBQWdCdEIsV0FBVTs7c0RBQ3pCLDhEQUFDeUI7NENBQUd6QixXQUFVO3NEQUNYbEUsV0FBV29CLElBQUkwQixhQUFhOzs7Ozs7c0RBRS9CLDhEQUFDNkM7NENBQUd6QixXQUFVO3NEQUNYM0QsV0FBV2EsSUFBSTBCLGFBQWE7Ozs7OztzREFFL0IsOERBQUM2Qzs0Q0FBR3pCLFdBQVU7c0RBQ1g5QyxJQUFJcUIsY0FBYyxHQUFHbEMsV0FBV2EsSUFBSXFCLGNBQWMsSUFBSTs7Ozs7O3NEQUV6RCw4REFBQ2tEOzRDQUFHekIsV0FBVTtzREFDWDlDLElBQUlDLFdBQVcsSUFBSTs7Ozs7O3NEQUV0Qiw4REFBQ3NFOzRDQUFHekIsV0FBVTtzREFDWDlDLElBQUlhLGlCQUFpQixJQUFJOzs7Ozs7c0RBRTVCLDhEQUFDMEQ7NENBQUd6QixXQUFVO3NEQUNaLDRFQUFDUTtnREFBS1IsV0FBVyxDQUFDLE1BQU0sRUFBRXRELGVBQWVRLElBQUlyRCxNQUFNLEVBQUUsQ0FBQzswREFDbkRxRCxJQUFJckQsTUFBTSxDQUFDNkgsTUFBTSxDQUFDLEdBQUdDLFdBQVcsS0FBS3pFLElBQUlyRCxNQUFNLENBQUMrSCxLQUFLLENBQUM7Ozs7Ozs7Ozs7O3NEQUczRCw4REFBQ0g7NENBQUd6QixXQUFVO3NEQUNYMUIscUJBQXFCcEIscUJBQ3BCLDhEQUFDaUQ7Z0RBQ0NDLFNBQVMsSUFBTWxDLGtCQUFrQmhCO2dEQUNqQzhDLFdBQVU7MERBQ1g7Ozs7O3FFQUlELDhEQUFDUTtnREFBS1IsV0FBVTswREFBZ0I7Ozs7Ozs7Ozs7OzttQ0E5QjdCOUMsSUFBSStELEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQXlDeEJsSSxhQUFhLG1CQUNaLDhEQUFDZ0g7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDRDt3QkFBSUMsV0FBVTs7NEJBQXdCOzRCQUN2Qi9HOzRCQUFZOzRCQUFLRjs0QkFBVzs0QkFBR0k7NEJBQVc7Ozs7Ozs7a0NBRTFELDhEQUFDNEc7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRztnQ0FDQ0MsU0FBUyxJQUFNdkUsaUJBQWlCNUMsY0FBYztnQ0FDOUNvSCxVQUFVcEgsZ0JBQWdCO2dDQUMxQitHLFdBQVU7MENBQ1g7Ozs7Ozs0QkFHQTZCLE1BQU1DLElBQUksQ0FBQztnQ0FBRXhCLFFBQVF5QixLQUFLQyxHQUFHLENBQUMsR0FBR2pKOzRCQUFZLEdBQUcsQ0FBQ2tKLEdBQUdDO2dDQUNuRCxNQUFNakgsT0FBT2lILElBQUk7Z0NBQ2pCLHFCQUNFLDhEQUFDL0I7b0NBRUNDLFNBQVMsSUFBTXZFLGlCQUFpQlo7b0NBQ2hDK0UsV0FBVyxDQUFDLHFEQUFxRCxFQUMvRC9HLGdCQUFnQmdDLE9BQ1osMkJBQ0EsZ0NBQ0wsQ0FBQzs4Q0FFREE7bUNBUklBOzs7Ozs0QkFXWDswQ0FDQSw4REFBQ2tGO2dDQUNDQyxTQUFTLElBQU12RSxpQkFBaUI1QyxjQUFjO2dDQUM5Q29ILFVBQVVwSCxnQkFBZ0JGO2dDQUMxQmlILFdBQVU7MENBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQVFOL0YscUJBQXFCRSxpQ0FDcEIsOERBQUM0RjtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUN0SCxpRkFBbUJBO3dCQUNsQnlKLFdBQVdoSSxnQkFBZ0I4RyxFQUFFO3dCQUM3Qm1CLGdCQUFnQmpJLGdCQUFnQnlFLGFBQWE7d0JBQzdDeUQsaUJBQWlCbEksZ0JBQWdCb0UsY0FBYyxJQUFJO3dCQUNuRCtELFdBQVdqRTt3QkFDWGtFLFVBQVVuRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU94QiIsInNvdXJjZXMiOlsid2VicGFjazovL2ZsZXhhaXItZnJvbnRlbmQvLi9zcmMvY29tcG9uZW50cy90aW1lL1RpbWVMb2dzLnRzeD84MmZhIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcclxuXHJcbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xyXG5pbXBvcnQgeyB1c2VBdXRoIH0gZnJvbSAnQC9jb250ZXh0cy9BdXRoQ29udGV4dCc7XHJcbmltcG9ydCB7IGFwaVJlcXVlc3QgfSBmcm9tICdAL2xpYi9hdXRoJztcclxuaW1wb3J0IEFwcHJvdmFsUmVxdWVzdEZvcm0gZnJvbSAnQC9jb21wb25lbnRzL2FwcHJvdmFscy9BcHByb3ZhbFJlcXVlc3RGb3JtJztcclxuLy8gaW1wb3J0IHsgQ2hldnJvblVwSWNvbiwgQ2hldnJvbkRvd25JY29uLCBNYWduaWZ5aW5nR2xhc3NJY29uLCBBcnJvd0Rvd25UcmF5SWNvbiB9IGZyb20gJ0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZSc7XHJcblxyXG5pbnRlcmZhY2UgVGltZUxvZyB7XHJcbiAgaWQ6IG51bWJlcjtcclxuICB1c2VyX2lkOiBudW1iZXI7XHJcbiAgY2xvY2tfaW5fdGltZTogc3RyaW5nO1xyXG4gIGNsb2NrX291dF90aW1lPzogc3RyaW5nO1xyXG4gIHRvdGFsX2hvdXJzPzogc3RyaW5nO1xyXG4gIGJyZWFrX21pbnV0ZXM/OiBudW1iZXI7XHJcbiAgb3ZlcnRpbWVfbWludXRlcz86IG51bWJlcjtcclxuICBjbG9ja19pbl9sb2NhdGlvbj86IHN0cmluZztcclxuICBjbG9ja19vdXRfbG9jYXRpb24/OiBzdHJpbmc7XHJcbiAgbm90ZXM/OiBzdHJpbmc7XHJcbiAgc3RhdHVzOiAnYWN0aXZlJyB8ICdjb21wbGV0ZWQnIHwgJ2FwcHJvdmVkJztcclxuICBjcmVhdGVkX2F0OiBzdHJpbmc7XHJcbiAgdXBkYXRlZF9hdDogc3RyaW5nO1xyXG59XHJcblxyXG5pbnRlcmZhY2UgVGltZUxvZ3NSZXNwb25zZSB7XHJcbiAgbG9nczogVGltZUxvZ1tdO1xyXG4gIHRvdGFsQ291bnQ6IG51bWJlcjtcclxuICB0b3RhbFBhZ2VzOiBudW1iZXI7XHJcbiAgY3VycmVudFBhZ2U6IG51bWJlcjtcclxufVxyXG5cclxuLyoqXHJcbiAqIFRpbWUgTG9ncyBjb21wb25lbnQgZm9yIHZpZXdpbmcgYW5kIG1hbmFnaW5nIHRpbWUgZW50cmllc1xyXG4gKiBEaXNwbGF5cyBwYWdpbmF0ZWQgbGlzdCBvZiB0aW1lIGxvZ3Mgd2l0aCBmaWx0ZXJpbmcgb3B0aW9uc1xyXG4gKi9cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gVGltZUxvZ3MoKSB7XHJcbiAgY29uc3QgeyB1c2VyIH0gPSB1c2VBdXRoKCk7XHJcbiAgY29uc3QgW3RpbWVMb2dzLCBzZXRUaW1lTG9nc10gPSB1c2VTdGF0ZTxUaW1lTG9nW10+KFtdKTtcclxuICBjb25zdCBbdG90YWxQYWdlcywgc2V0VG90YWxQYWdlc10gPSB1c2VTdGF0ZSgxKTtcclxuICBjb25zdCBbY3VycmVudFBhZ2UsIHNldEN1cnJlbnRQYWdlXSA9IHVzZVN0YXRlKDEpO1xyXG4gIGNvbnN0IFt0b3RhbENvdW50LCBzZXRUb3RhbENvdW50XSA9IHVzZVN0YXRlKDApO1xyXG4gIGNvbnN0IFtpc0xvYWRpbmcsIHNldElzTG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW2Vycm9yLCBzZXRFcnJvcl0gPSB1c2VTdGF0ZSgnJyk7XHJcblxyXG4gIC8vIEZpbHRlciBzdGF0ZXNcclxuICBjb25zdCBbc3RhcnREYXRlLCBzZXRTdGFydERhdGVdID0gdXNlU3RhdGUoJycpO1xyXG4gIGNvbnN0IFtlbmREYXRlLCBzZXRFbmREYXRlXSA9IHVzZVN0YXRlKCcnKTtcclxuICBjb25zdCBbc3RhdHVzLCBzZXRTdGF0dXNdID0gdXNlU3RhdGUoJ2FsbCcpO1xyXG4gIGNvbnN0IFtzZWFyY2hUZXJtLCBzZXRTZWFyY2hUZXJtXSA9IHVzZVN0YXRlKCcnKTtcclxuXHJcbiAgLy8gQXBwcm92YWwgcmVxdWVzdCBtb2RhbCBzdGF0ZVxyXG4gIGNvbnN0IFtzaG93QXBwcm92YWxNb2RhbCwgc2V0U2hvd0FwcHJvdmFsTW9kYWxdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtzZWxlY3RlZFRpbWVMb2csIHNldFNlbGVjdGVkVGltZUxvZ10gPSB1c2VTdGF0ZTxUaW1lTG9nIHwgbnVsbD4obnVsbCk7XHJcblxyXG4gIC8vIEluaXRpYWxpemUgd2l0aCBjdXJyZW50IHdlZWtcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgY29uc3Qgbm93ID0gbmV3IERhdGUoKTtcclxuICAgIGNvbnN0IHdlZWtTdGFydCA9IG5ldyBEYXRlKG5vdy5zZXREYXRlKG5vdy5nZXREYXRlKCkgLSBub3cuZ2V0RGF5KCkpKTtcclxuICAgIGNvbnN0IHdlZWtFbmQgPSBuZXcgRGF0ZSh3ZWVrU3RhcnQpO1xyXG4gICAgd2Vla0VuZC5zZXREYXRlKHdlZWtTdGFydC5nZXREYXRlKCkgKyA2KTtcclxuXHJcbiAgICBzZXRTdGFydERhdGUod2Vla1N0YXJ0LnRvSVNPU3RyaW5nKCkuc3BsaXQoJ1QnKVswXSk7XHJcbiAgICBzZXRFbmREYXRlKHdlZWtFbmQudG9JU09TdHJpbmcoKS5zcGxpdCgnVCcpWzBdKTtcclxuICB9LCBbXSk7XHJcblxyXG4gIC8vIExvYWQgdGltZSBsb2dzIHdoZW4gZmlsdGVycyBvciBwYWdlIGNoYW5nZXNcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgaWYgKHN0YXJ0RGF0ZSAmJiBlbmREYXRlKSB7XHJcbiAgICAgIGxvYWRUaW1lTG9ncygpO1xyXG4gICAgfVxyXG4gIH0sIFtjdXJyZW50UGFnZSwgc3RhcnREYXRlLCBlbmREYXRlLCBzdGF0dXNdKTtcclxuXHJcbiAgLyoqXHJcbiAgICogTG9hZCB0aW1lIGxvZ3MgZnJvbSBBUEkgd2l0aCBjdXJyZW50IGZpbHRlcnNcclxuICAgKi9cclxuICBjb25zdCBsb2FkVGltZUxvZ3MgPSBhc3luYyAoKSA9PiB7XHJcbiAgICBzZXRJc0xvYWRpbmcodHJ1ZSk7XHJcbiAgICBzZXRFcnJvcignJyk7XHJcblxyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc3QgcGFyYW1zID0gbmV3IFVSTFNlYXJjaFBhcmFtcyh7XHJcbiAgICAgICAgcGFnZTogY3VycmVudFBhZ2UudG9TdHJpbmcoKSxcclxuICAgICAgICBsaW1pdDogJzEwJyxcclxuICAgICAgICBzdGFydF9kYXRlOiBzdGFydERhdGUsXHJcbiAgICAgICAgZW5kX2RhdGU6IGVuZERhdGUsXHJcbiAgICAgICAgLi4uKHN0YXR1cyAhPT0gJ2FsbCcgJiYgeyBzdGF0dXMgfSlcclxuICAgICAgfSk7XHJcblxyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaVJlcXVlc3QoYC90aW1lL2xvZ3M/JHtwYXJhbXN9YCk7XHJcblxyXG4gICAgICBpZiAocmVzcG9uc2Uuc3VjY2VzcyAmJiByZXNwb25zZS5kYXRhKSB7XHJcbiAgICAgICAgY29uc3QgZGF0YSA9IHJlc3BvbnNlLmRhdGEgYXMgVGltZUxvZ3NSZXNwb25zZTtcclxuICAgICAgICBzZXRUaW1lTG9ncyhkYXRhLmxvZ3MpO1xyXG4gICAgICAgIHNldFRvdGFsUGFnZXMoZGF0YS50b3RhbFBhZ2VzKTtcclxuICAgICAgICBzZXRUb3RhbENvdW50KGRhdGEudG90YWxDb3VudCk7XHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgc2V0RXJyb3IocmVzcG9uc2UubWVzc2FnZSB8fCAnRmFpbGVkIHRvIGxvYWQgdGltZSBsb2dzJyk7XHJcbiAgICAgIH1cclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIHNldEVycm9yKCdFcnJvciBsb2FkaW5nIHRpbWUgbG9ncy4gUGxlYXNlIHRyeSBhZ2Fpbi4nKTtcclxuICAgICAgY29uc29sZS5lcnJvcignVGltZSBsb2dzIGVycm9yOicsIGVycm9yKTtcclxuICAgIH0gZmluYWxseSB7XHJcbiAgICAgIHNldElzTG9hZGluZyhmYWxzZSk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgLyoqXHJcbiAgICogSGFuZGxlIGZpbHRlciBjaGFuZ2VzXHJcbiAgICovXHJcbiAgY29uc3QgaGFuZGxlRmlsdGVyQ2hhbmdlID0gKCkgPT4ge1xyXG4gICAgc2V0Q3VycmVudFBhZ2UoMSk7IC8vIFJlc2V0IHRvIGZpcnN0IHBhZ2Ugd2hlbiBmaWx0ZXJzIGNoYW5nZVxyXG4gICAgbG9hZFRpbWVMb2dzKCk7XHJcbiAgfTtcclxuXHJcbiAgLyoqXHJcbiAgICogSGFuZGxlIHBhZ2UgY2hhbmdlc1xyXG4gICAqL1xyXG4gIGNvbnN0IGhhbmRsZVBhZ2VDaGFuZ2UgPSAocGFnZTogbnVtYmVyKSA9PiB7XHJcbiAgICBzZXRDdXJyZW50UGFnZShwYWdlKTtcclxuICB9O1xyXG5cclxuICAvKipcclxuICAgKiBGb3JtYXQgZGF0ZSBmb3IgZGlzcGxheVxyXG4gICAqL1xyXG4gIGNvbnN0IGZvcm1hdERhdGUgPSAoZGF0ZVN0cmluZzogc3RyaW5nKSA9PiB7XHJcbiAgICBjb25zdCBkYXRlID0gbmV3IERhdGUoZGF0ZVN0cmluZyk7XHJcbiAgICByZXR1cm4gZGF0ZS50b0xvY2FsZURhdGVTdHJpbmcoJ2VuLVVTJywge1xyXG4gICAgICBtb250aDogJ3Nob3J0JyxcclxuICAgICAgZGF5OiAnbnVtZXJpYycsXHJcbiAgICAgIHllYXI6ICdudW1lcmljJ1xyXG4gICAgfSk7XHJcbiAgfTtcclxuXHJcbiAgLyoqXHJcbiAgICogRm9ybWF0IHRpbWUgZm9yIGRpc3BsYXlcclxuICAgKi9cclxuICBjb25zdCBmb3JtYXRUaW1lID0gKGRhdGVTdHJpbmc6IHN0cmluZykgPT4ge1xyXG4gICAgY29uc3QgZGF0ZSA9IG5ldyBEYXRlKGRhdGVTdHJpbmcpO1xyXG4gICAgcmV0dXJuIGRhdGUudG9Mb2NhbGVUaW1lU3RyaW5nKCdlbi1VUycsIHtcclxuICAgICAgaG91cjogJzItZGlnaXQnLFxyXG4gICAgICBtaW51dGU6ICcyLWRpZ2l0JyxcclxuICAgICAgaG91cjEyOiB0cnVlXHJcbiAgICB9KTtcclxuICB9O1xyXG5cclxuICAvKipcclxuICAgKiBHZXQgc3RhdHVzIGJhZGdlIGNvbG9yXHJcbiAgICovXHJcbiAgY29uc3QgZ2V0U3RhdHVzQmFkZ2UgPSAoc3RhdHVzOiBzdHJpbmcpID0+IHtcclxuICAgIGNvbnN0IGNvbG9ycyA9IHtcclxuICAgICAgYWN0aXZlOiAnYmctYmx1ZS0xMDAgdGV4dC1ibHVlLTgwMCcsXHJcbiAgICAgIGNvbXBsZXRlZDogJ2JnLWdyZWVuLTEwMCB0ZXh0LWdyZWVuLTgwMCcsXHJcbiAgICAgIGFwcHJvdmVkOiAnYmctcHVycGxlLTEwMCB0ZXh0LXB1cnBsZS04MDAnXHJcbiAgICB9O1xyXG4gICAgcmV0dXJuIGNvbG9yc1tzdGF0dXMgYXMga2V5b2YgdHlwZW9mIGNvbG9yc10gfHwgJ2JnLWdyYXktMTAwIHRleHQtZ3JheS04MDAnO1xyXG4gIH07XHJcblxyXG4gIC8qKlxyXG4gICAqIENhbGN1bGF0ZSB0b3RhbCBob3VycyBmb3IgdGhlIHBlcmlvZFxyXG4gICAqL1xyXG4gIGNvbnN0IGdldFRvdGFsSG91cnMgPSAoKSA9PiB7XHJcbiAgICByZXR1cm4gdGltZUxvZ3MucmVkdWNlKCh0b3RhbCwgbG9nKSA9PiB7XHJcbiAgICAgIGlmIChsb2cudG90YWxfaG91cnMpIHtcclxuICAgICAgICBjb25zdCBob3VycyA9IHBhcnNlRmxvYXQobG9nLnRvdGFsX2hvdXJzKTtcclxuICAgICAgICByZXR1cm4gdG90YWwgKyBob3VycztcclxuICAgICAgfVxyXG4gICAgICByZXR1cm4gdG90YWw7XHJcbiAgICB9LCAwKS50b0ZpeGVkKDIpO1xyXG4gIH07XHJcblxyXG4gIC8qKlxyXG4gICAqIFNldCBxdWljayBkYXRlIGZpbHRlcnNcclxuICAgKi9cclxuICBjb25zdCBzZXRRdWlja0RhdGVGaWx0ZXIgPSAoZGF5czogbnVtYmVyKSA9PiB7XHJcbiAgICBjb25zdCBlbmQgPSBuZXcgRGF0ZSgpO1xyXG4gICAgY29uc3Qgc3RhcnQgPSBuZXcgRGF0ZSgpO1xyXG4gICAgc3RhcnQuc2V0RGF0ZShlbmQuZ2V0RGF0ZSgpIC0gZGF5cyk7XHJcblxyXG4gICAgc2V0U3RhcnREYXRlKHN0YXJ0LnRvSVNPU3RyaW5nKCkuc3BsaXQoJ1QnKVswXSk7XHJcbiAgICBzZXRFbmREYXRlKGVuZC50b0lTT1N0cmluZygpLnNwbGl0KCdUJylbMF0pO1xyXG4gIH07XHJcblxyXG4gIC8qKlxyXG4gICAqIEZpbHRlciB0aW1lIGxvZ3MgYmFzZWQgb24gc2VhcmNoIHRlcm1cclxuICAgKi9cclxuICBjb25zdCBmaWx0ZXJlZFRpbWVMb2dzID0gdGltZUxvZ3MuZmlsdGVyKGxvZyA9PiB7XHJcbiAgICBpZiAoIXNlYXJjaFRlcm0pIHJldHVybiB0cnVlO1xyXG5cclxuICAgIGNvbnN0IHNlYXJjaExvd2VyID0gc2VhcmNoVGVybS50b0xvd2VyQ2FzZSgpO1xyXG4gICAgcmV0dXJuIChcclxuICAgICAgbG9nLmNsb2NrX2luX2xvY2F0aW9uPy50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHNlYXJjaExvd2VyKSB8fFxyXG4gICAgICBsb2cubm90ZXM/LnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoTG93ZXIpIHx8XHJcbiAgICAgIGxvZy5zdGF0dXMudG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhzZWFyY2hMb3dlcilcclxuICAgICk7XHJcbiAgfSk7XHJcblxyXG4gIC8qKlxyXG4gICAqIE9wZW4gYXBwcm92YWwgcmVxdWVzdCBtb2RhbFxyXG4gICAqL1xyXG4gIGNvbnN0IG9wZW5BcHByb3ZhbE1vZGFsID0gKHRpbWVMb2c6IFRpbWVMb2cpID0+IHtcclxuICAgIHNldFNlbGVjdGVkVGltZUxvZyh0aW1lTG9nKTtcclxuICAgIHNldFNob3dBcHByb3ZhbE1vZGFsKHRydWUpO1xyXG4gIH07XHJcblxyXG4gIC8qKlxyXG4gICAqIENsb3NlIGFwcHJvdmFsIHJlcXVlc3QgbW9kYWxcclxuICAgKi9cclxuICBjb25zdCBjbG9zZUFwcHJvdmFsTW9kYWwgPSAoKSA9PiB7XHJcbiAgICBzZXRTaG93QXBwcm92YWxNb2RhbChmYWxzZSk7XHJcbiAgICBzZXRTZWxlY3RlZFRpbWVMb2cobnVsbCk7XHJcbiAgfTtcclxuXHJcbiAgLyoqXHJcbiAgICogSGFuZGxlIGFwcHJvdmFsIHJlcXVlc3Qgc3VjY2Vzc1xyXG4gICAqL1xyXG4gIGNvbnN0IGhhbmRsZUFwcHJvdmFsU3VjY2VzcyA9ICgpID0+IHtcclxuICAgIGNsb3NlQXBwcm92YWxNb2RhbCgpO1xyXG4gICAgLy8gT3B0aW9uYWxseSByZWZyZXNoIHRoZSB0aW1lIGxvZ3MgdG8gc2hvdyB1cGRhdGVkIHN0YXR1c1xyXG4gICAgbG9hZFRpbWVMb2dzKCk7XHJcbiAgfTtcclxuXHJcbiAgLyoqXHJcbiAgICogQ2hlY2sgaWYgdGltZSBsb2cgY2FuIGJlIGNvcnJlY3RlZFxyXG4gICAqL1xyXG4gIGNvbnN0IGNhblJlcXVlc3RDb3JyZWN0aW9uID0gKHRpbWVMb2c6IFRpbWVMb2cpID0+IHtcclxuICAgIC8vIE9ubHkgYWxsb3cgY29ycmVjdGlvbiByZXF1ZXN0cyBmb3IgY29tcGxldGVkIHRpbWUgbG9nc1xyXG4gICAgcmV0dXJuIHRpbWVMb2cuc3RhdHVzID09PSAnY29tcGxldGVkJyAmJiB0aW1lTG9nLmNsb2NrX291dF90aW1lO1xyXG4gIH07XHJcblxyXG4gIC8qKlxyXG4gICAqIEV4cG9ydCB0aW1lIGxvZ3MgdG8gQ1NWXHJcbiAgICovXHJcbiAgY29uc3QgZXhwb3J0VG9DU1YgPSAoKSA9PiB7XHJcbiAgICBjb25zdCBoZWFkZXJzID0gWydEYXRlJywgJ0Nsb2NrIEluJywgJ0Nsb2NrIE91dCcsICdUb3RhbCBIb3VycycsICdMb2NhdGlvbicsICdTdGF0dXMnLCAnTm90ZXMnXTtcclxuICAgIGNvbnN0IGNzdkRhdGEgPSBmaWx0ZXJlZFRpbWVMb2dzLm1hcChsb2cgPT4gW1xyXG4gICAgICBmb3JtYXREYXRlKGxvZy5jbG9ja19pbl90aW1lKSxcclxuICAgICAgZm9ybWF0VGltZShsb2cuY2xvY2tfaW5fdGltZSksXHJcbiAgICAgIGxvZy5jbG9ja19vdXRfdGltZSA/IGZvcm1hdFRpbWUobG9nLmNsb2NrX291dF90aW1lKSA6ICcnLFxyXG4gICAgICBsb2cudG90YWxfaG91cnMgfHwgJycsXHJcbiAgICAgIGxvZy5jbG9ja19pbl9sb2NhdGlvbiB8fCAnJyxcclxuICAgICAgbG9nLnN0YXR1cyxcclxuICAgICAgbG9nLm5vdGVzIHx8ICcnXHJcbiAgICBdKTtcclxuXHJcbiAgICBjb25zdCBjc3ZDb250ZW50ID0gW2hlYWRlcnMsIC4uLmNzdkRhdGFdXHJcbiAgICAgIC5tYXAocm93ID0+IHJvdy5tYXAoZmllbGQgPT4gYFwiJHtmaWVsZH1cImApLmpvaW4oJywnKSlcclxuICAgICAgLmpvaW4oJ1xcbicpO1xyXG5cclxuICAgIGNvbnN0IGJsb2IgPSBuZXcgQmxvYihbY3N2Q29udGVudF0sIHsgdHlwZTogJ3RleHQvY3N2JyB9KTtcclxuICAgIGNvbnN0IHVybCA9IHdpbmRvdy5VUkwuY3JlYXRlT2JqZWN0VVJMKGJsb2IpO1xyXG4gICAgY29uc3QgYSA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ2EnKTtcclxuICAgIGEuaHJlZiA9IHVybDtcclxuICAgIGEuZG93bmxvYWQgPSBgdGltZS1sb2dzLSR7c3RhcnREYXRlfS10by0ke2VuZERhdGV9LmNzdmA7XHJcbiAgICBhLmNsaWNrKCk7XHJcbiAgICB3aW5kb3cuVVJMLnJldm9rZU9iamVjdFVSTCh1cmwpO1xyXG4gIH07XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbXBhY3QtY2FyZFwiPlxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTRcIj5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgc206ZmxleC1yb3cgc206aXRlbXMtY2VudGVyIHNtOmp1c3RpZnktYmV0d2VlblwiPlxyXG4gICAgICAgICAgPGRpdj5cclxuICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1ib2xkIHRleHQtZ3JheS04MDAgbWItMVwiPlRpbWUgTG9nczwvaDI+XHJcbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj5WaWV3IGFuZCBtYW5hZ2UgeW91ciB0aW1lIGVudHJpZXM8L3A+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtMyBzbTptdC0wXCI+XHJcbiAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICBvbkNsaWNrPXtleHBvcnRUb0NTVn1cclxuICAgICAgICAgICAgICBkaXNhYmxlZD17ZmlsdGVyZWRUaW1lTG9ncy5sZW5ndGggPT09IDB9XHJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYnRuIGJ0bi1zdWNjZXNzXCJcclxuICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgIPCfk6UgRXhwb3J0IENTVlxyXG4gICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L2Rpdj5cclxuXHJcbiAgICAgIHsvKiBTZWFyY2ggYW5kIFF1aWNrIEZpbHRlcnMgKi99XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JheS01MCByb3VuZGVkLWxnIHAtMyBtYi00XCI+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIGxnOmZsZXgtcm93IGxnOml0ZW1zLWNlbnRlciBsZzpqdXN0aWZ5LWJldHdlZW4gbWItM1wiPlxyXG4gICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktODAwIG1iLTIgbGc6bWItMFwiPlNlYXJjaCAmIEZpbHRlcnM8L2gzPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtd3JhcCBnYXAtMlwiPlxyXG4gICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0UXVpY2tEYXRlRmlsdGVyKDApfVxyXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJ0biBidG4tc20gYmctYmx1ZS0xMDAgaG92ZXI6YmctYmx1ZS0yMDAgdGV4dC1ibHVlLTgwMFwiXHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICBUb2RheVxyXG4gICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFF1aWNrRGF0ZUZpbHRlcig3KX1cclxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJidG4gYnRuLXNtIGJnLWJsdWUtMTAwIGhvdmVyOmJnLWJsdWUtMjAwIHRleHQtYmx1ZS04MDBcIlxyXG4gICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgTGFzdCA3IERheXNcclxuICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRRdWlja0RhdGVGaWx0ZXIoMzApfVxyXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJ0biBidG4tc20gYmctYmx1ZS0xMDAgaG92ZXI6YmctYmx1ZS0yMDAgdGV4dC1ibHVlLTgwMFwiXHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICBMYXN0IDMwIERheXNcclxuICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRRdWlja0RhdGVGaWx0ZXIoOTApfVxyXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJ0biBidG4tc20gYmctYmx1ZS0xMDAgaG92ZXI6YmctYmx1ZS0yMDAgdGV4dC1ibHVlLTgwMFwiXHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICBMYXN0IDMgTW9udGhzXHJcbiAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgIHsvKiBTZWFyY2ggQmFyICovfVxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItM1wiPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxyXG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBsZWZ0LTMgdG9wLTEvMiB0cmFuc2Zvcm0gLXRyYW5zbGF0ZS15LTEvMiB0ZXh0LWdyYXktNDAwXCI+8J+UjTwvc3Bhbj5cclxuICAgICAgICAgICAgPGlucHV0XHJcbiAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxyXG4gICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiU2VhcmNoIGJ5IGxvY2F0aW9uLCBub3Rlcywgb3Igc3RhdHVzLi4uXCJcclxuICAgICAgICAgICAgICB2YWx1ZT17c2VhcmNoVGVybX1cclxuICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFNlYXJjaFRlcm0oZS50YXJnZXQudmFsdWUpfVxyXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZvcm0taW5wdXQgcGwtMTBcIlxyXG4gICAgICAgICAgICAvPlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgIHsvKiBEYXRlIGFuZCBTdGF0dXMgRmlsdGVycyAqL31cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTQgZ2FwLTNcIj5cclxuICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgIDxsYWJlbCBodG1sRm9yPVwic3RhcnREYXRlXCIgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTFcIj5cclxuICAgICAgICAgICAgICBTdGFydCBEYXRlXHJcbiAgICAgICAgICAgIDwvbGFiZWw+XHJcbiAgICAgICAgICAgIDxpbnB1dFxyXG4gICAgICAgICAgICAgIHR5cGU9XCJkYXRlXCJcclxuICAgICAgICAgICAgICBpZD1cInN0YXJ0RGF0ZVwiXHJcbiAgICAgICAgICAgICAgdmFsdWU9e3N0YXJ0RGF0ZX1cclxuICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFN0YXJ0RGF0ZShlLnRhcmdldC52YWx1ZSl9XHJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZm9ybS1pbnB1dFwiXHJcbiAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgIDxsYWJlbCBodG1sRm9yPVwiZW5kRGF0ZVwiIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0xXCI+XHJcbiAgICAgICAgICAgICAgRW5kIERhdGVcclxuICAgICAgICAgICAgPC9sYWJlbD5cclxuICAgICAgICAgICAgPGlucHV0XHJcbiAgICAgICAgICAgICAgdHlwZT1cImRhdGVcIlxyXG4gICAgICAgICAgICAgIGlkPVwiZW5kRGF0ZVwiXHJcbiAgICAgICAgICAgICAgdmFsdWU9e2VuZERhdGV9XHJcbiAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRFbmREYXRlKGUudGFyZ2V0LnZhbHVlKX1cclxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmb3JtLWlucHV0XCJcclxuICAgICAgICAgICAgLz5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPGRpdj5cclxuICAgICAgICAgICAgPGxhYmVsIGh0bWxGb3I9XCJzdGF0dXNcIiBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMVwiPlxyXG4gICAgICAgICAgICAgIFN0YXR1c1xyXG4gICAgICAgICAgICA8L2xhYmVsPlxyXG4gICAgICAgICAgICA8c2VsZWN0XHJcbiAgICAgICAgICAgICAgaWQ9XCJzdGF0dXNcIlxyXG4gICAgICAgICAgICAgIHZhbHVlPXtzdGF0dXN9XHJcbiAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRTdGF0dXMoZS50YXJnZXQudmFsdWUpfVxyXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZvcm0taW5wdXRcIlxyXG4gICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cImFsbFwiPkFsbCBTdGF0dXNlczwvb3B0aW9uPlxyXG4gICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJhY3RpdmVcIj5BY3RpdmU8L29wdGlvbj5cclxuICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiY29tcGxldGVkXCI+Q29tcGxldGVkPC9vcHRpb24+XHJcbiAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cImFwcHJvdmVkXCI+QXBwcm92ZWQ8L29wdGlvbj5cclxuICAgICAgICAgICAgPC9zZWxlY3Q+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1lbmRcIj5cclxuICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZUZpbHRlckNoYW5nZX1cclxuICAgICAgICAgICAgICBkaXNhYmxlZD17aXNMb2FkaW5nfVxyXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJ0biBidG4tcHJpbWFyeSB3LWZ1bGxcIlxyXG4gICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAge2lzTG9hZGluZyA/ICdMb2FkaW5nLi4uJyA6ICdBcHBseSBGaWx0ZXJzJ31cclxuICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9kaXY+XHJcblxyXG4gICAgICB7LyogU3VtbWFyeSAqL31cclxuICAgICAge3RpbWVMb2dzLmxlbmd0aCA+IDAgJiYgKFxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctYmx1ZS01MCBib3JkZXIgYm9yZGVyLWJsdWUtMjAwIHJvdW5kZWQtbGcgcC00IG1iLTZcIj5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtNCBnYXAtNFwiPlxyXG4gICAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ibHVlLTYwMFwiPlRvdGFsIEVudHJpZXM8L3A+XHJcbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LWJvbGQgdGV4dC1ibHVlLTgwMFwiPnt0b3RhbENvdW50fTwvcD5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWJsdWUtNjAwXCI+U2hvd2luZzwvcD5cclxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtYm9sZCB0ZXh0LWJsdWUtODAwXCI+e2ZpbHRlcmVkVGltZUxvZ3MubGVuZ3RofTwvcD5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWJsdWUtNjAwXCI+VG90YWwgSG91cnM8L3A+XHJcbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LWJvbGQgdGV4dC1ibHVlLTgwMFwiPntnZXRUb3RhbEhvdXJzKCl9PC9wPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPGRpdj5cclxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtYmx1ZS02MDBcIj5EYXRlIFJhbmdlPC9wPlxyXG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGQgdGV4dC1ibHVlLTgwMFwiPlxyXG4gICAgICAgICAgICAgICAge2Zvcm1hdERhdGUoc3RhcnREYXRlKX0gLSB7Zm9ybWF0RGF0ZShlbmREYXRlKX1cclxuICAgICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgICl9XHJcblxyXG4gICAgICB7LyogRXJyb3IgRGlzcGxheSAqL31cclxuICAgICAge2Vycm9yICYmIChcclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXJlZC0xMDAgYm9yZGVyIGJvcmRlci1yZWQtMjAwIHRleHQtcmVkLTcwMCBweC00IHB5LTMgcm91bmRlZC1sZyBtYi02XCI+XHJcbiAgICAgICAgICB7ZXJyb3J9XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgICl9XHJcblxyXG4gICAgICB7LyogVGltZSBMb2dzIFRhYmxlICovfVxyXG4gICAgICB7aXNMb2FkaW5nID8gKFxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWNlbnRlciBpdGVtcy1jZW50ZXIgcHktOFwiPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGgtNiB3LTYgYm9yZGVyLWItMiBib3JkZXItYmx1ZS02MDBcIj48L2Rpdj5cclxuICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1sLTIgdGV4dC1ncmF5LTYwMFwiPkxvYWRpbmcgdGltZSBsb2dzLi4uPC9zcGFuPlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICApIDogZmlsdGVyZWRUaW1lTG9ncy5sZW5ndGggPT09IDAgPyAoXHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBweS04XCI+XHJcbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNTAwXCI+XHJcbiAgICAgICAgICAgIHt0aW1lTG9ncy5sZW5ndGggPT09IDBcclxuICAgICAgICAgICAgICA/IFwiTm8gdGltZSBsb2dzIGZvdW5kIGZvciB0aGUgc2VsZWN0ZWQgcGVyaW9kLlwiXHJcbiAgICAgICAgICAgICAgOiBcIk5vIHRpbWUgbG9ncyBtYXRjaCB5b3VyIHNlYXJjaCBjcml0ZXJpYS5cIn1cclxuICAgICAgICAgIDwvcD5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgKSA6IChcclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm92ZXJmbG93LXgtYXV0b1wiPlxyXG4gICAgICAgICAgPHRhYmxlIGNsYXNzTmFtZT1cInRhYmxlXCI+XHJcbiAgICAgICAgICAgIDx0aGVhZCBjbGFzc05hbWU9XCJ0YWJsZS1oZWFkZXJcIj5cclxuICAgICAgICAgICAgICA8dHI+XHJcbiAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwidGFibGUtaGVhZGVyLWNlbGxcIj5cclxuICAgICAgICAgICAgICAgICAgRGF0ZVxyXG4gICAgICAgICAgICAgICAgPC90aD5cclxuICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJ0YWJsZS1oZWFkZXItY2VsbFwiPlxyXG4gICAgICAgICAgICAgICAgICBDbG9jayBJblxyXG4gICAgICAgICAgICAgICAgPC90aD5cclxuICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJ0YWJsZS1oZWFkZXItY2VsbFwiPlxyXG4gICAgICAgICAgICAgICAgICBDbG9jayBPdXRcclxuICAgICAgICAgICAgICAgIDwvdGg+XHJcbiAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwidGFibGUtaGVhZGVyLWNlbGxcIj5cclxuICAgICAgICAgICAgICAgICAgVG90YWwgSG91cnNcclxuICAgICAgICAgICAgICAgIDwvdGg+XHJcbiAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwidGFibGUtaGVhZGVyLWNlbGxcIj5cclxuICAgICAgICAgICAgICAgICAgTG9jYXRpb25cclxuICAgICAgICAgICAgICAgIDwvdGg+XHJcbiAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwidGFibGUtaGVhZGVyLWNlbGxcIj5cclxuICAgICAgICAgICAgICAgICAgU3RhdHVzXHJcbiAgICAgICAgICAgICAgICA8L3RoPlxyXG4gICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInRhYmxlLWhlYWRlci1jZWxsXCI+XHJcbiAgICAgICAgICAgICAgICAgIEFjdGlvbnNcclxuICAgICAgICAgICAgICAgIDwvdGg+XHJcbiAgICAgICAgICAgICAgPC90cj5cclxuICAgICAgICAgICAgPC90aGVhZD5cclxuICAgICAgICAgICAgPHRib2R5IGNsYXNzTmFtZT1cImJnLXdoaXRlIGRpdmlkZS15IGRpdmlkZS1ncmF5LTIwMFwiPlxyXG4gICAgICAgICAgICAgIHtmaWx0ZXJlZFRpbWVMb2dzLm1hcCgobG9nKSA9PiAoXHJcbiAgICAgICAgICAgICAgICA8dHIga2V5PXtsb2cuaWR9IGNsYXNzTmFtZT1cInRhYmxlLXJvd1wiPlxyXG4gICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwidGFibGUtY2VsbFwiPlxyXG4gICAgICAgICAgICAgICAgICAgIHtmb3JtYXREYXRlKGxvZy5jbG9ja19pbl90aW1lKX1cclxuICAgICAgICAgICAgICAgICAgPC90ZD5cclxuICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInRhYmxlLWNlbGxcIj5cclxuICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0VGltZShsb2cuY2xvY2tfaW5fdGltZSl9XHJcbiAgICAgICAgICAgICAgICAgIDwvdGQ+XHJcbiAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJ0YWJsZS1jZWxsXCI+XHJcbiAgICAgICAgICAgICAgICAgICAge2xvZy5jbG9ja19vdXRfdGltZSA/IGZvcm1hdFRpbWUobG9nLmNsb2NrX291dF90aW1lKSA6ICctJ31cclxuICAgICAgICAgICAgICAgICAgPC90ZD5cclxuICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInRhYmxlLWNlbGwgZm9udC1tZWRpdW1cIj5cclxuICAgICAgICAgICAgICAgICAgICB7bG9nLnRvdGFsX2hvdXJzIHx8ICctJ31cclxuICAgICAgICAgICAgICAgICAgPC90ZD5cclxuICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInRhYmxlLWNlbGwgdGV4dC1ncmF5LTUwMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgIHtsb2cuY2xvY2tfaW5fbG9jYXRpb24gfHwgJ05vdCBzcGVjaWZpZWQnfVxyXG4gICAgICAgICAgICAgICAgICA8L3RkPlxyXG4gICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwidGFibGUtY2VsbFwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17YGJhZGdlICR7Z2V0U3RhdHVzQmFkZ2UobG9nLnN0YXR1cyl9YH0+XHJcbiAgICAgICAgICAgICAgICAgICAgICB7bG9nLnN0YXR1cy5jaGFyQXQoMCkudG9VcHBlckNhc2UoKSArIGxvZy5zdGF0dXMuc2xpY2UoMSl9XHJcbiAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICA8L3RkPlxyXG4gICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwidGFibGUtY2VsbCBmb250LW1lZGl1bVwiPlxyXG4gICAgICAgICAgICAgICAgICAgIHtjYW5SZXF1ZXN0Q29ycmVjdGlvbihsb2cpID8gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBvcGVuQXBwcm92YWxNb2RhbChsb2cpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWJsdWUtNjAwIGhvdmVyOnRleHQtYmx1ZS05MDAgdHJhbnNpdGlvbiBkdXJhdGlvbi0yMDBcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBSZXF1ZXN0IENvcnJlY3Rpb25cclxuICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwXCI+LTwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICA8L3RkPlxyXG4gICAgICAgICAgICAgICAgPC90cj5cclxuICAgICAgICAgICAgICApKX1cclxuICAgICAgICAgICAgPC90Ym9keT5cclxuICAgICAgICAgIDwvdGFibGU+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgICl9XHJcblxyXG4gICAgICB7LyogUGFnaW5hdGlvbiAqL31cclxuICAgICAge3RvdGFsUGFnZXMgPiAxICYmIChcclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtdC02XCI+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTcwMFwiPlxyXG4gICAgICAgICAgICBTaG93aW5nIHBhZ2Uge2N1cnJlbnRQYWdlfSBvZiB7dG90YWxQYWdlc30gKHt0b3RhbENvdW50fSB0b3RhbCBlbnRyaWVzKVxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC0yXCI+XHJcbiAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVQYWdlQ2hhbmdlKGN1cnJlbnRQYWdlIC0gMSl9XHJcbiAgICAgICAgICAgICAgZGlzYWJsZWQ9e2N1cnJlbnRQYWdlID09PSAxfVxyXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTMgcHktMSB0ZXh0LXNtIGJnLWdyYXktMjAwIGhvdmVyOmJnLWdyYXktMzAwIGRpc2FibGVkOmJnLWdyYXktMTAwIGRpc2FibGVkOnRleHQtZ3JheS00MDAgcm91bmRlZC1tZCB0cmFuc2l0aW9uIGR1cmF0aW9uLTIwMFwiXHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICBQcmV2aW91c1xyXG4gICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICAge0FycmF5LmZyb20oeyBsZW5ndGg6IE1hdGgubWluKDUsIHRvdGFsUGFnZXMpIH0sIChfLCBpKSA9PiB7XHJcbiAgICAgICAgICAgICAgY29uc3QgcGFnZSA9IGkgKyAxO1xyXG4gICAgICAgICAgICAgIHJldHVybiAoXHJcbiAgICAgICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgIGtleT17cGFnZX1cclxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlUGFnZUNoYW5nZShwYWdlKX1cclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcHgtMyBweS0xIHRleHQtc20gcm91bmRlZC1tZCB0cmFuc2l0aW9uIGR1cmF0aW9uLTIwMCAke1xyXG4gICAgICAgICAgICAgICAgICAgIGN1cnJlbnRQYWdlID09PSBwYWdlXHJcbiAgICAgICAgICAgICAgICAgICAgICA/ICdiZy1ibHVlLTYwMCB0ZXh0LXdoaXRlJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgOiAnYmctZ3JheS0yMDAgaG92ZXI6YmctZ3JheS0zMDAnXHJcbiAgICAgICAgICAgICAgICAgIH1gfVxyXG4gICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICB7cGFnZX1cclxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICAgICk7XHJcbiAgICAgICAgICAgIH0pfVxyXG4gICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlUGFnZUNoYW5nZShjdXJyZW50UGFnZSArIDEpfVxyXG4gICAgICAgICAgICAgIGRpc2FibGVkPXtjdXJyZW50UGFnZSA9PT0gdG90YWxQYWdlc31cclxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJweC0zIHB5LTEgdGV4dC1zbSBiZy1ncmF5LTIwMCBob3ZlcjpiZy1ncmF5LTMwMCBkaXNhYmxlZDpiZy1ncmF5LTEwMCBkaXNhYmxlZDp0ZXh0LWdyYXktNDAwIHJvdW5kZWQtbWQgdHJhbnNpdGlvbiBkdXJhdGlvbi0yMDBcIlxyXG4gICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgTmV4dFxyXG4gICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICApfVxyXG5cclxuICAgICAgey8qIEFwcHJvdmFsIFJlcXVlc3QgTW9kYWwgKi99XHJcbiAgICAgIHtzaG93QXBwcm92YWxNb2RhbCAmJiBzZWxlY3RlZFRpbWVMb2cgJiYgKFxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZml4ZWQgaW5zZXQtMCBiZy1ncmF5LTYwMCBiZy1vcGFjaXR5LTUwIG92ZXJmbG93LXktYXV0byBoLWZ1bGwgdy1mdWxsIHotNTBcIj5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgdG9wLTIwIG14LWF1dG8gcC01IGJvcmRlciB3LWZ1bGwgbWF4LXctMnhsIHNoYWRvdy1sZyByb3VuZGVkLW1kIGJnLXdoaXRlXCI+XHJcbiAgICAgICAgICAgIDxBcHByb3ZhbFJlcXVlc3RGb3JtXHJcbiAgICAgICAgICAgICAgdGltZUxvZ0lkPXtzZWxlY3RlZFRpbWVMb2cuaWR9XHJcbiAgICAgICAgICAgICAgY3VycmVudENsb2NrSW49e3NlbGVjdGVkVGltZUxvZy5jbG9ja19pbl90aW1lfVxyXG4gICAgICAgICAgICAgIGN1cnJlbnRDbG9ja091dD17c2VsZWN0ZWRUaW1lTG9nLmNsb2NrX291dF90aW1lIHx8ICcnfVxyXG4gICAgICAgICAgICAgIG9uU3VjY2Vzcz17aGFuZGxlQXBwcm92YWxTdWNjZXNzfVxyXG4gICAgICAgICAgICAgIG9uQ2FuY2VsPXtjbG9zZUFwcHJvdmFsTW9kYWx9XHJcbiAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgKX1cclxuICAgIDwvZGl2PlxyXG4gICk7XHJcbn0iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZUF1dGgiLCJhcGlSZXF1ZXN0IiwiQXBwcm92YWxSZXF1ZXN0Rm9ybSIsIlRpbWVMb2dzIiwidXNlciIsInRpbWVMb2dzIiwic2V0VGltZUxvZ3MiLCJ0b3RhbFBhZ2VzIiwic2V0VG90YWxQYWdlcyIsImN1cnJlbnRQYWdlIiwic2V0Q3VycmVudFBhZ2UiLCJ0b3RhbENvdW50Iiwic2V0VG90YWxDb3VudCIsImlzTG9hZGluZyIsInNldElzTG9hZGluZyIsImVycm9yIiwic2V0RXJyb3IiLCJzdGFydERhdGUiLCJzZXRTdGFydERhdGUiLCJlbmREYXRlIiwic2V0RW5kRGF0ZSIsInN0YXR1cyIsInNldFN0YXR1cyIsInNlYXJjaFRlcm0iLCJzZXRTZWFyY2hUZXJtIiwic2hvd0FwcHJvdmFsTW9kYWwiLCJzZXRTaG93QXBwcm92YWxNb2RhbCIsInNlbGVjdGVkVGltZUxvZyIsInNldFNlbGVjdGVkVGltZUxvZyIsIm5vdyIsIkRhdGUiLCJ3ZWVrU3RhcnQiLCJzZXREYXRlIiwiZ2V0RGF0ZSIsImdldERheSIsIndlZWtFbmQiLCJ0b0lTT1N0cmluZyIsInNwbGl0IiwibG9hZFRpbWVMb2dzIiwicGFyYW1zIiwiVVJMU2VhcmNoUGFyYW1zIiwicGFnZSIsInRvU3RyaW5nIiwibGltaXQiLCJzdGFydF9kYXRlIiwiZW5kX2RhdGUiLCJyZXNwb25zZSIsInN1Y2Nlc3MiLCJkYXRhIiwibG9ncyIsIm1lc3NhZ2UiLCJjb25zb2xlIiwiaGFuZGxlRmlsdGVyQ2hhbmdlIiwiaGFuZGxlUGFnZUNoYW5nZSIsImZvcm1hdERhdGUiLCJkYXRlU3RyaW5nIiwiZGF0ZSIsInRvTG9jYWxlRGF0ZVN0cmluZyIsIm1vbnRoIiwiZGF5IiwieWVhciIsImZvcm1hdFRpbWUiLCJ0b0xvY2FsZVRpbWVTdHJpbmciLCJob3VyIiwibWludXRlIiwiaG91cjEyIiwiZ2V0U3RhdHVzQmFkZ2UiLCJjb2xvcnMiLCJhY3RpdmUiLCJjb21wbGV0ZWQiLCJhcHByb3ZlZCIsImdldFRvdGFsSG91cnMiLCJyZWR1Y2UiLCJ0b3RhbCIsImxvZyIsInRvdGFsX2hvdXJzIiwiaG91cnMiLCJwYXJzZUZsb2F0IiwidG9GaXhlZCIsInNldFF1aWNrRGF0ZUZpbHRlciIsImRheXMiLCJlbmQiLCJzdGFydCIsImZpbHRlcmVkVGltZUxvZ3MiLCJmaWx0ZXIiLCJzZWFyY2hMb3dlciIsInRvTG93ZXJDYXNlIiwiY2xvY2tfaW5fbG9jYXRpb24iLCJpbmNsdWRlcyIsIm5vdGVzIiwib3BlbkFwcHJvdmFsTW9kYWwiLCJ0aW1lTG9nIiwiY2xvc2VBcHByb3ZhbE1vZGFsIiwiaGFuZGxlQXBwcm92YWxTdWNjZXNzIiwiY2FuUmVxdWVzdENvcnJlY3Rpb24iLCJjbG9ja19vdXRfdGltZSIsImV4cG9ydFRvQ1NWIiwiaGVhZGVycyIsImNzdkRhdGEiLCJtYXAiLCJjbG9ja19pbl90aW1lIiwiY3N2Q29udGVudCIsInJvdyIsImZpZWxkIiwiam9pbiIsImJsb2IiLCJCbG9iIiwidHlwZSIsInVybCIsIndpbmRvdyIsIlVSTCIsImNyZWF0ZU9iamVjdFVSTCIsImEiLCJkb2N1bWVudCIsImNyZWF0ZUVsZW1lbnQiLCJocmVmIiwiZG93bmxvYWQiLCJjbGljayIsInJldm9rZU9iamVjdFVSTCIsImRpdiIsImNsYXNzTmFtZSIsImgyIiwicCIsImJ1dHRvbiIsIm9uQ2xpY2siLCJkaXNhYmxlZCIsImxlbmd0aCIsImgzIiwic3BhbiIsImlucHV0IiwicGxhY2Vob2xkZXIiLCJ2YWx1ZSIsIm9uQ2hhbmdlIiwiZSIsInRhcmdldCIsImxhYmVsIiwiaHRtbEZvciIsImlkIiwic2VsZWN0Iiwib3B0aW9uIiwidGFibGUiLCJ0aGVhZCIsInRyIiwidGgiLCJ0Ym9keSIsInRkIiwiY2hhckF0IiwidG9VcHBlckNhc2UiLCJzbGljZSIsIkFycmF5IiwiZnJvbSIsIk1hdGgiLCJtaW4iLCJfIiwiaSIsInRpbWVMb2dJZCIsImN1cnJlbnRDbG9ja0luIiwiY3VycmVudENsb2NrT3V0Iiwib25TdWNjZXNzIiwib25DYW5jZWwiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/time/TimeLogs.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(ssr)/./src/lib/auth.ts\");\n/**\r\n * Authentication Context Provider\r\n * Manages global authentication state and provides auth methods\r\n */ /* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n/**\r\n * Authentication Provider Component\r\n * Wraps the app and provides authentication state and methods\r\n * @param children - Child components to render\r\n */ function AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    /**\r\n   * Clear any authentication errors\r\n   */ const clearError = ()=>setError(null);\n    /**\r\n   * Initialize authentication state on mount\r\n   */ (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        initializeAuth();\n    }, []);\n    /**\r\n   * Initialize authentication state\r\n   * Checks for existing tokens and validates them\r\n   */ const initializeAuth = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            if (!(0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.isAuthenticated)()) {\n                setLoading(false);\n                return;\n            }\n            const token = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.getAccessToken)();\n            if (!token || (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.isTokenExpired)(token)) {\n                // Try to refresh token\n                try {\n                    await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.refreshTokens)();\n                } catch (error) {\n                    (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.clearTokens)();\n                    setLoading(false);\n                    return;\n                }\n            }\n            // Verify token and get user data\n            const { valid, user: userData } = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.verifyToken)();\n            if (valid && userData) {\n                setUser(userData);\n            } else {\n                (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.clearTokens)();\n            }\n        } catch (error) {\n            console.error(\"Auth initialization error:\", error);\n            (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.clearTokens)();\n        } finally{\n            setLoading(false);\n        }\n    };\n    /**\r\n   * Login user with credentials\r\n   * @param credentials - Login credentials\r\n   */ const login = async (credentials)=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const response = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.login)(credentials);\n            setUser(response.user);\n        } catch (error) {\n            setError(error.message || \"Login failed\");\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    /**\r\n   * Register new user\r\n   * @param userData - Registration data\r\n   */ const register = async (userData)=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const response = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.register)(userData);\n            setUser(response.user);\n        } catch (error) {\n            setError(error.message || \"Registration failed\");\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    /**\r\n   * Logout current user\r\n   */ const logout = async ()=>{\n        try {\n            setLoading(true);\n            await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.logout)();\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n        } finally{\n            setUser(null);\n            setLoading(false);\n        }\n    };\n    /**\r\n   * Refresh current user data\r\n   */ const refreshUser = async ()=>{\n        try {\n            const userData = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.getCurrentUser)();\n            setUser(userData);\n        } catch (error) {\n            console.error(\"Failed to refresh user data:\", error);\n        }\n    };\n    const value = {\n        user,\n        loading,\n        error,\n        isAuthenticated: !!user,\n        login,\n        register,\n        logout,\n        clearError,\n        refreshUser\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 186,\n        columnNumber: 5\n    }, this);\n}\n/**\r\n * Hook to use authentication context\r\n * @returns Authentication context\r\n * @throws Error if used outside AuthProvider\r\n */ function useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/approvals.ts":
/*!******************************!*\
  !*** ./src/lib/approvals.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createApprovalRequest: () => (/* binding */ createApprovalRequest),\n/* harmony export */   formatRequestData: () => (/* binding */ formatRequestData),\n/* harmony export */   getApprovalById: () => (/* binding */ getApprovalById),\n/* harmony export */   getApprovalStats: () => (/* binding */ getApprovalStats),\n/* harmony export */   getApprovals: () => (/* binding */ getApprovals),\n/* harmony export */   getMyApprovalRequests: () => (/* binding */ getMyApprovalRequests),\n/* harmony export */   getPendingApprovals: () => (/* binding */ getPendingApprovals),\n/* harmony export */   getRequestTypeDisplayName: () => (/* binding */ getRequestTypeDisplayName),\n/* harmony export */   getStatusBadgeColor: () => (/* binding */ getStatusBadgeColor),\n/* harmony export */   processApproval: () => (/* binding */ processApproval)\n/* harmony export */ });\n/* harmony import */ var _auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./auth */ \"(ssr)/./src/lib/auth.ts\");\n/**\n * Approval API service functions\n * Handles all approval-related API calls\n */ \nconst API_BASE_URL = \"http://localhost:5002/api\" || 0;\n/**\n * Make authenticated API request\n */ async function makeRequest(endpoint, options = {}) {\n    const token = (0,_auth__WEBPACK_IMPORTED_MODULE_0__.getAccessToken)();\n    const response = await fetch(`${API_BASE_URL}${endpoint}`, {\n        ...options,\n        headers: {\n            \"Content-Type\": \"application/json\",\n            \"Authorization\": `Bearer ${token}`,\n            ...options.headers\n        }\n    });\n    if (!response.ok) {\n        const errorData = await response.json().catch(()=>({}));\n        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);\n    }\n    return response.json();\n}\n/**\n * Create a new approval request\n */ async function createApprovalRequest(data) {\n    const response = await makeRequest(\"/approvals\", {\n        method: \"POST\",\n        body: JSON.stringify(data)\n    });\n    if (!response.success || !response.data) {\n        throw new Error(response.error || \"Failed to create approval request\");\n    }\n    return response.data;\n}\n/**\n * Get my approval requests\n */ async function getMyApprovalRequests(filters = {}) {\n    const queryParams = new URLSearchParams();\n    Object.entries(filters).forEach(([key, value])=>{\n        if (value !== undefined && value !== null) {\n            queryParams.append(key, value.toString());\n        }\n    });\n    const response = await makeRequest(`/approvals/my-requests?${queryParams.toString()}`);\n    if (!response.success) {\n        throw new Error(\"Failed to fetch approval requests\");\n    }\n    return {\n        requests: response.data,\n        pagination: response.pagination\n    };\n}\n/**\n * Get pending approvals (manager/admin only)\n */ async function getPendingApprovals(filters = {}) {\n    const queryParams = new URLSearchParams();\n    Object.entries(filters).forEach(([key, value])=>{\n        if (value !== undefined && value !== null) {\n            queryParams.append(key, value.toString());\n        }\n    });\n    const response = await makeRequest(`/approvals/pending?${queryParams.toString()}`);\n    if (!response.success) {\n        throw new Error(\"Failed to fetch pending approvals\");\n    }\n    return {\n        requests: response.data,\n        pagination: response.pagination\n    };\n}\n/**\n * Get all approvals with filtering\n */ async function getApprovals(filters = {}) {\n    const queryParams = new URLSearchParams();\n    Object.entries(filters).forEach(([key, value])=>{\n        if (value !== undefined && value !== null) {\n            queryParams.append(key, value.toString());\n        }\n    });\n    const response = await makeRequest(`/approvals?${queryParams.toString()}`);\n    if (!response.success) {\n        throw new Error(\"Failed to fetch approvals\");\n    }\n    return {\n        requests: response.data,\n        pagination: response.pagination\n    };\n}\n/**\n * Get approval by ID\n */ async function getApprovalById(id) {\n    const response = await makeRequest(`/approvals/${id}`);\n    if (!response.success || !response.data) {\n        throw new Error(response.error || \"Failed to fetch approval\");\n    }\n    return response.data;\n}\n/**\n * Process approval (approve/reject) - manager/admin only\n */ async function processApproval(id, data) {\n    const response = await makeRequest(`/approvals/${id}`, {\n        method: \"PUT\",\n        body: JSON.stringify(data)\n    });\n    if (!response.success || !response.data) {\n        throw new Error(response.error || \"Failed to process approval\");\n    }\n    return response.data;\n}\n/**\n * Get approval statistics (admin/manager only)\n */ async function getApprovalStats(filters = {}) {\n    const queryParams = new URLSearchParams();\n    Object.entries(filters).forEach(([key, value])=>{\n        if (value !== undefined && value !== null) {\n            queryParams.append(key, value.toString());\n        }\n    });\n    const response = await makeRequest(`/approvals/stats?${queryParams.toString()}`);\n    if (!response.success || !response.data) {\n        throw new Error(response.error || \"Failed to fetch approval statistics\");\n    }\n    return response.data;\n}\n/**\n * Utility functions\n */ /**\n * Get status badge color for approval status\n */ function getStatusBadgeColor(status) {\n    switch(status){\n        case \"pending\":\n            return \"bg-yellow-100 text-yellow-800\";\n        case \"approved\":\n            return \"bg-green-100 text-green-800\";\n        case \"rejected\":\n            return \"bg-red-100 text-red-800\";\n        default:\n            return \"bg-gray-100 text-gray-800\";\n    }\n}\n/**\n * Get request type display name\n */ function getRequestTypeDisplayName(type) {\n    switch(type){\n        case \"correction\":\n            return \"Time Correction\";\n        case \"overtime\":\n            return \"Overtime Request\";\n        case \"leave\":\n            return \"Leave Request\";\n        default:\n            return type;\n    }\n}\n/**\n * Format approval request data for display\n */ function formatRequestData(requestType, requestData) {\n    try {\n        switch(requestType){\n            case \"correction\":\n                return `Clock In: ${requestData.clockIn || \"N/A\"}, Clock Out: ${requestData.clockOut || \"N/A\"}`;\n            case \"overtime\":\n                return `Hours: ${requestData.hours || \"N/A\"}, Date: ${requestData.date || \"N/A\"}`;\n            case \"leave\":\n                return `From: ${requestData.startDate || \"N/A\"}, To: ${requestData.endDate || \"N/A\"}`;\n            default:\n                return JSON.stringify(requestData);\n        }\n    } catch  {\n        return \"Invalid request data\";\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/approvals.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiRequest: () => (/* binding */ apiRequest),\n/* harmony export */   clearTokens: () => (/* binding */ clearTokens),\n/* harmony export */   decodeToken: () => (/* binding */ decodeToken),\n/* harmony export */   getAccessToken: () => (/* binding */ getAccessToken),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   getRefreshToken: () => (/* binding */ getRefreshToken),\n/* harmony export */   isAuthenticated: () => (/* binding */ isAuthenticated),\n/* harmony export */   isTokenExpired: () => (/* binding */ isTokenExpired),\n/* harmony export */   login: () => (/* binding */ login),\n/* harmony export */   logout: () => (/* binding */ logout),\n/* harmony export */   refreshTokens: () => (/* binding */ refreshTokens),\n/* harmony export */   register: () => (/* binding */ register),\n/* harmony export */   setTokens: () => (/* binding */ setTokens),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/**\r\n * Authentication utilities and API calls\r\n * Handles login, registration, token management, and user session\r\n */ const API_BASE = \"http://localhost:5002/api\" || 0;\n/**\r\n * API response wrapper for error handling\r\n */ class ApiError extends Error {\n    constructor(message, status, code){\n        super(message);\n        this.status = status;\n        this.code = code;\n        this.name = \"ApiError\";\n    }\n}\n/**\r\n * Make authenticated API request with automatic token refresh\r\n * @param url - API endpoint URL\r\n * @param options - Fetch options\r\n * @returns Response data\r\n */ async function apiRequest(url, options = {}) {\n    const token = getAccessToken();\n    const config = {\n        headers: {\n            \"Content-Type\": \"application/json\",\n            ...token && {\n                Authorization: `Bearer ${token}`\n            },\n            ...options.headers\n        },\n        ...options\n    };\n    const response = await fetch(`${API_BASE}${url}`, config);\n    if (!response.ok) {\n        const errorData = await response.json().catch(()=>({}));\n        throw new ApiError(errorData.error?.message || \"Request failed\", response.status, errorData.error?.code);\n    }\n    return response.json();\n}\n/**\r\n * Login user with email and password\r\n * @param credentials - Login credentials\r\n * @returns Authentication response with user data and tokens\r\n */ async function login(credentials) {\n    const response = await apiRequest(\"/auth/login\", {\n        method: \"POST\",\n        body: JSON.stringify(credentials)\n    });\n    // Store tokens\n    setTokens(response.tokens);\n    return response;\n}\n/**\r\n * Register new user account\r\n * @param userData - Registration data\r\n * @returns Authentication response with user data and tokens\r\n */ async function register(userData) {\n    const response = await apiRequest(\"/auth/register\", {\n        method: \"POST\",\n        body: JSON.stringify(userData)\n    });\n    // Store tokens\n    setTokens(response.tokens);\n    return response;\n}\n/**\r\n * Logout user and clear tokens\r\n */ async function logout() {\n    const refreshToken = getRefreshToken();\n    try {\n        await apiRequest(\"/auth/logout\", {\n            method: \"POST\",\n            body: JSON.stringify({\n                refreshToken\n            })\n        });\n    } catch (error) {\n        // Continue with logout even if API call fails\n        console.warn(\"Logout API call failed:\", error);\n    }\n    clearTokens();\n}\n/**\r\n * Get current user profile\r\n * @returns Current user data\r\n */ async function getCurrentUser() {\n    const response = await apiRequest(\"/auth/profile\");\n    return response.user;\n}\n/**\r\n * Verify if current token is valid\r\n * @returns Token validity and user data\r\n */ async function verifyToken() {\n    try {\n        const response = await apiRequest(\"/auth/verify\");\n        return {\n            valid: response.valid,\n            user: response.user\n        };\n    } catch (error) {\n        return {\n            valid: false\n        };\n    }\n}\n/**\r\n * Refresh access token using refresh token\r\n * @returns New authentication tokens\r\n */ async function refreshTokens() {\n    const refreshToken = getRefreshToken();\n    if (!refreshToken) {\n        throw new Error(\"No refresh token available\");\n    }\n    const response = await fetch(`${API_BASE}/auth/refresh`, {\n        method: \"POST\",\n        headers: {\n            \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n            refreshToken\n        })\n    });\n    if (!response.ok) {\n        clearTokens();\n        throw new Error(\"Token refresh failed\");\n    }\n    const data = await response.json();\n    setTokens(data.tokens);\n    return data.tokens;\n}\n/**\r\n * Store authentication tokens in localStorage\r\n * @param tokens - Authentication tokens to store\r\n */ function setTokens(tokens) {\n    if (false) {}\n}\n/**\r\n * Get stored access token\r\n * @returns Access token or null\r\n */ function getAccessToken() {\n    if (false) {}\n    return null;\n}\n/**\r\n * Get stored refresh token\r\n * @returns Refresh token or null\r\n */ function getRefreshToken() {\n    if (false) {}\n    return null;\n}\n/**\r\n * Clear all stored authentication tokens\r\n */ function clearTokens() {\n    if (false) {}\n}\n/**\r\n * Check if user is currently authenticated\r\n * @returns True if user has valid tokens\r\n */ function isAuthenticated() {\n    return !!(getAccessToken() && getRefreshToken());\n}\n/**\r\n * Decode JWT token payload without verification\r\n * @param token - JWT token to decode\r\n * @returns Decoded payload or null\r\n */ function decodeToken(token) {\n    try {\n        const payload = token.split(\".\")[1];\n        return JSON.parse(atob(payload));\n    } catch (error) {\n        return null;\n    }\n}\n/**\r\n * Check if token is expired\r\n * @param token - JWT token to check\r\n * @returns True if token is expired\r\n */ function isTokenExpired(token) {\n    const decoded = decodeToken(token);\n    if (!decoded || !decoded.exp) return true;\n    return Date.now() >= decoded.exp * 1000;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"4036a722cdb7\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZmxleGFpci1mcm9udGVuZC8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/MTc0ZCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjQwMzZhNzIyY2RiN1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/../node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(rsc)/./src/contexts/AuthContext.tsx\");\n/**\r\n * Root layout component for the Flexair Timekeeping App\r\n * Provides global styling, context providers, and common layout structure\r\n */ \n\n\n\nconst metadata = {\n    title: \"Flexair Timekeeping\",\n    description: \"Modern timekeeping application with biometric integration\",\n    keywords: [\n        \"timekeeping\",\n        \"attendance\",\n        \"biometric\",\n        \"HR\",\n        \"workforce management\"\n    ],\n    authors: [\n        {\n            name: \"Flexair Team\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\",\n    robots: \"noindex, nofollow\"\n};\n/**\r\n * Root layout component with authentication context\r\n * @param children - Child components to render\r\n */ function RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className)} h-full bg-gray-50 antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-full\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 34,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 33,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/time/page.tsx":
/*!*******************************!*\
  !*** ./src/app/time/page.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Workspace\flexair_timekeeping_app\frontend\src\app\time\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ e0),\n/* harmony export */   useAuth: () => (/* binding */ e1)\n/* harmony export */ });\n/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ \"(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js\");\n\n\nconst e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\\Users\\<USER>\\Workspace\\flexair_timekeeping_app\\frontend\\src\\contexts\\AuthContext.tsx#AuthProvider`);\n\nconst e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\\Users\\<USER>\\Workspace\\flexair_timekeeping_app\\frontend\\src\\contexts\\AuthContext.tsx#useAuth`);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/contexts/AuthContext.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ftime%2Fpage&page=%2Ftime%2Fpage&appPaths=%2Ftime%2Fpage&pagePath=private-next-app-dir%2Ftime%2Fpage.tsx&appDir=C%3A%5CUsers%5Cijureidini%5CWorkspace%5Cflexair_timekeeping_app%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cijureidini%5CWorkspace%5Cflexair_timekeeping_app%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();