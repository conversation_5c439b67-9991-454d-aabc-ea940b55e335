/**
 * Authentication service
 * Handles JWT tokens, refresh tokens, and password reset functionality
 */

const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const { query: executeQuery } = require('../../shared/database');
const { logger } = require('../../shared/logger');
const { AuthenticationError } = require('../../shared/middleware/errorHandler');

/**
 * Generate access and refresh tokens for user
 * @param {Object} user - User object
 * @param {Object} options - Token generation options
 * @returns {Object} Access and refresh tokens
 */
function generateTokens(user, options = {}) {
  const payload = {
    userId: user.id,
    email: user.email,
    role: user.role
  };

  const accessTokenExpiry = options.expiresIn || process.env.JWT_EXPIRE || '24h';
  const refreshTokenExpiry = process.env.JWT_REFRESH_EXPIRE || '7d';

  const accessToken = jwt.sign(payload, process.env.JWT_SECRET, {
    expiresIn: accessTokenExpiry
  });

  const refreshToken = jwt.sign(
    { userId: user.id, type: 'refresh' }, 
    process.env.JWT_SECRET, 
    { expiresIn: refreshTokenExpiry }
  );

  return {
    accessToken,
    refreshToken,
    expiresIn: accessTokenExpiry
  };
}

/**
 * Verify JWT token
 * @param {string} token - JWT token to verify
 * @returns {Object} Decoded token payload
 */
function verifyToken(token) {
  try {
    return jwt.verify(token, process.env.JWT_SECRET);
  } catch (error) {
    throw new AuthenticationError('Invalid token');
  }
}

/**
 * Verify refresh token
 * @param {string} refreshToken - Refresh token to verify
 * @returns {Object} Decoded token payload
 */
async function verifyRefreshToken(refreshToken) {
  try {
    const payload = jwt.verify(refreshToken, process.env.JWT_SECRET);
    
    if (payload.type !== 'refresh') {
      throw new AuthenticationError('Invalid token type');
    }

    // Check if token exists in database and is not expired
    const query = `
      SELECT * FROM refresh_tokens 
      WHERE user_id = ? AND token = ? AND expires_at > NOW() AND is_revoked = false
    `;
    const tokens = await executeQuery(query, [payload.userId, refreshToken]);
    
    if (tokens.length === 0) {
      throw new AuthenticationError('Invalid refresh token');
    }

    return payload;
  } catch (error) {
    throw new AuthenticationError('Invalid refresh token');
  }
}

/**
 * Store refresh token in database
 * @param {number} userId - User ID
 * @param {string} refreshToken - Refresh token
 * @returns {Promise<void>}
 */
async function storeRefreshToken(userId, refreshToken) {
  const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days
  
  const query = `
    INSERT INTO refresh_tokens (user_id, token, expires_at, created_at)
    VALUES (?, ?, ?, NOW())
  `;
  
  await executeQuery(query, [userId, refreshToken, expiresAt]);
}

/**
 * Invalidate refresh token
 * @param {string} refreshToken - Refresh token to invalidate
 * @returns {Promise<void>}
 */
async function invalidateRefreshToken(refreshToken) {
  const query = `
    UPDATE refresh_tokens 
    SET is_revoked = true, revoked_at = NOW()
    WHERE token = ?
  `;
  
  await executeQuery(query, [refreshToken]);
}

/**
 * Invalidate all refresh tokens for a user
 * @param {number} userId - User ID
 * @returns {Promise<void>}
 */
async function invalidateAllUserTokens(userId) {
  const query = `
    UPDATE refresh_tokens 
    SET is_revoked = true, revoked_at = NOW()
    WHERE user_id = ? AND is_revoked = false
  `;
  
  await executeQuery(query, [userId]);
}

/**
 * Clean up expired refresh tokens
 * @returns {Promise<void>}
 */
async function cleanupExpiredTokens() {
  const query = `
    DELETE FROM refresh_tokens 
    WHERE expires_at < NOW() OR is_revoked = true
  `;
  
  const result = await executeQuery(query);
  
  if (result.affectedRows > 0) {
    logger.info(`Cleaned up ${result.affectedRows} expired/revoked tokens`);
  }
}

/**
 * Add token to blacklist
 * @param {string} token - Token to blacklist
 * @returns {Promise<void>}
 */
async function blacklistToken(token) {
  try {
    const payload = jwt.decode(token);
    if (!payload) return;
    
    const expiresAt = new Date(payload.exp * 1000);
    
    const query = `
      INSERT INTO token_blacklist (token, expires_at, created_at)
      VALUES (?, ?, NOW())
      ON DUPLICATE KEY UPDATE created_at = NOW()
    `;
    
    await executeQuery(query, [token, expiresAt]);
  } catch (error) {
    logger.error('Failed to blacklist token:', error);
  }
}

/**
 * Check if token is blacklisted
 * @param {string} token - Token to check
 * @returns {Promise<boolean>} True if blacklisted
 */
async function isTokenBlacklisted(token) {
  const query = `
    SELECT 1 FROM token_blacklist 
    WHERE token = ? AND expires_at > NOW()
  `;
  
  const result = await executeQuery(query, [token]);
  return result.length > 0;
}

/**
 * Store password reset token
 * @param {number} userId - User ID
 * @param {string} resetToken - Reset token
 * @param {Date} expiresAt - Token expiry date
 * @returns {Promise<void>}
 */
async function storePasswordResetToken(userId, resetToken, expiresAt) {
  // Hash the token for storage
  const hashedToken = crypto.createHash('sha256').update(resetToken).digest('hex');
  
  const query = `
    INSERT INTO password_reset_tokens (user_id, token, expires_at, created_at)
    VALUES (?, ?, ?, NOW())
    ON DUPLICATE KEY UPDATE 
      token = VALUES(token),
      expires_at = VALUES(expires_at),
      created_at = VALUES(created_at),
      used_at = NULL
  `;
  
  await executeQuery(query, [userId, hashedToken, expiresAt]);
}

/**
 * Verify password reset token
 * @param {string} resetToken - Reset token to verify
 * @returns {Promise<Object|null>} User object if valid, null if invalid
 */
async function verifyPasswordResetToken(resetToken) {
  const hashedToken = crypto.createHash('sha256').update(resetToken).digest('hex');
  
  const query = `
    SELECT prt.*, u.id, u.email, u.first_name, u.last_name
    FROM password_reset_tokens prt
    JOIN users u ON prt.user_id = u.id
    WHERE prt.token = ? AND prt.expires_at > NOW() AND prt.used_at IS NULL
  `;
  
  const results = await executeQuery(query, [hashedToken]);
  
  if (results.length === 0) {
    return null;
  }
  
  return results[0];
}

/**
 * Clear password reset token after use
 * @param {number} userId - User ID
 * @returns {Promise<void>}
 */
async function clearPasswordResetToken(userId) {
  const query = `
    UPDATE password_reset_tokens 
    SET used_at = NOW()
    WHERE user_id = ? AND used_at IS NULL
  `;
  
  await executeQuery(query, [userId]);
}

/**
 * Clean up expired password reset tokens
 * @returns {Promise<void>}
 */
async function cleanupExpiredPasswordResetTokens() {
  const query = `
    DELETE FROM password_reset_tokens 
    WHERE expires_at < NOW() OR used_at IS NOT NULL
  `;
  
  const result = await executeQuery(query);
  
  if (result.affectedRows > 0) {
    logger.info(`Cleaned up ${result.affectedRows} expired/used password reset tokens`);
  }
}

module.exports = {
  generateTokens,
  verifyToken,
  verifyRefreshToken,
  storeRefreshToken,
  invalidateRefreshToken,
  invalidateAllUserTokens,
  cleanupExpiredTokens,
  blacklistToken,
  isTokenBlacklisted,
  storePasswordResetToken,
  verifyPasswordResetToken,
  clearPasswordResetToken,
  cleanupExpiredPasswordResetTokens
};