/**
 * Custom hook for approval notifications
 * Provides real-time notification data for pending approvals
 */

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { getApprovalStats, getPendingApprovals } from '@/lib/approvals';

/**
 * Notification data interface
 */
export interface ApprovalNotifications {
  pendingCount: number;
  myPendingRequests: number;
  isLoading: boolean;
  error: string | null;
  refresh: () => Promise<void>;
}

/**
 * Hook for managing approval notifications
 */
export function useApprovalNotifications(): ApprovalNotifications {
  const { user } = useAuth();
  const [pendingCount, setPendingCount] = useState(0);
  const [myPendingRequests, setMyPendingRequests] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * Load notification data
   */
  const loadNotifications = async () => {
    if (!user) return;

    setIsLoading(true);
    setError(null);

    try {
      // Load pending approvals count for managers/admins
      if (user.role === 'admin' || user.role === 'manager') {
        try {
          const stats = await getApprovalStats();
          setPendingCount(stats.pendingRequests);
        } catch (err) {
          console.warn('Failed to load approval stats:', err);
          setPendingCount(0);
        }
      }

      // Load my pending requests count for all users
      try {
        const myRequests = await getPendingApprovals({ 
          status: 'pending',
          limit: 1 // We only need the count
        });
        setMyPendingRequests(myRequests.pagination?.total || 0);
      } catch (err) {
        console.warn('Failed to load my pending requests:', err);
        setMyPendingRequests(0);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load notifications');
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Refresh notifications
   */
  const refresh = async () => {
    await loadNotifications();
  };

  /**
   * Load notifications on mount and user change
   */
  useEffect(() => {
    loadNotifications();
  }, [user]);

  /**
   * Auto-refresh notifications every 30 seconds
   */
  useEffect(() => {
    const interval = setInterval(() => {
      loadNotifications();
    }, 30000); // 30 seconds

    return () => clearInterval(interval);
  }, [user]);

  return {
    pendingCount,
    myPendingRequests,
    isLoading,
    error,
    refresh
  };
}




