/**
 * Approval notification summary component
 */

import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { ApprovalNotifications } from '@/hooks/useApprovalNotifications';
import NotificationBadge from './NotificationBadge';

export interface ApprovalNotificationSummaryProps {
  notifications: ApprovalNotifications;
  showDetails?: boolean;
}

export default function ApprovalNotificationSummary({ 
  notifications, 
  showDetails = false 
}: ApprovalNotificationSummaryProps) {
  const { user } = useAuth();
  const { pendingCount, myPendingRequests, isLoading } = notifications;

  if (isLoading) {
    return (
      <div className="text-sm text-gray-500">
        Loading notifications...
      </div>
    );
  }

  const hasNotifications = pendingCount > 0 || myPendingRequests > 0;

  if (!hasNotifications) {
    return showDetails ? (
      <div className="text-sm text-gray-500">
        No pending approvals
      </div>
    ) : null;
  }

  return (
    <div className="space-y-1">
      {(user?.role === 'admin' || user?.role === 'manager') && pendingCount > 0 && (
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-600">Pending approvals to review:</span>
          <NotificationBadge count={pendingCount} />
        </div>
      )}
      
      {myPendingRequests > 0 && (
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-600">My pending requests:</span>
          <NotificationBadge count={myPendingRequests} className="bg-blue-600" />
        </div>
      )}
    </div>
  );
}
