-- Setup Test Environment for Flexair Timekeeping Approval System
-- Run this script to create test users and sample data for manual testing

-- Create test users (if they don't exist)
INSERT IGNORE INTO users (email, password_hash, first_name, last_name, role, is_active, created_at, updated_at) VALUES
('<EMAIL>', '$2b$10$rQZ9QZ9QZ9QZ9QZ9QZ9QZ9QZ9QZ9QZ9QZ9QZ9QZ9QZ9QZ9QZ9QZ9Q', 'Manager', 'Smith', 'manager', 1, NOW(), NOW()),
('<EMAIL>', '$2b$10$rQZ9QZ9QZ9QZ9QZ9QZ9QZ9QZ9QZ9QZ9QZ9QZ9QZ9QZ9QZ9QZ9QZ9Q', 'John', 'Doe', 'employee', 1, NOW(), NOW()),
('<EMAIL>', '$2b$10$rQZ9QZ9QZ9QZ9QZ9QZ9QZ9QZ9QZ9QZ9QZ9QZ9QZ9QZ9QZ9QZ9QZ9Q', 'Admin', 'User', 'admin', 1, NOW(), NOW()),
('<EMAIL>', '$2b$10$rQZ9QZ9QZ9QZ9QZ9QZ9QZ9QZ9QZ9QZ9QZ9QZ9QZ9QZ9QZ9QZ9QZ9Q', 'Jane', 'Smith', 'employee', 1, NOW(), NOW());

-- Note: The password hash above corresponds to 'password123'
-- In a real environment, you would generate proper bcrypt hashes

-- Get user IDs for reference
SET @employee_id = (SELECT id FROM users WHERE email = '<EMAIL>');
SET @employee2_id = (SELECT id FROM users WHERE email = '<EMAIL>');
SET @manager_id = (SELECT id FROM users WHERE email = '<EMAIL>');
SET @admin_id = (SELECT id FROM users WHERE email = '<EMAIL>');

-- Create some sample time logs for testing
INSERT IGNORE INTO time_logs (user_id, clock_in, clock_out, total_hours, created_at, updated_at) VALUES
(@employee_id, '2024-01-15 09:00:00', '2024-01-15 17:00:00', 8.00, '2024-01-15 17:00:00', '2024-01-15 17:00:00'),
(@employee_id, '2024-01-16 08:30:00', '2024-01-16 16:45:00', 8.25, '2024-01-16 16:45:00', '2024-01-16 16:45:00'),
(@employee_id, '2024-01-17 09:15:00', '2024-01-17 18:30:00', 9.25, '2024-01-17 18:30:00', '2024-01-17 18:30:00'),
(@employee2_id, '2024-01-15 08:00:00', '2024-01-15 16:00:00', 8.00, '2024-01-15 16:00:00', '2024-01-15 16:00:00'),
(@employee2_id, '2024-01-16 09:00:00', '2024-01-16 17:30:00', 8.50, '2024-01-16 17:30:00', '2024-01-16 17:30:00');

-- Create some sample approval requests for testing
INSERT IGNORE INTO approval_requests (
    time_log_id, 
    requested_by, 
    assigned_to, 
    request_type, 
    request_data, 
    reason, 
    status, 
    created_at, 
    updated_at
) VALUES
-- Pending correction request
((SELECT id FROM time_logs WHERE user_id = @employee_id LIMIT 1), 
 @employee_id, 
 @manager_id, 
 'correction', 
 '{"clockIn": "2024-01-15T08:45:00", "clockOut": "2024-01-15T17:15:00"}', 
 'Forgot to clock in on time, was actually here at 8:45 AM', 
 'pending', 
 '2024-01-15 18:00:00', 
 '2024-01-15 18:00:00'),

-- Pending overtime request
((SELECT id FROM time_logs WHERE user_id = @employee_id ORDER BY id DESC LIMIT 1), 
 @employee_id, 
 @manager_id, 
 'overtime', 
 '{"hours": 2.5, "date": "2024-01-17"}', 
 'Project deadline required extra hours to complete deliverables', 
 'pending', 
 '2024-01-17 19:00:00', 
 '2024-01-17 19:00:00'),

-- Approved leave request
(NULL, 
 @employee2_id, 
 @manager_id, 
 'leave', 
 '{"startDate": "2024-01-20", "endDate": "2024-01-22", "leaveType": "vacation"}', 
 'Family vacation - pre-planned', 
 'approved', 
 '2024-01-10 10:00:00', 
 '2024-01-12 14:30:00'),

-- Rejected overtime request
((SELECT id FROM time_logs WHERE user_id = @employee2_id LIMIT 1), 
 @employee2_id, 
 @manager_id, 
 'overtime', 
 '{"hours": 4, "date": "2024-01-16"}', 
 'Stayed late to finish project tasks', 
 'rejected', 
 '2024-01-16 20:00:00', 
 '2024-01-17 09:00:00');

-- Update the approved and rejected requests with processing details
UPDATE approval_requests 
SET approved_at = '2024-01-12 14:30:00', 
    comments = 'Approved - vacation request was pre-approved by supervisor'
WHERE status = 'approved';

UPDATE approval_requests 
SET approved_at = '2024-01-17 09:00:00', 
    comments = 'Overtime was not pre-approved. Please get supervisor approval before working extra hours.'
WHERE status = 'rejected';

-- Display summary of created test data
SELECT 'Test Users Created:' as summary;
SELECT email, first_name, last_name, role FROM users WHERE email LIKE '%flexair.com';

SELECT 'Time Logs Created:' as summary;
SELECT tl.id, u.first_name, u.last_name, tl.clock_in, tl.clock_out, tl.total_hours 
FROM time_logs tl 
JOIN users u ON tl.user_id = u.id 
WHERE u.email LIKE '%flexair.com'
ORDER BY tl.created_at DESC;

SELECT 'Approval Requests Created:' as summary;
SELECT ar.id, 
       requester.first_name as requester_name, 
       manager.first_name as manager_name,
       ar.request_type, 
       ar.status, 
       ar.reason,
       ar.created_at
FROM approval_requests ar
JOIN users requester ON ar.requested_by = requester.id
JOIN users manager ON ar.assigned_to = manager.id
ORDER BY ar.created_at DESC;

-- Instructions for manual testing
SELECT 'MANUAL TESTING INSTRUCTIONS:' as instructions;
SELECT '1. <NAME_EMAIL> (password: password123)' as step;
SELECT '2. Go to Time Tracking page to see existing time logs' as step;
SELECT '3. Click "Request Approval" to create new approval requests' as step;
SELECT '4. <NAME_EMAIL> (password: password123)' as step;
SELECT '5. Go to Approvals page to see pending requests' as step;
SELECT '6. Test approving and rejecting requests' as step;
SELECT '7. Check notification badges on dashboard' as step;
