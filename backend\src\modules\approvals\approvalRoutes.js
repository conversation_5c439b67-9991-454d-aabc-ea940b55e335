/**
 * Approval workflow routes
 */

const express = require('express');
const router = express.Router();
const approvalController = require('./approvalController');
const { authenticateToken, requireRole } = require('../../shared/middleware/authMiddleware');
const { approvalValidation } = require('../../shared/middleware/validation');

// All routes require authentication
router.use(authenticateToken);

// Create approval request
router.post('/', 
  approvalValidation.request,
  approvalController.createApprovalRequest
);

// Get my approval requests
router.get('/my-requests', 
  approvalController.getMyApprovalRequests
);

// Get pending approvals (manager/admin only)
router.get('/pending', 
  requireRole(['admin', 'manager']),
  approvalController.getPendingApprovals
);

// Get approval statistics (admin/manager only)
router.get('/stats', 
  requireRole(['admin', 'manager']),
  approvalController.getApprovalStats
);

// Get all approvals with filtering
router.get('/', 
  approvalController.getApprovals
);

// Get approval by ID
router.get('/:id', 
  approvalController.getApprovalById
);

// Process approval (approve/reject) - manager/admin only
router.put('/:id', 
  requireRole(['admin', 'manager']),
  approvalValidation.approve,
  approvalController.processApproval
);

module.exports = router;