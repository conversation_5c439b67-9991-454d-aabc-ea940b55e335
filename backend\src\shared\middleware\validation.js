/**
 * Request validation middleware and schemas
 */

const { body, param, query, validationResult } = require('express-validator');
const { logger } = require('../logger');

/**
 * Handle validation errors
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    logger.warn('Validation errors:', { 
      path: req.path,
      method: req.method,
      errors: errors.array()
    });
    
    return res.status(400).json({
      error: 'Validation failed',
      code: 'VALIDATION_ERROR',
      details: errors.array().map(err => ({
        field: err.path,
        message: err.msg,
        value: err.value
      }))
    });
  }
  
  next();
};

// Authentication validation schemas
const authValidation = {
  login: [
    body('email')
      .isEmail()
      .normalizeEmail()
      .withMessage('Valid email is required'),
    body('password')
      .isLength({ min: 8 })
      .withMessage('Password must be at least 8 characters'),
    handleValidationErrors
  ],
  
  register: [
    body('email')
      .isEmail()
      .normalizeEmail()
      .withMessage('Valid email is required'),
    body('password')
      .isLength({ min: 8 })
      .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
      .withMessage('Password must contain at least 8 characters with uppercase, lowercase, number and special character'),
    body('firstName')
      .trim()
      .isLength({ min: 2, max: 50 })
      .withMessage('First name must be between 2-50 characters'),
    body('lastName')
      .trim()
      .isLength({ min: 2, max: 50 })
      .withMessage('Last name must be between 2-50 characters'),
    body('role')
      .optional()
      .isIn(['admin', 'manager', 'employee'])
      .withMessage('Role must be admin, manager, or employee'),
    handleValidationErrors
  ],
  
  forgotPassword: [
    body('email')
      .isEmail()
      .normalizeEmail()
      .withMessage('Valid email is required'),
    handleValidationErrors
  ],
  
  resetPassword: [
    body('token')
      .isLength({ min: 1 })
      .withMessage('Reset token is required'),
    body('password')
      .isLength({ min: 8 })
      .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
      .withMessage('Password must contain at least 8 characters with uppercase, lowercase, number and special character'),
    handleValidationErrors
  ]
};

// User validation schemas
const userValidation = {
  create: [
    body('email')
      .isEmail()
      .normalizeEmail()
      .withMessage('Valid email is required'),
    body('firstName')
      .trim()
      .isLength({ min: 2, max: 50 })
      .withMessage('First name must be between 2-50 characters'),
    body('lastName')
      .trim()
      .isLength({ min: 2, max: 50 })
      .withMessage('Last name must be between 2-50 characters'),
    body('role')
      .isIn(['admin', 'manager', 'employee'])
      .withMessage('Role must be admin, manager, or employee'),
    body('password')
      .optional()
      .isLength({ min: 8 })
      .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
      .withMessage('Password must contain at least 8 characters with uppercase, lowercase, number and special character'),
    handleValidationErrors
  ],
  
  update: [
    param('id')
      .isInt({ min: 1 })
      .withMessage('Valid user ID is required'),
    body('email')
      .optional()
      .isEmail()
      .normalizeEmail()
      .withMessage('Valid email is required'),
    body('firstName')
      .optional()
      .trim()
      .isLength({ min: 2, max: 50 })
      .withMessage('First name must be between 2-50 characters'),
    body('lastName')
      .optional()
      .trim()
      .isLength({ min: 2, max: 50 })
      .withMessage('Last name must be between 2-50 characters'),
    body('role')
      .optional()
      .isIn(['admin', 'manager', 'employee'])
      .withMessage('Role must be admin, manager, or employee'),
    body('isActive')
      .optional()
      .isBoolean()
      .withMessage('isActive must be a boolean'),
    handleValidationErrors
  ],
  
  getId: [
    param('id')
      .isInt({ min: 1 })
      .withMessage('Valid user ID is required'),
    handleValidationErrors
  ]
};

// Time tracking validation schemas
const timeValidation = {
  clockIn: [
    body('location')
      .optional()
      .isLength({ max: 255 })
      .withMessage('Location must be less than 255 characters'),
    body('biometricData')
      .optional()
      .isLength({ max: 5000 })
      .withMessage('Biometric data too large'),
    handleValidationErrors
  ],
  
  clockOut: [
    body('location')
      .optional()
      .isLength({ max: 255 })
      .withMessage('Location must be less than 255 characters'),
    body('biometricData')
      .optional()
      .isLength({ max: 5000 })
      .withMessage('Biometric data too large'),
    body('notes')
      .optional()
      .isLength({ max: 1000 })
      .withMessage('Notes must be less than 1000 characters'),
    handleValidationErrors
  ],
  
  getTimeLogs: [
    query('startDate')
      .optional()
      .isISO8601()
      .withMessage('Start date must be a valid ISO date'),
    query('endDate')
      .optional()
      .isISO8601()
      .withMessage('End date must be a valid ISO date'),
    query('userId')
      .optional()
      .isInt({ min: 1 })
      .withMessage('User ID must be a positive integer'),
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Page must be a positive integer'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit must be between 1-100'),
    handleValidationErrors
  ]
};

// Approval validation schemas
const approvalValidation = {
  approve: [
    param('id')
      .isInt({ min: 1 })
      .withMessage('Valid approval ID is required'),
    body('status')
      .isIn(['approved', 'rejected'])
      .withMessage('Status must be approved or rejected'),
    body('comments')
      .optional()
      .isLength({ max: 1000 })
      .withMessage('Comments must be less than 1000 characters'),
    handleValidationErrors
  ],
  
  request: [
    body('timeLogId')
      .isInt({ min: 1 })
      .withMessage('Valid time log ID is required'),
    body('requestType')
      .isIn(['correction', 'approval'])
      .withMessage('Request type must be correction or approval'),
    body('requestData')
      .isObject()
      .withMessage('Request data must be an object'),
    body('reason')
      .isLength({ min: 10, max: 1000 })
      .withMessage('Reason must be between 10-1000 characters'),
    handleValidationErrors
  ]
};

// Biometric validation schemas
const biometricValidation = {
  enroll: [
    body('templateData')
      .isLength({ min: 100 })
      .withMessage('Template data is required'),
    body('templateType')
      .isIn(['fingerprint', 'face'])
      .withMessage('Template type must be fingerprint or face'),
    handleValidationErrors
  ],
  
  verify: [
    body('templateData')
      .isLength({ min: 100 })
      .withMessage('Template data is required'),
    body('templateType')
      .isIn(['fingerprint', 'face'])
      .withMessage('Template type must be fingerprint or face'),
    handleValidationErrors
  ]
};

module.exports = {
  handleValidationErrors,
  authValidation,
  userValidation,
  timeValidation,
  approvalValidation,
  biometricValidation
};