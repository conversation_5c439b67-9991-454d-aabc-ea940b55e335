/**
 * Authentication Context Provider
 * Manages global authentication state and provides auth methods
 */

'use client';

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { 
  User, 
  AuthTokens, 
  LoginCredentials, 
  RegisterData,
  login as apiLogin,
  register as apiRegister,
  logout as apiLogout,
  getCurrentUser,
  verifyToken,
  refreshTokens,
  isAuthenticated,
  clearTokens,
  getAccessToken,
  isTokenExpired
} from '@/lib/auth';

interface AuthContextType {
  user: User | null;
  loading: boolean;
  error: string | null;
  isAuthenticated: boolean;
  login: (credentials: LoginCredentials) => Promise<void>;
  register: (userData: RegisterData) => Promise<void>;
  logout: () => Promise<void>;
  clearError: () => void;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

/**
 * Authentication Provider Component
 * Wraps the app and provides authentication state and methods
 * @param children - Child components to render
 */
export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  /**
   * Clear any authentication errors
   */
  const clearError = () => setError(null);

  /**
   * Initialize authentication state on mount
   */
  useEffect(() => {
    initializeAuth();
  }, []);

  /**
   * Initialize authentication state
   * Checks for existing tokens and validates them
   */
  const initializeAuth = async () => {
    try {
      setLoading(true);
      setError(null);

      if (!isAuthenticated()) {
        setLoading(false);
        return;
      }

      const token = getAccessToken();
      if (!token || isTokenExpired(token)) {
        // Try to refresh token
        try {
          await refreshTokens();
        } catch (error) {
          clearTokens();
          setLoading(false);
          return;
        }
      }

      // Verify token and get user data
      const { valid, user: userData } = await verifyToken();
      
      if (valid && userData) {
        setUser(userData);
      } else {
        clearTokens();
      }
    } catch (error) {
      console.error('Auth initialization error:', error);
      clearTokens();
    } finally {
      setLoading(false);
    }
  };

  /**
   * Login user with credentials
   * @param credentials - Login credentials
   */
  const login = async (credentials: LoginCredentials) => {
    try {
      setLoading(true);
      setError(null);

      const response = await apiLogin(credentials);
      setUser(response.user);
    } catch (error: any) {
      setError(error.message || 'Login failed');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  /**
   * Register new user
   * @param userData - Registration data
   */
  const register = async (userData: RegisterData) => {
    try {
      setLoading(true);
      setError(null);

      const response = await apiRegister(userData);
      setUser(response.user);
    } catch (error: any) {
      setError(error.message || 'Registration failed');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  /**
   * Logout current user
   */
  const logout = async () => {
    try {
      setLoading(true);
      await apiLogout();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setUser(null);
      setLoading(false);
    }
  };

  /**
   * Refresh current user data
   */
  const refreshUser = async () => {
    try {
      const userData = await getCurrentUser();
      setUser(userData);
    } catch (error) {
      console.error('Failed to refresh user data:', error);
    }
  };

  const value: AuthContextType = {
    user,
    loading,
    error,
    isAuthenticated: !!user,
    login,
    register,
    logout,
    clearError,
    refreshUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

/**
 * Hook to use authentication context
 * @returns Authentication context
 * @throws Error if used outside AuthProvider
 */
export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  
  return context;
}