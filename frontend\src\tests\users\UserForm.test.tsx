/**
 * UserForm Component Tests
 * Tests user form functionality, validation, and submission
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import UserForm from '@/components/users/UserForm';
import { useAuth } from '@/contexts/AuthContext';
import * as userService from '@/lib/users';

// Mock auth context
jest.mock('@/contexts/AuthContext', () => ({
  useAuth: jest.fn(),
}));

// Mock user service
jest.mock('@/lib/users', () => ({
  createUser: jest.fn(),
  updateUser: jest.fn(),
}));

const mockUser = {
  id: 1,
  email: '<EMAIL>',
  firstName: 'John',
  lastName: 'Doe',
  role: 'employee' as const,
  isActive: true,
  emailVerified: true,
  biometricEnabled: false,
  createdAt: '2024-01-01T00:00:00Z',
  department: 'Engineering',
  employeeId: 'EMP001',
  phone: '+1234567890',
};

const defaultAuthContextValue = {
  user: {
    id: 2,
    email: '<EMAIL>',
    firstName: 'Admin',
    lastName: 'User',
    role: 'admin' as const,
    isActive: true,
  },
  loading: false,
  error: null,
  isAuthenticated: true,
  login: jest.fn(),
  register: jest.fn(),
  logout: jest.fn(),
  clearError: jest.fn(),
  refreshUser: jest.fn(),
};

describe('UserForm', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (useAuth as jest.Mock).mockReturnValue(defaultAuthContextValue);
  });

  /**
   * Test: Create mode renders correctly
   */
  it('renders create form with all required fields', () => {
    render(<UserForm mode="create" />);
    
    expect(screen.getByText('Create New User')).toBeInTheDocument();
    expect(screen.getByLabelText(/first name/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/last name/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/email address/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/password/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/confirm password/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/role/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /create user/i })).toBeInTheDocument();
  });

  /**
   * Test: Edit mode renders correctly with user data
   */
  it('renders edit form with user data pre-filled', () => {
    render(<UserForm mode="edit" user={mockUser} />);
    
    expect(screen.getByText('Edit User')).toBeInTheDocument();
    expect(screen.getByDisplayValue('John')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Doe')).toBeInTheDocument();
    expect(screen.getByDisplayValue('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Engineering')).toBeInTheDocument();
    expect(screen.getByDisplayValue('EMP001')).toBeInTheDocument();
    expect(screen.getByDisplayValue('+1234567890')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /update user/i })).toBeInTheDocument();
  });

  /**
   * Test: Form validation works for required fields
   */
  it('validates required fields', async () => {
    const user = userEvent.setup();
    render(<UserForm mode="create" />);
    
    const submitButton = screen.getByRole('button', { name: /create user/i });
    await user.click(submitButton);
    
    expect(screen.getByText(/first name is required/i)).toBeInTheDocument();
    expect(screen.getByText(/last name is required/i)).toBeInTheDocument();
    expect(screen.getByText(/email is required/i)).toBeInTheDocument();
    expect(screen.getByText(/password is required/i)).toBeInTheDocument();
  });

  /**
   * Test: Email validation works
   */
  it('validates email format', async () => {
    const user = userEvent.setup();
    render(<UserForm mode="create" />);
    
    const emailField = screen.getByLabelText(/email address/i);
    await user.type(emailField, 'invalid-email');
    await user.tab(); // Trigger blur event
    
    expect(screen.getByText(/please enter a valid email address/i)).toBeInTheDocument();
  });

  /**
   * Test: Password validation works
   */
  it('validates password requirements', async () => {
    const user = userEvent.setup();
    render(<UserForm mode="create" />);
    
    const passwordField = screen.getByLabelText(/^password/i);
    await user.type(passwordField, 'weak');
    await user.tab();
    
    expect(screen.getByText(/password must be at least 8 characters long/i)).toBeInTheDocument();
    
    await user.clear(passwordField);
    await user.type(passwordField, 'weakpassword');
    await user.tab();
    
    expect(screen.getByText(/password must contain at least one uppercase letter/i)).toBeInTheDocument();
  });

  /**
   * Test: Password confirmation validation works
   */
  it('validates password confirmation', async () => {
    const user = userEvent.setup();
    render(<UserForm mode="create" />);
    
    const passwordField = screen.getByLabelText(/^password/i);
    const confirmPasswordField = screen.getByLabelText(/confirm password/i);
    
    await user.type(passwordField, 'ValidPassword123');
    await user.type(confirmPasswordField, 'DifferentPassword123');
    await user.tab();
    
    expect(screen.getByText(/passwords do not match/i)).toBeInTheDocument();
  });

  /**
   * Test: Successful user creation
   */
  it('creates user successfully with valid data', async () => {
    const user = userEvent.setup();
    const mockOnSuccess = jest.fn();
    (userService.createUser as jest.Mock).mockResolvedValue(mockUser);
    
    render(<UserForm mode="create" onSuccess={mockOnSuccess} />);
    
    // Fill form with valid data
    await user.type(screen.getByLabelText(/first name/i), 'John');
    await user.type(screen.getByLabelText(/last name/i), 'Doe');
    await user.type(screen.getByLabelText(/email address/i), '<EMAIL>');
    await user.type(screen.getByLabelText(/^password/i), 'ValidPassword123');
    await user.type(screen.getByLabelText(/confirm password/i), 'ValidPassword123');
    
    const submitButton = screen.getByRole('button', { name: /create user/i });
    await user.click(submitButton);
    
    await waitFor(() => {
      expect(userService.createUser).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'ValidPassword123',
        firstName: 'John',
        lastName: 'Doe',
        role: 'employee',
        department: undefined,
        employeeId: undefined,
        phone: undefined,
      });
      expect(mockOnSuccess).toHaveBeenCalledWith(mockUser);
    });
  });

  /**
   * Test: Successful user update
   */
  it('updates user successfully with valid data', async () => {
    const user = userEvent.setup();
    const mockOnSuccess = jest.fn();
    const updatedUser = { ...mockUser, firstName: 'Jane' };
    (userService.updateUser as jest.Mock).mockResolvedValue(updatedUser);
    
    render(<UserForm mode="edit" user={mockUser} onSuccess={mockOnSuccess} />);
    
    // Update first name
    const firstNameField = screen.getByLabelText(/first name/i);
    await user.clear(firstNameField);
    await user.type(firstNameField, 'Jane');
    
    const submitButton = screen.getByRole('button', { name: /update user/i });
    await user.click(submitButton);
    
    await waitFor(() => {
      expect(userService.updateUser).toHaveBeenCalledWith(1, {
        email: '<EMAIL>',
        firstName: 'Jane',
        lastName: 'Doe',
        role: 'employee',
        department: 'Engineering',
        employeeId: 'EMP001',
        phone: '+1234567890',
        isActive: true,
      });
      expect(mockOnSuccess).toHaveBeenCalledWith(updatedUser);
    });
  });

  /**
   * Test: Loading state is displayed during submission
   */
  it('shows loading state during form submission', async () => {
    const user = userEvent.setup();
    (userService.createUser as jest.Mock).mockImplementation(() => new Promise(() => {})); // Never resolves
    
    render(<UserForm mode="create" />);
    
    // Fill required fields
    await user.type(screen.getByLabelText(/first name/i), 'John');
    await user.type(screen.getByLabelText(/last name/i), 'Doe');
    await user.type(screen.getByLabelText(/email address/i), '<EMAIL>');
    await user.type(screen.getByLabelText(/^password/i), 'ValidPassword123');
    await user.type(screen.getByLabelText(/confirm password/i), 'ValidPassword123');
    
    const submitButton = screen.getByRole('button', { name: /create user/i });
    await user.click(submitButton);
    
    expect(screen.getByText(/creating.../i)).toBeInTheDocument();
    expect(submitButton).toBeDisabled();
  });

  /**
   * Test: Error handling for duplicate email
   */
  it('handles duplicate email error', async () => {
    const user = userEvent.setup();
    const error = new Error('Email already exists');
    error.message = 'EMAIL_EXISTS';
    (userService.createUser as jest.Mock).mockRejectedValue(error);
    
    render(<UserForm mode="create" />);
    
    // Fill form with valid data
    await user.type(screen.getByLabelText(/first name/i), 'John');
    await user.type(screen.getByLabelText(/last name/i), 'Doe');
    await user.type(screen.getByLabelText(/email address/i), '<EMAIL>');
    await user.type(screen.getByLabelText(/^password/i), 'ValidPassword123');
    await user.type(screen.getByLabelText(/confirm password/i), 'ValidPassword123');
    
    const submitButton = screen.getByRole('button', { name: /create user/i });
    await user.click(submitButton);
    
    await waitFor(() => {
      expect(screen.getByText(/email address is already in use/i)).toBeInTheDocument();
    });
  });

  /**
   * Test: Cancel button works
   */
  it('calls onCancel when cancel button is clicked', async () => {
    const user = userEvent.setup();
    const mockOnCancel = jest.fn();
    
    render(<UserForm mode="create" onCancel={mockOnCancel} />);
    
    const cancelButton = screen.getByRole('button', { name: /cancel/i });
    await user.click(cancelButton);
    
    expect(mockOnCancel).toHaveBeenCalled();
  });

  /**
   * Test: Role selection is disabled for non-admin users
   */
  it('disables role selection for non-admin users', () => {
    (useAuth as jest.Mock).mockReturnValue({
      ...defaultAuthContextValue,
      user: { ...defaultAuthContextValue.user, role: 'manager' },
    });
    
    render(<UserForm mode="create" />);
    
    const roleSelect = screen.getByLabelText(/role/i);
    expect(roleSelect).toBeDisabled();
    expect(screen.getByText(/only admins can change user roles/i)).toBeInTheDocument();
  });

  /**
   * Test: Status field is shown in edit mode for admins
   */
  it('shows status field in edit mode for admin users', () => {
    render(<UserForm mode="edit" user={mockUser} />);
    
    expect(screen.getByLabelText(/status/i)).toBeInTheDocument();
  });

  /**
   * Test: Password fields are not shown in edit mode
   */
  it('does not show password fields in edit mode', () => {
    render(<UserForm mode="edit" user={mockUser} />);
    
    expect(screen.queryByLabelText(/^password/i)).not.toBeInTheDocument();
    expect(screen.queryByLabelText(/confirm password/i)).not.toBeInTheDocument();
  });
});
