/**
 * Test script to check what the API is actually returning
 */

require('dotenv').config({ path: './backend/.env' });
const mysql = require('mysql2/promise');

async function testApiResponse() {
  console.log('🔍 Testing API response vs database state...\n');

  const connection = await mysql.createConnection({
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME
  });

  try {
    // Get a few users from database directly
    console.log('📋 Database state (first 5 users):');
    const [dbUsers] = await connection.execute(
      'SELECT id, email, first_name, last_name, is_active FROM users ORDER BY id LIMIT 5'
    );

    dbUsers.forEach(user => {
      console.log(`   DB: ${user.id} - ${user.first_name} ${user.last_name} - is_active: ${user.is_active} (type: ${typeof user.is_active})`);
    });

    console.log('\n🔄 Testing transformation function...');
    
    // Test the transformation function
    const transformUserObject = (user) => {
      if (!user) return null;
      
      return {
        id: user.id,
        email: user.email,
        firstName: user.first_name,
        lastName: user.last_name,
        department: user.department,
        role: user.role,
        employeeId: user.employee_id,
        phone: user.phone,
        isActive: Boolean(user.is_active), // Ensure boolean conversion
        emailVerified: Boolean(user.email_verified),
        biometricEnabled: Boolean(user.biometric_enabled),
        lastLogin: user.last_login,
        createdAt: user.created_at,
        updatedAt: user.updated_at
      };
    };

    console.log('🔄 Transformed users:');
    dbUsers.forEach(user => {
      const transformed = transformUserObject(user);
      console.log(`   API: ${transformed.id} - ${transformed.firstName} ${transformed.lastName} - isActive: ${transformed.isActive} (type: ${typeof transformed.isActive})`);
    });

    console.log('\n🧪 Testing status toggle on user 1...');
    
    // Get current status of user 1
    const [currentUser] = await connection.execute(
      'SELECT id, email, first_name, last_name, is_active FROM users WHERE id = 1'
    );
    
    if (currentUser.length > 0) {
      const user = currentUser[0];
      console.log(`   Current: ${user.first_name} ${user.last_name} - is_active: ${user.is_active}`);
      
      // Toggle status
      const newStatus = !user.is_active;
      await connection.execute(
        'UPDATE users SET is_active = ?, updated_at = NOW() WHERE id = 1',
        [newStatus]
      );
      
      console.log(`   Updated to: ${newStatus}`);
      
      // Check what it looks like after transformation
      const [updatedUser] = await connection.execute(
        'SELECT id, email, first_name, last_name, is_active FROM users WHERE id = 1'
      );
      
      const transformed = transformUserObject(updatedUser[0]);
      console.log(`   Transformed: isActive: ${transformed.isActive} (type: ${typeof transformed.isActive})`);
      
      // Restore original status
      await connection.execute(
        'UPDATE users SET is_active = ?, updated_at = NOW() WHERE id = 1',
        [user.is_active]
      );
      
      console.log(`   Restored to: ${user.is_active}`);
    }

    console.log('\n✅ API transformation test completed!');

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await connection.end();
  }
}

testApiResponse();
