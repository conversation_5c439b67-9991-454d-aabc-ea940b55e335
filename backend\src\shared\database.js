/**
 * Database connection and configuration module
 * Handles MySQL connection with connection pooling
 */

const mysql = require('mysql2/promise');
const { logger } = require('./logger');

let pool = null;

/**
 * Database configuration
 */
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT) || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'flexair_timekeeping',
  timezone: process.env.DB_TIMEZONE || '+08:00', // Philippines timezone (UTC+8)
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  acquireTimeout: 60000,
  idleTimeout: 300000,
  ssl: false
};

/**
 * Create database connection pool
 * @returns {Promise<mysql.Pool>}
 */
async function createConnection() {
  try {
    if (pool) {
      return pool;
    }

    pool = mysql.createPool(dbConfig);
    
    // Test connection
    const connection = await pool.getConnection();
    await connection.ping();
    connection.release();
    
    logger.info('MySQL connection pool created successfully');
    return pool;
  } catch (error) {
    logger.error('Failed to create MySQL connection pool:', error);
    throw error;
  }
}

/**
 * Get database connection from pool
 * @returns {Promise<mysql.PoolConnection>}
 */
async function getConnection() {
  if (!pool) {
    await createConnection();
  }
  return pool.getConnection();
}

/**
 * Execute a query with parameters
 * @param {string} sqlQuery - SQL query string
 * @param {Array} params - Query parameters
 * @returns {Promise<Array>} Query results
 */
async function executeQuery(sqlQuery, params = [], retries = 3) {
  for (let attempt = 1; attempt <= retries; attempt++) {
    let connection;
    try {
      connection = await getConnection();
      const [results] = await connection.execute(sqlQuery, params);
      return results;
    } catch (error) {
      logger.error(`Database query error (attempt ${attempt}/${retries}):`, error);

      // If it's a connection error and we have retries left, wait and retry
      if ((error.code === 'ECONNRESET' || error.code === 'PROTOCOL_CONNECTION_LOST' || error.code === 'ENOTFOUND') && attempt < retries) {
        logger.info(`Retrying database query in ${attempt * 1000}ms...`);
        await new Promise(resolve => setTimeout(resolve, attempt * 1000));
        continue;
      }

      throw error;
    } finally {
      if (connection) {
        connection.release();
      }
    }
  }
}

/**
 * Alias for executeQuery to match expected import name
 * @param {string} sqlQuery - SQL query string
 * @param {Array} params - Query parameters
 * @returns {Promise<Array>} Query results
 */
const query = executeQuery;

/**
 * Execute a transaction with multiple queries
 * @param {Function} callback - Function containing transaction logic
 * @returns {Promise<any>} Transaction result
 */
async function executeTransaction(callback) {
  const connection = await getConnection();
  try {
    await connection.beginTransaction();
    const result = await callback(connection);
    await connection.commit();
    return result;
  } catch (error) {
    await connection.rollback();
    logger.error('Transaction error:', error);
    throw error;
  } finally {
    connection.release();
  }
}

/**
 * Close database connection pool
 * @returns {Promise<void>}
 */
async function closeConnection() {
  if (pool) {
    await pool.end();
    pool = null;
    logger.info('Database connection pool closed');
  }
}

/**
 * Check database connection health
 * @returns {Promise<boolean>}
 */
async function healthCheck() {
  try {
    const connection = await getConnection();
    await connection.ping();
    connection.release();
    return true;
  } catch (error) {
    logger.error('Database health check failed:', error);
    return false;
  }
}

module.exports = {
  createConnection,
  getConnection,
  executeQuery,
  query,
  executeTransaction,
  closeConnection,
  healthCheck
};