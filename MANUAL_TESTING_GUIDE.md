# 🧪 Manual Testing Guide - Flexair Timekeeping Approval System

## 🚀 Quick Start

### Prerequisites
1. **Backend Server**: Running on `http://localhost:5000`
2. **Frontend Server**: Running on `http://localhost:3000`
3. **Database**: MySQL with approval_requests table created
4. **Test Users**: At least one employee and one manager account

### Starting the Application
```bash
# Terminal 1 - Backend
cd backend
npm run dev

# Terminal 2 - Frontend
cd frontend
npm run dev
```

## 👥 Test User Setup

### Required Test Accounts

**Manager Account:**
- Email: `<EMAIL>`
- Password: `password123`
- Role: `manager` or `admin`

**Employee Account:**
- Email: `<EMAIL>`
- Password: `password123`
- Role: `employee`

### Creating Test Users (if needed)
1. Navigate to: `http://localhost:3000/auth/register`
2. Create accounts with the above credentials
3. Manually update the database to set proper roles:
```sql
UPDATE users SET role = 'manager' WHERE email = '<EMAIL>';
UPDATE users SET role = 'employee' WHERE email = '<EMAIL>';
```

## 🎯 End-to-End Testing Scenarios

### Scenario 1: Employee Creates Time Correction Request

**Objective**: Test the complete flow of creating a time correction approval request

**Steps:**
1. **Login as Employee**
   - Go to: `http://localhost:3000/auth/login`
   - Email: `<EMAIL>`
   - Password: `password123`

2. **Navigate to Time Tracking**
   - Click "Time Tracking" in the navigation
   - URL should be: `http://localhost:3000/time-tracking`

3. **Create a Time Log Entry** (if none exist)
   - Click "Clock In" to create a session
   - Wait a few seconds, then "Clock Out"
   - Verify the time log appears in the table

4. **Request Time Correction**
   - Find a time log entry in the table
   - Click the "Request Approval" button
   - **Expected**: Approval request modal opens

5. **Fill Out Correction Form**
   - Request Type: Should default to "Correction"
   - Corrected Clock In Time: Change to a different time (e.g., 09:00)
   - Corrected Clock Out Time: Change to a different time (e.g., 17:30)
   - Reason: Enter "Forgot to clock in on time"
   - Click "Submit Request"

6. **Verify Success**
   - **Expected**: Success message appears
   - **Expected**: Modal closes
   - **Expected**: Time log shows "Approval Pending" status

**✅ Success Criteria:**
- [ ] Modal opens correctly
- [ ] Form validates required fields
- [ ] Submission succeeds
- [ ] UI updates to show pending status
- [ ] No console errors

### Scenario 2: Employee Creates Overtime Request

**Steps:**
1. **Access Approval Form**
   - From time tracking page, click "Request Approval" on any time log
   
2. **Fill Overtime Request**
   - Request Type: Select "Overtime"
   - Overtime Hours: Enter "2.5"
   - Overtime Date: Select today's date
   - Reason: Enter "Project deadline requires extra hours"
   - Click "Submit Request"

3. **Verify Creation**
   - **Expected**: Success message
   - **Expected**: Request appears in approval history

**✅ Success Criteria:**
- [ ] Overtime fields appear when type is selected
- [ ] Hours validation works (positive numbers only)
- [ ] Date picker functions correctly
- [ ] Submission creates request successfully

### Scenario 3: Employee Views Approval History

**Steps:**
1. **Navigate to Approvals Page**
   - Go to: `http://localhost:3000/approvals`
   - **Expected**: Shows "My Approval Requests" by default

2. **Verify Request Display**
   - **Expected**: Previously created requests appear
   - **Expected**: Status badges show correctly (Pending, Approved, Rejected)
   - **Expected**: Request details are visible

3. **Test Filtering**
   - Use status filter: Select "Pending"
   - **Expected**: Only pending requests show
   - Use type filter: Select "Correction"
   - **Expected**: Only correction requests show
   - Use date range filters
   - **Expected**: Results filter by date range

**✅ Success Criteria:**
- [ ] All requests display correctly
- [ ] Filters work as expected
- [ ] Pagination works (if >20 requests)
- [ ] Status badges have correct colors
- [ ] Request data formats properly

### Scenario 4: Manager Reviews Pending Approvals

**Objective**: Test manager approval workflow

**Steps:**
1. **Login as Manager**
   - Logout from employee account
   - Login with: `<EMAIL>` / `password123`

2. **Check Dashboard Notifications**
   - Go to: `http://localhost:3000/dashboard`
   - **Expected**: Notification badge shows pending approval count
   - **Expected**: Approval summary shows pending items

3. **Navigate to Manager Interface**
   - Go to: `http://localhost:3000/approvals`
   - **Expected**: Shows "Pending Approvals" interface
   - **Expected**: Lists all pending requests from employees

4. **Review Individual Request**
   - Find the time correction request created earlier
   - **Expected**: Shows employee name, request details, reason
   - **Expected**: Shows "Approve" and "Reject" buttons

**✅ Success Criteria:**
- [ ] Manager sees different interface than employee
- [ ] All pending requests are visible
- [ ] Employee information displays correctly
- [ ] Request details are complete and accurate

### Scenario 5: Manager Approves Request

**Steps:**
1. **Approve Individual Request**
   - Click "Approve" button on a time correction request
   - **Expected**: Approval modal opens
   - **Expected**: Shows request summary and employee info

2. **Add Comments and Approve**
   - Comments field: Enter "Approved - valid correction"
   - Click "Approve" button in modal
   - **Expected**: Success message appears
   - **Expected**: Request disappears from pending list

3. **Verify in History**
   - Switch to "All Approval Requests" view
   - **Expected**: Request shows as "Approved"
   - **Expected**: Shows approval date and manager name
   - **Expected**: Shows approval comments

**✅ Success Criteria:**
- [ ] Approval modal functions correctly
- [ ] Comments are saved and displayed
- [ ] Status updates immediately
- [ ] Audit trail is complete

### Scenario 6: Manager Rejects Request

**Steps:**
1. **Reject a Request**
   - Click "Reject" button on an overtime request
   - **Expected**: Rejection modal opens

2. **Add Rejection Reason**
   - Comments: Enter "Overtime not pre-approved by supervisor"
   - Click "Reject" button
   - **Expected**: Request is rejected successfully

3. **Verify Employee View**
   - Logout and login as employee
   - Go to approvals page
   - **Expected**: Request shows as "Rejected"
   - **Expected**: Rejection comments are visible

**✅ Success Criteria:**
- [ ] Rejection process works correctly
- [ ] Comments are required and saved
- [ ] Employee can see rejection reason
- [ ] Status updates across all views

### Scenario 7: Bulk Operations

**Steps:**
1. **Login as Manager**
   - Ensure multiple pending requests exist

2. **Select Multiple Requests**
   - Use checkboxes to select 2-3 requests
   - **Expected**: Bulk action buttons appear
   - **Expected**: Shows count of selected items

3. **Bulk Approve**
   - Click "Approve (X)" button
   - **Expected**: All selected requests are approved
   - **Expected**: Success message shows count

4. **Bulk Reject**
   - Select different requests
   - Click "Reject (X)" button
   - **Expected**: All selected requests are rejected

**✅ Success Criteria:**
- [ ] Selection mechanism works
- [ ] Bulk operations process all selected items
- [ ] Proper feedback is provided
- [ ] No partial failures occur

## 🔍 Advanced Testing Scenarios

### Error Handling Tests

**Test Invalid Data:**
1. Submit approval request with empty reason
   - **Expected**: Validation error appears
2. Submit overtime with negative hours
   - **Expected**: Validation prevents submission
3. Submit leave with end date before start date
   - **Expected**: Date validation error

**Test Network Errors:**
1. Disconnect internet during submission
   - **Expected**: Appropriate error message
2. Submit when backend is down
   - **Expected**: Connection error handling

### Performance Tests

**Large Data Sets:**
1. Create 50+ approval requests
   - **Expected**: Pagination works correctly
   - **Expected**: Filtering remains responsive
2. Test bulk operations with 20+ items
   - **Expected**: Operations complete successfully

### Mobile Responsiveness

**Test on Mobile Device:**
1. Access approval forms on mobile
   - **Expected**: Forms are usable on small screens
2. Test manager interface on tablet
   - **Expected**: Table scrolls horizontally if needed
3. Verify notification badges on mobile
   - **Expected**: Badges are visible and properly sized

## 📊 Verification Checklist

### Database Verification
After each test, verify in database:
```sql
-- Check approval requests
SELECT * FROM approval_requests ORDER BY created_at DESC;

-- Check request status distribution
SELECT status, COUNT(*) FROM approval_requests GROUP BY status;

-- Check manager assignments
SELECT ar.*, u.first_name, u.last_name 
FROM approval_requests ar 
JOIN users u ON ar.assigned_to = u.id;
```

### API Endpoint Testing
Use browser dev tools or Postman to verify:
- `GET /api/approvals/pending` returns correct data
- `POST /api/approvals` creates requests properly
- `PUT /api/approvals/:id` processes approvals correctly

### Frontend State Management
Check browser dev tools:
- No console errors during operations
- Network requests complete successfully
- Component state updates correctly
- Local storage/session handling works

## 🚨 Common Issues and Solutions

### Issue: "No pending approvals" despite creating requests
**Solution**: Check that manager user has proper role in database

### Issue: Approval request form doesn't submit
**Solution**: Verify backend server is running and API endpoints are accessible

### Issue: Notifications don't update
**Solution**: Check that the notification hook is refreshing properly

### Issue: Time logs don't show "Request Approval" button
**Solution**: Ensure user is logged in and time logs exist

## 🎯 Success Metrics

**Complete Success**: All scenarios pass without errors
**Partial Success**: Core functionality works with minor UI issues
**Failure**: Critical functionality broken or data corruption occurs

## 📞 Support Information

**Backend Logs**: Check terminal running `npm run dev` in backend folder
**Frontend Logs**: Check browser console (F12 → Console tab)
**Database Logs**: Check MySQL error logs if database issues occur

**API Documentation**: Available in backend/src/modules/approvals/
**Component Documentation**: Available in frontend/src/components/approvals/
