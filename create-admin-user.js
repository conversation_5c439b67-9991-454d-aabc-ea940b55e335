/**
 * Create an admin user for testing the user management interface
 */

require('dotenv').config({ path: './backend/.env' });
const mysql = require('mysql2/promise');
const bcrypt = require('bcrypt');

async function createAdminUser() {
  console.log('👑 Creating admin user for user management testing...\n');

  const connection = await mysql.createConnection({
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME
  });

  try {
    const email = '<EMAIL>';
    const password = 'AdminPass123!';
    const firstName = 'Admin';
    const lastName = 'User';
    const role = 'admin';

    // Check if admin user already exists
    const [existingUsers] = await connection.execute(
      'SELECT id, role FROM users WHERE email = ?',
      [email]
    );

    if (existingUsers.length > 0) {
      console.log('ℹ️ Admin user already exists, updating password and ensuring admin role...');
      const hashedPassword = await bcrypt.hash(password, 12);
      await connection.execute(
        'UPDATE users SET password_hash = ?, role = ?, is_active = 1 WHERE email = ?',
        [hashedPassword, role, email]
      );
      console.log('✅ Admin user updated!');
    } else {
      console.log('🆕 Creating new admin user...');
      const hashedPassword = await bcrypt.hash(password, 12);
      const [result] = await connection.execute(`
        INSERT INTO users (email, password_hash, first_name, last_name, role, is_active, email_verified, created_at)
        VALUES (?, ?, ?, ?, ?, 1, 1, NOW())
      `, [email, hashedPassword, firstName, lastName, role]);
      
      console.log('✅ Admin user created with ID:', result.insertId);
    }

    console.log('\n🎯 Admin User Credentials:');
    console.log(`   Email: ${email}`);
    console.log(`   Password: ${password}`);
    console.log(`   Role: ${role}`);
    console.log(`   Name: ${firstName} ${lastName}`);
    
    console.log('\n📋 How to test User Management:');
    console.log('1. Go to http://localhost:3000');
    console.log('2. Login with the admin credentials above');
    console.log('3. Click "User Management" on the dashboard');
    console.log('4. You can now create, edit, and manage users!');
    
    // Also create a few test users for demonstration
    console.log('\n👥 Creating sample users for testing...');
    
    const sampleUsers = [
      {
        email: '<EMAIL>',
        password: 'Manager123!',
        firstName: 'Jane',
        lastName: 'Manager',
        role: 'manager',
        department: 'Operations'
      },
      {
        email: '<EMAIL>',
        password: 'Employee123!',
        firstName: 'John',
        lastName: 'Employee',
        role: 'employee',
        department: 'Engineering'
      },
      {
        email: '<EMAIL>',
        password: 'Employee123!',
        firstName: 'Sarah',
        lastName: 'Smith',
        role: 'employee',
        department: 'Marketing'
      }
    ];

    for (const user of sampleUsers) {
      const [existing] = await connection.execute(
        'SELECT id FROM users WHERE email = ?',
        [user.email]
      );

      if (existing.length === 0) {
        const hashedPassword = await bcrypt.hash(user.password, 12);
        await connection.execute(`
          INSERT INTO users (email, password_hash, first_name, last_name, role, department, is_active, email_verified, created_at)
          VALUES (?, ?, ?, ?, ?, ?, 1, 1, NOW())
        `, [user.email, hashedPassword, user.firstName, user.lastName, user.role, user.department]);
        
        console.log(`   ✅ Created ${user.role}: ${user.firstName} ${user.lastName} (${user.email})`);
      } else {
        console.log(`   ℹ️ ${user.role} user ${user.email} already exists`);
      }
    }
    
    console.log('\n🎉 Setup complete! You now have:');
    console.log('   - 1 Admin user (can manage all users)');
    console.log('   - 1 Manager user (can view/edit users)');
    console.log('   - 2 Employee users (for testing)');
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await connection.end();
  }
}

createAdminUser();
