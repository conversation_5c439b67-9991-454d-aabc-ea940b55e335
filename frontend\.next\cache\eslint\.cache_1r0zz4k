[{"C:\\Users\\<USER>\\Workspace\\flexair_timekeeping_app\\frontend\\src\\app\\auth\\login\\page.tsx": "1", "C:\\Users\\<USER>\\Workspace\\flexair_timekeeping_app\\frontend\\src\\app\\auth\\register\\page.tsx": "2", "C:\\Users\\<USER>\\Workspace\\flexair_timekeeping_app\\frontend\\src\\app\\dashboard\\page.tsx": "3", "C:\\Users\\<USER>\\Workspace\\flexair_timekeeping_app\\frontend\\src\\app\\layout.tsx": "4", "C:\\Users\\<USER>\\Workspace\\flexair_timekeeping_app\\frontend\\src\\app\\page.tsx": "5", "C:\\Users\\<USER>\\Workspace\\flexair_timekeeping_app\\frontend\\src\\app\\time\\page.tsx": "6", "C:\\Users\\<USER>\\Workspace\\flexair_timekeeping_app\\frontend\\src\\components\\auth\\LoginForm.tsx": "7", "C:\\Users\\<USER>\\Workspace\\flexair_timekeeping_app\\frontend\\src\\components\\auth\\ProtectedRoute.tsx": "8", "C:\\Users\\<USER>\\Workspace\\flexair_timekeeping_app\\frontend\\src\\components\\auth\\RegisterForm.tsx": "9", "C:\\Users\\<USER>\\Workspace\\flexair_timekeeping_app\\frontend\\src\\components\\time\\ClockInOut.tsx": "10", "C:\\Users\\<USER>\\Workspace\\flexair_timekeeping_app\\frontend\\src\\components\\time\\TimeLogs.tsx": "11", "C:\\Users\\<USER>\\Workspace\\flexair_timekeeping_app\\frontend\\src\\contexts\\AuthContext.tsx": "12", "C:\\Users\\<USER>\\Workspace\\flexair_timekeeping_app\\frontend\\src\\lib\\auth.ts": "13", "C:\\Users\\<USER>\\Workspace\\flexair_timekeeping_app\\frontend\\src\\tests\\auth\\LoginForm.test.tsx": "14", "C:\\Users\\<USER>\\Workspace\\flexair_timekeeping_app\\frontend\\src\\tests\\auth\\RegisterForm.test.tsx": "15", "C:\\Users\\<USER>\\Workspace\\flexair_timekeeping_app\\frontend\\src\\tests\\time\\ClockInOut.test.tsx": "16", "C:\\Users\\<USER>\\Workspace\\flexair_timekeeping_app\\frontend\\src\\tests\\time\\TimeLogs.test.tsx": "17"}, {"size": 429, "mtime": 1750088588225, "results": "18", "hashOfConfig": "19"}, {"size": 460, "mtime": 1750088606662, "results": "20", "hashOfConfig": "19"}, {"size": 7032, "mtime": 1750126306225, "results": "21", "hashOfConfig": "19"}, {"size": 1249, "mtime": 1750088672913, "results": "22", "hashOfConfig": "19"}, {"size": 5969, "mtime": 1750088734483, "results": "23", "hashOfConfig": "19"}, {"size": 5748, "mtime": 1750175971868, "results": "24", "hashOfConfig": "19"}, {"size": 8519, "mtime": 1750088452139, "results": "25", "hashOfConfig": "19"}, {"size": 4255, "mtime": 1750088568263, "results": "26", "hashOfConfig": "19"}, {"size": 13193, "mtime": 1750088518229, "results": "27", "hashOfConfig": "19"}, {"size": 20340, "mtime": 1750167756133, "results": "28", "hashOfConfig": "19"}, {"size": 12943, "mtime": 1750167797146, "results": "29", "hashOfConfig": "19"}, {"size": 4681, "mtime": 1750088407282, "results": "30", "hashOfConfig": "19"}, {"size": 6385, "mtime": 1750175950796, "results": "31", "hashOfConfig": "19"}, {"size": 5964, "mtime": 1750088863178, "results": "32", "hashOfConfig": "19"}, {"size": 6097, "mtime": 1750088901461, "results": "33", "hashOfConfig": "19"}, {"size": 8878, "mtime": 1750126354553, "results": "34", "hashOfConfig": "19"}, {"size": 10053, "mtime": 1750126408631, "results": "35", "hashOfConfig": "19"}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "176ewc6", {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Workspace\\flexair_timekeeping_app\\frontend\\src\\app\\auth\\login\\page.tsx", [], [], "C:\\Users\\<USER>\\Workspace\\flexair_timekeeping_app\\frontend\\src\\app\\auth\\register\\page.tsx", [], [], "C:\\Users\\<USER>\\Workspace\\flexair_timekeeping_app\\frontend\\src\\app\\dashboard\\page.tsx", [], [], "C:\\Users\\<USER>\\Workspace\\flexair_timekeeping_app\\frontend\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Workspace\\flexair_timekeeping_app\\frontend\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Workspace\\flexair_timekeeping_app\\frontend\\src\\app\\time\\page.tsx", [], [], "C:\\Users\\<USER>\\Workspace\\flexair_timekeeping_app\\frontend\\src\\components\\auth\\LoginForm.tsx", ["87"], [], "C:\\Users\\<USER>\\Workspace\\flexair_timekeeping_app\\frontend\\src\\components\\auth\\ProtectedRoute.tsx", [], [], "C:\\Users\\<USER>\\Workspace\\flexair_timekeeping_app\\frontend\\src\\components\\auth\\RegisterForm.tsx", [], [], "C:\\Users\\<USER>\\Workspace\\flexair_timekeeping_app\\frontend\\src\\components\\time\\ClockInOut.tsx", [], [], "C:\\Users\\<USER>\\Workspace\\flexair_timekeeping_app\\frontend\\src\\components\\time\\TimeLogs.tsx", ["88"], [], "C:\\Users\\<USER>\\Workspace\\flexair_timekeeping_app\\frontend\\src\\contexts\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\Workspace\\flexair_timekeeping_app\\frontend\\src\\lib\\auth.ts", [], [], "C:\\Users\\<USER>\\Workspace\\flexair_timekeeping_app\\frontend\\src\\tests\\auth\\LoginForm.test.tsx", [], [], "C:\\Users\\<USER>\\Workspace\\flexair_timekeeping_app\\frontend\\src\\tests\\auth\\RegisterForm.test.tsx", [], [], "C:\\Users\\<USER>\\Workspace\\flexair_timekeeping_app\\frontend\\src\\tests\\time\\ClockInOut.test.tsx", [], [], "C:\\Users\\<USER>\\Workspace\\flexair_timekeeping_app\\frontend\\src\\tests\\time\\TimeLogs.test.tsx", [], [], {"ruleId": "89", "severity": 2, "message": "90", "line": 232, "column": 18, "nodeType": "91", "messageId": "92", "suggestions": "93"}, {"ruleId": "94", "severity": 1, "message": "95", "line": 64, "column": 6, "nodeType": "96", "endLine": 64, "endColumn": 47, "suggestions": "97"}, "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["98", "99", "100", "101"], "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadTimeLogs'. Either include it or remove the dependency array.", "ArrayExpression", ["102"], {"messageId": "103", "data": "104", "fix": "105", "desc": "106"}, {"messageId": "103", "data": "107", "fix": "108", "desc": "109"}, {"messageId": "103", "data": "110", "fix": "111", "desc": "112"}, {"messageId": "103", "data": "113", "fix": "114", "desc": "115"}, {"desc": "116", "fix": "117"}, "replaceWithAlt", {"alt": "118"}, {"range": "119", "text": "120"}, "Replace with `&apos;`.", {"alt": "121"}, {"range": "122", "text": "123"}, "Replace with `&lsquo;`.", {"alt": "124"}, {"range": "125", "text": "126"}, "Replace with `&#39;`.", {"alt": "127"}, {"range": "128", "text": "129"}, "Replace with `&rsquo;`.", "Update the dependencies array to be: [currentPage, startDate, endDate, status, loadTimeLogs]", {"range": "130", "text": "131"}, "&apos;", [8177, 8215], "\r\n              Don&apos;t have an account?", "&lsquo;", [8177, 8215], "\r\n              Don&lsquo;t have an account?", "&#39;", [8177, 8215], "\r\n              Don&#39;t have an account?", "&rsquo;", [8177, 8215], "\r\n              Don&rsquo;t have an account?", [1869, 1910], "[currentPage, startDate, endDate, status, loadTimeLogs]"]