/**
 * Approval workflow controller
 * Handles HTTP requests for approval operations
 */

const approvalService = require('./approvalService');
const { logger } = require('../../shared/logger');

/**
 * Create approval request
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const createApprovalRequest = async (req, res) => {
  try {
    const requestData = {
      ...req.body,
      requestedBy: req.user.id
    };

    const approval = await approvalService.createApprovalRequest(requestData);

    logger.info(`Approval request created by ${req.user.email} for time log ${requestData.timeLogId}`);

    res.status(201).json({
      success: true,
      message: 'Approval request created successfully',
      approval
    });
  } catch (error) {
    logger.error('Create approval request error:', error);
    
    const errorMessages = {
      'Time log not found': { code: 'TIME_LOG_NOT_FOUND', status: 404 },
      'Can only request approval for your own time logs': { code: 'ACCESS_DENIED', status: 403 },
      'There is already a pending approval for this time log': { code: 'APPROVAL_EXISTS', status: 409 },
      'No active managers available for approval': { code: 'NO_MANAGERS', status: 500 }
    };

    const errorInfo = errorMessages[error.message];
    if (errorInfo) {
      return res.status(errorInfo.status).json({
        error: error.message,
        code: errorInfo.code
      });
    }

    res.status(500).json({
      error: 'Failed to create approval request',
      code: 'APPROVAL_CREATE_ERROR'
    });
  }
};

/**
 * Process approval (approve/reject)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const processApproval = async (req, res) => {
  try {
    const { id } = req.params;
    const { status, comments } = req.body;
    const approverId = req.user.id;

    const approval = await approvalService.processApproval(
      parseInt(id),
      approverId,
      status,
      comments
    );

    logger.info(`Approval ${id} ${status} by ${req.user.email}`);

    res.json({
      success: true,
      message: `Approval request ${status} successfully`,
      approval
    });
  } catch (error) {
    logger.error('Process approval error:', error);
    
    const errorMessages = {
      'Approval request not found': { code: 'APPROVAL_NOT_FOUND', status: 404 },
      'Approval request has already been processed': { code: 'ALREADY_PROCESSED', status: 400 },
      'Only managers and admins can process approvals': { code: 'INSUFFICIENT_PERMISSIONS', status: 403 }
    };

    const errorInfo = errorMessages[error.message];
    if (errorInfo) {
      return res.status(errorInfo.status).json({
        error: error.message,
        code: errorInfo.code
      });
    }

    res.status(500).json({
      error: 'Failed to process approval',
      code: 'APPROVAL_PROCESS_ERROR'
    });
  }
};

/**
 * Get approval by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getApprovalById = async (req, res) => {
  try {
    const { id } = req.params;
    const approval = await approvalService.getApprovalById(parseInt(id));

    if (!approval) {
      return res.status(404).json({
        error: 'Approval not found',
        code: 'APPROVAL_NOT_FOUND'
      });
    }

    // Check if user can access this approval
    const canAccess = req.user.role === 'admin' || 
                     req.user.role === 'manager' ||
                     approval.requested_by === req.user.id ||
                     approval.assigned_to === req.user.id;

    if (!canAccess) {
      return res.status(403).json({
        error: 'Access denied',
        code: 'ACCESS_DENIED'
      });
    }

    res.json({
      success: true,
      approval
    });
  } catch (error) {
    logger.error('Get approval by ID error:', error);
    res.status(500).json({
      error: 'Failed to retrieve approval',
      code: 'APPROVAL_FETCH_ERROR'
    });
  }
};

/**
 * Get approvals with filtering and pagination
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getApprovals = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      status,
      requestType,
      requestedBy,
      assignedTo,
      startDate,
      endDate
    } = req.query;

    const filters = {};
    
    // Apply role-based filtering
    if (req.user.role === 'employee') {
      // Employees can only see their own requests
      filters.requestedBy = req.user.id;
    } else if (req.user.role === 'manager') {
      // Managers can see requests assigned to them or all requests
      if (assignedTo) {
        filters.assignedTo = parseInt(assignedTo);
      } else {
        filters.assignedTo = req.user.id;
      }
    }
    // Admins can see all approvals with any filters

    if (status) filters.status = status;
    if (requestType) filters.requestType = requestType;
    if (requestedBy && req.user.role === 'admin') filters.requestedBy = parseInt(requestedBy);
    if (startDate) filters.startDate = startDate;
    if (endDate) filters.endDate = endDate;

    const result = await approvalService.getApprovals(
      filters,
      parseInt(page),
      parseInt(limit)
    );

    res.json({
      success: true,
      ...result
    });
  } catch (error) {
    logger.error('Get approvals error:', error);
    res.status(500).json({
      error: 'Failed to retrieve approvals',
      code: 'APPROVALS_FETCH_ERROR'
    });
  }
};

/**
 * Get my approval requests
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getMyApprovalRequests = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      status,
      requestType
    } = req.query;

    const filters = {
      requestedBy: req.user.id
    };

    if (status) filters.status = status;
    if (requestType) filters.requestType = requestType;

    const result = await approvalService.getApprovals(
      filters,
      parseInt(page),
      parseInt(limit)
    );

    res.json({
      success: true,
      ...result
    });
  } catch (error) {
    logger.error('Get my approval requests error:', error);
    res.status(500).json({
      error: 'Failed to retrieve your approval requests',
      code: 'MY_APPROVALS_FETCH_ERROR'
    });
  }
};

/**
 * Get pending approvals for manager
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getPendingApprovals = async (req, res) => {
  try {
    const { page = 1, limit = 20 } = req.query;

    const filters = {
      status: 'pending'
    };

    // Managers see approvals assigned to them, admins see all
    if (req.user.role === 'manager') {
      filters.assignedTo = req.user.id;
    }

    const result = await approvalService.getApprovals(
      filters,
      parseInt(page),
      parseInt(limit)
    );

    res.json({
      success: true,
      ...result
    });
  } catch (error) {
    logger.error('Get pending approvals error:', error);
    res.status(500).json({
      error: 'Failed to retrieve pending approvals',
      code: 'PENDING_APPROVALS_ERROR'
    });
  }
};

/**
 * Get approval statistics
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getApprovalStats = async (req, res) => {
  try {
    const { startDate, endDate } = req.query;
    const filters = {};

    if (startDate) filters.startDate = startDate;
    if (endDate) filters.endDate = endDate;

    const stats = await approvalService.getApprovalStats(filters);

    res.json({
      success: true,
      stats
    });
  } catch (error) {
    logger.error('Get approval stats error:', error);
    res.status(500).json({
      error: 'Failed to retrieve approval statistics',
      code: 'APPROVAL_STATS_ERROR'
    });
  }
};

module.exports = {
  createApprovalRequest,
  processApproval,
  getApprovalById,
  getApprovals,
  getMyApprovalRequests,
  getPendingApprovals,
  getApprovalStats
};