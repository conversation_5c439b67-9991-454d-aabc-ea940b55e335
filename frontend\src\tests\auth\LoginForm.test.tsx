/**
 * LoginForm Component Tests
 * Tests login form functionality, validation, and user interactions
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { useRouter } from 'next/navigation';
import LoginForm from '@/components/auth/LoginForm';
import { useAuth } from '@/contexts/AuthContext';

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
}));

// Mock auth context
jest.mock('@/contexts/AuthContext', () => ({
  useAuth: jest.fn(),
}));

// Mock the auth context provider
const mockLogin = jest.fn();
const mockClearError = jest.fn();
const mockPush = jest.fn();

const defaultAuthContextValue = {
  login: mockLogin,
  loading: false,
  error: null,
  clearError: mockClearError,
  user: null,
  isAuthenticated: false,
  register: jest.fn(),
  logout: jest.fn(),
  refreshUser: jest.fn(),
};

describe('LoginForm', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockReturnValue({ push: mockPush });
    (useAuth as jest.Mock).mockReturnValue(defaultAuthContextValue);
  });

  /**
   * Test: Form renders correctly with all required fields
   */
  it('renders login form with required fields', () => {
    render(<LoginForm />);
    
    expect(screen.getByRole('heading', { name: /sign in to your account/i })).toBeInTheDocument();
    expect(screen.getByLabelText(/email address/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/password/i)).toBeInTheDocument();
    expect(screen.getByRole('checkbox', { name: /remember me/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument();
  });

  /**
   * Test: Email validation works correctly
   */
  it('validates email field correctly', async () => {
    const user = userEvent.setup();
    render(<LoginForm />);
    
    const emailField = screen.getByLabelText(/email address/i);
    const submitButton = screen.getByRole('button', { name: /sign in/i });
    
    // Test empty email
    await user.click(submitButton);
    expect(screen.getByText(/email is required/i)).toBeInTheDocument();
    
    // Test invalid email format
    await user.type(emailField, 'invalid-email');
    await user.click(submitButton);
    expect(screen.getByText(/please enter a valid email address/i)).toBeInTheDocument();
    
    // Test valid email
    await user.clear(emailField);
    await user.type(emailField, '<EMAIL>');
    await waitFor(() => {
      expect(screen.queryByText(/please enter a valid email address/i)).not.toBeInTheDocument();
    });
  });

  /**
   * Test: Password validation works correctly
   */
  it('validates password field correctly', async () => {
    const user = userEvent.setup();
    render(<LoginForm />);
    
    const passwordField = screen.getByLabelText(/password/i);
    const submitButton = screen.getByRole('button', { name: /sign in/i });
    
    // Test empty password
    await user.click(submitButton);
    expect(screen.getByText(/password is required/i)).toBeInTheDocument();
    
    // Test short password
    await user.type(passwordField, '123');
    await user.click(submitButton);
    expect(screen.getByText(/password must be at least 6 characters/i)).toBeInTheDocument();
  });

  /**
   * Test: Successful form submission
   */
  it('submits form with valid credentials', async () => {
    const user = userEvent.setup();
    render(<LoginForm />);
    
    const emailField = screen.getByLabelText(/email address/i);
    const passwordField = screen.getByLabelText(/password/i);
    const rememberMeField = screen.getByRole('checkbox', { name: /remember me/i });
    const submitButton = screen.getByRole('button', { name: /sign in/i });
    
    // Fill form with valid data
    await user.type(emailField, '<EMAIL>');
    await user.type(passwordField, 'password123');
    await user.click(rememberMeField);
    
    // Submit form
    await user.click(submitButton);
    
    expect(mockLogin).toHaveBeenCalledWith({
      email: '<EMAIL>',
      password: 'password123',
      rememberMe: true,
    });
  });

  /**
   * Test: Loading state is displayed correctly
   */
  it('shows loading state during submission', () => {
    (useAuth as jest.Mock).mockReturnValue({
      ...defaultAuthContextValue,
      loading: true,
    });
    
    render(<LoginForm />);
    
    expect(screen.getByText(/signing in.../i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /signing in.../i })).toBeDisabled();
  });

  /**
   * Test: Error message is displayed when login fails
   */
  it('displays error message when login fails', () => {
    (useAuth as jest.Mock).mockReturnValue({
      ...defaultAuthContextValue,
      error: 'Invalid credentials',
    });
    
    render(<LoginForm />);
    
    expect(screen.getByText(/invalid credentials/i)).toBeInTheDocument();
  });

  /**
   * Test: Error is cleared when user types in form
   */
  it('clears error when user types in form', async () => {
    const user = userEvent.setup();
    (useAuth as jest.Mock).mockReturnValue({
      ...defaultAuthContextValue,
      error: 'Invalid credentials',
    });
    
    render(<LoginForm />);
    
    const emailField = screen.getByLabelText(/email address/i);
    await user.type(emailField, 'a');
    
    expect(mockClearError).toHaveBeenCalled();
  });

  /**
   * Test: Navigation links are present
   */
  it('contains navigation links', () => {
    render(<LoginForm />);
    
    expect(screen.getByRole('link', { name: /forgot your password/i })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /sign up here/i })).toBeInTheDocument();
  });
});